#!/usr/bin/env python3
"""
<PERSON>rip<PERSON> to fix ChromeDriver compatibility issues.
"""

import os
import shutil
import subprocess
import sys
from pathlib import Path

def clear_chromedriver_cache():
    """Clear ChromeDriver cache."""
    print("🧹 Clearing ChromeDriver cache...")
    
    # Common cache locations
    cache_paths = [
        Path.home() / ".wdm",
        Path.home() / ".cache" / "selenium",
        Path(os.environ.get("USERPROFILE", "")) / ".wdm" if os.name == 'nt' else None
    ]
    
    for cache_path in cache_paths:
        if cache_path and cache_path.exists():
            try:
                shutil.rmtree(cache_path)
                print(f"✅ Cleared cache: {cache_path}")
            except Exception as e:
                print(f"⚠️  Could not clear {cache_path}: {e}")

def install_compatible_chromedriver():
    """Install compatible ChromeDriver."""
    print("📥 Installing compatible ChromeDriver...")
    
    try:
        # Update webdriver-manager to latest version
        subprocess.run([sys.executable, "-m", "pip", "install", "--upgrade", "webdriver-manager"], 
                      check=True, capture_output=True)
        print("✅ Updated webdriver-manager")
        
        # Try to install ChromeDriver
        from webdriver_manager.chrome import ChromeDriverManager
        driver_path = ChromeDriverManager().install()
        print(f"✅ ChromeDriver installed at: {driver_path}")
        return True
        
    except Exception as e:
        print(f"❌ Failed to install ChromeDriver: {e}")
        return False

def test_chromedriver():
    """Test if ChromeDriver works."""
    print("🧪 Testing ChromeDriver...")
    
    try:
        from selenium import webdriver
        from selenium.webdriver.chrome.service import Service
        from selenium.webdriver.chrome.options import Options
        from webdriver_manager.chrome import ChromeDriverManager
        
        options = Options()
        options.add_argument("--headless")
        options.add_argument("--no-sandbox")
        options.add_argument("--disable-dev-shm-usage")
        
        service = Service(ChromeDriverManager().install())
        driver = webdriver.Chrome(service=service, options=options)
        
        # Simple test
        driver.get("https://www.google.com")
        title = driver.title
        driver.quit()
        
        print(f"✅ ChromeDriver test successful! Page title: {title}")
        return True
        
    except Exception as e:
        print(f"❌ ChromeDriver test failed: {e}")
        return False

def main():
    """Main function."""
    print("🔧 ChromeDriver Fix Utility")
    print("=" * 40)
    
    # Step 1: Clear cache
    clear_chromedriver_cache()
    
    # Step 2: Install compatible driver
    if not install_compatible_chromedriver():
        print("\n💡 Manual steps to fix ChromeDriver:")
        print("1. Update Chrome browser to the latest version")
        print("2. Run: pip install --upgrade webdriver-manager selenium")
        print("3. Delete any manually installed ChromeDriver from system PATH")
        return False
    
    # Step 3: Test
    if test_chromedriver():
        print("\n🎉 ChromeDriver is now working correctly!")
        print("You can now run the lottery scraper.")
        return True
    else:
        print("\n❌ ChromeDriver still not working.")
        print("Please check your Chrome browser version and try again.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
