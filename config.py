"""
Configuration file for the lottery scraper.
"""

# Target URL
TARGET_URL = "https://wid8-baidu630.gabd11133ff.com/pages/historyRecord/lotteryRecord/index?route=aomen&"

# Output settings
OUTPUT_DIR = "output"
MAX_PAGES = 50  # Maximum number of pages to scrape (safety limit)

# WebDriver settings
HEADLESS_MODE = True  # Set to False to see browser window
IMPLICIT_WAIT = 10  # Seconds to wait for elements
PAGE_LOAD_TIMEOUT = 30  # Seconds to wait for page load

# Rate limiting
DELAY_BETWEEN_PAGES = 3  # Seconds to wait between page requests
RETRY_ATTEMPTS = 3  # Number of retry attempts for failed requests

# User agent string
USER_AGENT = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"

# Data extraction keywords (for identifying relevant content)
LOTTERY_KEYWORDS = ['期', '号', '开奖', '结果', '时间', 'lottery', 'result', 'draw', 'number']

# File formats to save
SAVE_CSV = True
SAVE_EXCEL = True
SAVE_JSON = True
