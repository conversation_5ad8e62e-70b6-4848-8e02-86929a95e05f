#!/usr/bin/env python3
"""
Web Interface for Lottery Data - Flask web application.
"""

from flask import Flask, render_template, jsonify, request, send_file
import json
import os
from datetime import datetime, timedelta
import base64
from io import BytesIO
import matplotlib
matplotlib.use('Agg')  # Use non-interactive backend
import matplotlib.pyplot as plt
import seaborn as sns
from database_manager import LotteryDatabaseManager
from data_analyzer import LotteryDataAnalyzer

app = Flask(__name__)
app.config['SECRET_KEY'] = 'lottery_scraper_2025'

# Initialize database manager
db_manager = LotteryDatabaseManager()

@app.route('/')
def index():
    """Main dashboard page."""
    try:
        # Get database info
        db_info = db_manager.get_database_info()
        
        # Get recent records
        recent_records = db_manager.get_all_records()
        if recent_records is not None:
            recent_records = recent_records.head(10).to_dict('records')
        else:
            recent_records = []
        
        return render_template('dashboard.html', 
                             db_info=db_info, 
                             recent_records=recent_records)
    except Exception as e:
        return f"Error loading dashboard: {e}", 500

@app.route('/api/statistics')
def api_statistics():
    """API endpoint for statistics data."""
    try:
        stats = {
            'numbers': db_manager.get_number_statistics().to_dict('records') if db_manager.get_number_statistics() is not None else [],
            'animals': db_manager.get_animal_statistics().to_dict('records') if db_manager.get_animal_statistics() is not None else [],
            'elements': db_manager.get_element_statistics().to_dict('records') if db_manager.get_element_statistics() is not None else []
        }
        return jsonify(stats)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/records')
def api_records():
    """API endpoint for lottery records."""
    try:
        page = int(request.args.get('page', 1))
        per_page = int(request.args.get('per_page', 20))
        
        all_records = db_manager.get_all_records()
        if all_records is not None:
            total = len(all_records)
            start = (page - 1) * per_page
            end = start + per_page
            records = all_records.iloc[start:end].to_dict('records')
            
            return jsonify({
                'records': records,
                'total': total,
                'page': page,
                'per_page': per_page,
                'total_pages': (total + per_page - 1) // per_page
            })
        else:
            return jsonify({'records': [], 'total': 0})
            
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/search')
def api_search():
    """API endpoint for searching records by number."""
    try:
        number = request.args.get('number')
        if not number:
            return jsonify({'error': 'Number parameter required'}), 400
        
        results = db_manager.search_by_number(int(number))
        if results is not None:
            return jsonify({'results': results.to_dict('records')})
        else:
            return jsonify({'results': []})
            
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/chart/<chart_type>')
def api_chart(chart_type):
    """API endpoint for generating charts."""
    try:
        plt.style.use('seaborn-v0_8')
        fig, ax = plt.subplots(figsize=(10, 6))
        
        if chart_type == 'number_frequency':
            stats = db_manager.get_number_statistics()
            if stats is not None and not stats.empty:
                top_20 = stats.head(20)
                bars = ax.bar(top_20['number'].astype(str), top_20['frequency'])
                ax.set_title('Top 20 Number Frequencies', fontsize=14, fontweight='bold')
                ax.set_xlabel('Numbers')
                ax.set_ylabel('Frequency')
                
                # Color bars
                for bar, freq in zip(bars, top_20['frequency']):
                    bar.set_color(plt.cm.viridis(freq / top_20['frequency'].max()))
        
        elif chart_type == 'animal_distribution':
            stats = db_manager.get_animal_statistics()
            if stats is not None and not stats.empty:
                ax.pie(stats['frequency'], labels=stats['animal'], autopct='%1.1f%%', startangle=90)
                ax.set_title('Animal Distribution', fontsize=14, fontweight='bold')
        
        elif chart_type == 'element_distribution':
            stats = db_manager.get_element_statistics()
            if stats is not None and not stats.empty:
                wedges, texts, autotexts = ax.pie(stats['frequency'], labels=stats['element'], 
                                                 autopct='%1.1f%%', startangle=90, pctdistance=0.85)
                # Create donut chart
                centre_circle = plt.Circle((0,0), 0.70, fc='white')
                fig.gca().add_artist(centre_circle)
                ax.set_title('Element Distribution', fontsize=14, fontweight='bold')
        
        else:
            return jsonify({'error': 'Invalid chart type'}), 400
        
        # Convert plot to base64 string
        img_buffer = BytesIO()
        plt.tight_layout()
        plt.savefig(img_buffer, format='png', dpi=150, bbox_inches='tight')
        img_buffer.seek(0)
        img_base64 = base64.b64encode(img_buffer.getvalue()).decode()
        plt.close()
        
        return jsonify({'image': f'data:image/png;base64,{img_base64}'})
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/statistics')
def statistics():
    """Statistics page."""
    return render_template('statistics.html')

@app.route('/records')
def records():
    """Records page."""
    return render_template('records.html')

@app.route('/analysis')
def analysis():
    """Analysis page."""
    return render_template('analysis.html')

@app.route('/api/analysis/run')
def api_run_analysis():
    """API endpoint to run comprehensive analysis."""
    try:
        # Export current database to CSV for analysis
        export_file = db_manager.export_to_csv()
        if not export_file:
            return jsonify({'error': 'Failed to export data for analysis'}), 500
        
        # Run analysis
        analyzer = LotteryDataAnalyzer()
        if analyzer.load_data(export_file):
            results = analyzer.generate_comprehensive_report()
            
            # Convert datetime objects to strings for JSON serialization
            def convert_datetime(obj):
                if hasattr(obj, 'isoformat'):
                    return obj.isoformat()
                return obj
            
            # Clean results for JSON serialization
            clean_results = {}
            for key, value in results.items():
                if isinstance(value, dict):
                    clean_results[key] = {k: convert_datetime(v) for k, v in value.items()}
                else:
                    clean_results[key] = convert_datetime(value)
            
            return jsonify({'success': True, 'results': clean_results})
        else:
            return jsonify({'error': 'Failed to load data for analysis'}), 500
            
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/scrape/status')
def api_scrape_status():
    """API endpoint to get scraping status."""
    # This would be implemented with actual scraping status tracking
    return jsonify({
        'status': 'idle',
        'last_scrape': '2025-01-04 18:19:55',
        'total_records': db_manager.get_database_info()['total_records'] if db_manager.get_database_info() else 0
    })

@app.route('/api/scrape/run')
def api_run_scrape():
    """API endpoint to trigger scraping (placeholder)."""
    # This would trigger the actual scraping process
    return jsonify({
        'success': True,
        'message': 'Scraping process would be triggered here',
        'status': 'started'
    })

def create_templates():
    """Create HTML templates if they don't exist."""
    templates_dir = 'templates'
    os.makedirs(templates_dir, exist_ok=True)
    
    # Base template
    base_template = '''<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Lottery Data Dashboard{% endblock %}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .sidebar { min-height: 100vh; background-color: #f8f9fa; }
        .main-content { padding: 20px; }
        .stat-card { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; }
        .chart-container { background: white; border-radius: 10px; padding: 20px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <nav class="col-md-2 sidebar">
                <div class="position-sticky pt-3">
                    <h5 class="text-center mb-4">🎲 Lottery Dashboard</h5>
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="/"><i class="fas fa-home"></i> Dashboard</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/records"><i class="fas fa-list"></i> Records</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/statistics"><i class="fas fa-chart-bar"></i> Statistics</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/analysis"><i class="fas fa-analytics"></i> Analysis</a>
                        </li>
                    </ul>
                </div>
            </nav>
            <main class="col-md-10 ms-sm-auto main-content">
                {% block content %}{% endblock %}
            </main>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    {% block scripts %}{% endblock %}
</body>
</html>'''
    
    with open(f'{templates_dir}/base.html', 'w', encoding='utf-8') as f:
        f.write(base_template)

def main():
    """Main function to run the web application."""
    print("🌐 Starting Lottery Data Web Interface...")
    
    # Create templates
    create_templates()
    
    # Create additional template files
    dashboard_template = '''{% extends "base.html" %}
{% block content %}
<h1>📊 Lottery Data Dashboard</h1>

{% if db_info %}
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card stat-card">
            <div class="card-body text-center">
                <h3>{{ db_info.total_records }}</h3>
                <p>Total Records</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card stat-card">
            <div class="card-body text-center">
                <h3>{{ db_info.total_numbers }}</h3>
                <p>Total Numbers</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card stat-card">
            <div class="card-body text-center">
                <h3>{{ db_info.unique_animals }}</h3>
                <p>Unique Animals</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card stat-card">
            <div class="card-body text-center">
                <h3>{{ db_info.unique_elements }}</h3>
                <p>Unique Elements</p>
            </div>
        </div>
    </div>
</div>
{% endif %}

<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5>Recent Records</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>Date</th>
                                <th>Period</th>
                                <th>Numbers</th>
                                <th>Total</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for record in recent_records %}
                            <tr>
                                <td>{{ record.date }}</td>
                                <td>{{ record.period }}</td>
                                <td>{{ record.raw_numbers }}</td>
                                <td>{{ record.total_numbers }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}'''
    
    with open('templates/dashboard.html', 'w', encoding='utf-8') as f:
        f.write(dashboard_template)

    # Statistics template
    statistics_template = '''{% extends "base.html" %}
{% block content %}
<h1>📈 Statistics</h1>
<div class="row">
    <div class="col-md-4">
        <div class="chart-container">
            <h5>Number Frequency</h5>
            <div id="numberChart"></div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="chart-container">
            <h5>Animal Distribution</h5>
            <div id="animalChart"></div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="chart-container">
            <h5>Element Distribution</h5>
            <div id="elementChart"></div>
        </div>
    </div>
</div>
{% endblock %}
{% block scripts %}
<script>
// Load charts
fetch('/api/chart/number_frequency')
    .then(response => response.json())
    .then(data => {
        if (data.image) {
            document.getElementById('numberChart').innerHTML = '<img src="' + data.image + '" class="img-fluid">';
        }
    });

fetch('/api/chart/animal_distribution')
    .then(response => response.json())
    .then(data => {
        if (data.image) {
            document.getElementById('animalChart').innerHTML = '<img src="' + data.image + '" class="img-fluid">';
        }
    });

fetch('/api/chart/element_distribution')
    .then(response => response.json())
    .then(data => {
        if (data.image) {
            document.getElementById('elementChart').innerHTML = '<img src="' + data.image + '" class="img-fluid">';
        }
    });
</script>
{% endblock %}'''

    with open('templates/statistics.html', 'w', encoding='utf-8') as f:
        f.write(statistics_template)

    # Records template
    records_template = '''{% extends "base.html" %}
{% block content %}
<h1>📋 Records</h1>
<div class="card">
    <div class="card-header">
        <div class="row">
            <div class="col-md-6">
                <h5>Lottery Records</h5>
            </div>
            <div class="col-md-6">
                <div class="input-group">
                    <input type="number" class="form-control" id="searchNumber" placeholder="Search by number">
                    <button class="btn btn-primary" onclick="searchByNumber()">Search</button>
                </div>
            </div>
        </div>
    </div>
    <div class="card-body">
        <div id="recordsTable"></div>
        <div id="searchResults" style="display:none;"></div>
    </div>
</div>
{% endblock %}
{% block scripts %}
<script>
let currentPage = 1;

function loadRecords(page = 1) {
    fetch(`/api/records?page=${page}&per_page=20`)
        .then(response => response.json())
        .then(data => {
            let html = '<div class="table-responsive"><table class="table table-striped"><thead><tr><th>Date</th><th>Period</th><th>Numbers</th><th>Total</th></tr></thead><tbody>';
            data.records.forEach(record => {
                html += `<tr><td>${record.date}</td><td>${record.period}</td><td>${record.raw_numbers}</td><td>${record.total_numbers}</td></tr>`;
            });
            html += '</tbody></table></div>';

            // Add pagination
            html += '<nav><ul class="pagination justify-content-center">';
            for (let i = 1; i <= data.total_pages; i++) {
                html += `<li class="page-item ${i === page ? 'active' : ''}"><a class="page-link" href="#" onclick="loadRecords(${i})">${i}</a></li>`;
            }
            html += '</ul></nav>';

            document.getElementById('recordsTable').innerHTML = html;
            currentPage = page;
        });
}

function searchByNumber() {
    const number = document.getElementById('searchNumber').value;
    if (!number) return;

    fetch(`/api/search?number=${number}`)
        .then(response => response.json())
        .then(data => {
            let html = '<h6>Search Results for Number ' + number + ':</h6>';
            html += '<div class="table-responsive"><table class="table table-striped"><thead><tr><th>Date</th><th>Period</th><th>Position</th><th>Animal</th><th>Element</th></tr></thead><tbody>';
            data.results.forEach(result => {
                html += `<tr><td>${result.date}</td><td>${result.period}</td><td>${result.position}</td><td>${result.animal}</td><td>${result.element}</td></tr>`;
            });
            html += '</tbody></table></div>';
            html += '<button class="btn btn-secondary" onclick="hideSearch()">Back to All Records</button>';

            document.getElementById('searchResults').innerHTML = html;
            document.getElementById('searchResults').style.display = 'block';
            document.getElementById('recordsTable').style.display = 'none';
        });
}

function hideSearch() {
    document.getElementById('searchResults').style.display = 'none';
    document.getElementById('recordsTable').style.display = 'block';
    document.getElementById('searchNumber').value = '';
}

// Load initial records
loadRecords();
</script>
{% endblock %}'''

    with open('templates/records.html', 'w', encoding='utf-8') as f:
        f.write(records_template)

    # Analysis template
    analysis_template = '''{% extends "base.html" %}
{% block content %}
<h1>🔍 Analysis</h1>
<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5>Comprehensive Analysis</h5>
                <button class="btn btn-primary" onclick="runAnalysis()">Run Analysis</button>
            </div>
            <div class="card-body">
                <div id="analysisResults"></div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
{% block scripts %}
<script>
function runAnalysis() {
    document.getElementById('analysisResults').innerHTML = '<div class="text-center"><div class="spinner-border" role="status"></div><p>Running analysis...</p></div>';

    fetch('/api/analysis/run')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                let html = '<div class="row">';

                // Basic Statistics
                if (data.results.basic_stats) {
                    html += '<div class="col-md-6"><div class="card mb-3"><div class="card-header"><h6>Basic Statistics</h6></div><div class="card-body">';
                    html += `<p><strong>Total Records:</strong> ${data.results.basic_stats.total_records}</p>`;
                    html += `<p><strong>Total Numbers:</strong> ${data.results.basic_stats.total_numbers}</p>`;
                    html += `<p><strong>Unique Numbers:</strong> ${data.results.basic_stats.unique_numbers}</p>`;
                    html += `<p><strong>Average Frequency:</strong> ${data.results.basic_stats.average_frequency}</p>`;
                    html += '</div></div></div>';
                }

                // Number Frequency
                if (data.results.number_frequency) {
                    html += '<div class="col-md-6"><div class="card mb-3"><div class="card-header"><h6>Top Numbers</h6></div><div class="card-body">';
                    data.results.number_frequency.most_common.forEach(item => {
                        html += `<p>Number ${item[0]}: ${item[1]} times</p>`;
                    });
                    html += '</div></div></div>';
                }

                html += '</div>';
                document.getElementById('analysisResults').innerHTML = html;
            } else {
                document.getElementById('analysisResults').innerHTML = '<div class="alert alert-danger">Error: ' + data.error + '</div>';
            }
        })
        .catch(error => {
            document.getElementById('analysisResults').innerHTML = '<div class="alert alert-danger">Error running analysis</div>';
        });
}
</script>
{% endblock %}'''

    with open('templates/analysis.html', 'w', encoding='utf-8') as f:
        f.write(analysis_template)

    print("✅ Templates created")
    print("🚀 Starting Flask server on http://localhost:5000")
    
    app.run(debug=True, host='0.0.0.0', port=5000)

if __name__ == "__main__":
    main()
