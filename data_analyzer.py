#!/usr/bin/env python3
"""
Lottery Data Analyzer - Advanced statistical analysis for scraped lottery data.
"""

import pandas as pd
import numpy as np
import json
import matplotlib.pyplot as plt
import matplotlib.patches as patches
import seaborn as sns
from collections import Counter, defaultdict
from datetime import datetime, timedelta
import os
from pathlib import Path

class LotteryDataAnalyzer:
    """Advanced analyzer for lottery data with statistical insights."""
    
    def __init__(self, data_file=None):
        """Initialize the analyzer with data file."""
        self.data = None
        self.analysis_results = {}
        
        if data_file:
            self.load_data(data_file)
    
    def load_data(self, data_file):
        """Load lottery data from various file formats."""
        try:
            file_path = Path(data_file)
            
            if file_path.suffix.lower() == '.csv':
                self.data = pd.read_csv(data_file)
            elif file_path.suffix.lower() in ['.xlsx', '.xls']:
                self.data = pd.read_excel(data_file)
            elif file_path.suffix.lower() == '.json':
                with open(data_file, 'r', encoding='utf-8') as f:
                    json_data = json.load(f)
                self.data = pd.DataFrame(json_data)
            else:
                raise ValueError(f"Unsupported file format: {file_path.suffix}")
            
            print(f"✅ Loaded {len(self.data)} records from {data_file}")
            return True
            
        except Exception as e:
            print(f"❌ Error loading data: {e}")
            return False
    
    def basic_statistics(self):
        """Generate basic statistics about the lottery data."""
        if self.data is None:
            print("❌ No data loaded")
            return None
        
        stats = {
            'total_records': len(self.data),
            'date_range': {
                'earliest': self.data['date'].min(),
                'latest': self.data['date'].max()
            },
            'total_numbers_drawn': 0,
            'unique_periods': self.data['period'].nunique() if 'period' in self.data.columns else 0
        }
        
        # Count total numbers drawn
        number_columns = [col for col in self.data.columns if col.startswith('number_')]
        stats['total_numbers_drawn'] = len(self.data) * len(number_columns)
        
        self.analysis_results['basic_stats'] = stats
        return stats
    
    def number_frequency_analysis(self):
        """Analyze frequency of lottery numbers."""
        if self.data is None:
            return None
        
        # Collect all numbers
        all_numbers = []
        number_columns = [col for col in self.data.columns if col.startswith('number_')]
        
        for _, row in self.data.iterrows():
            for col in number_columns:
                if pd.notna(row[col]):
                    all_numbers.append(int(row[col]))
        
        # Calculate frequency
        number_freq = Counter(all_numbers)
        
        # Create frequency analysis
        freq_analysis = {
            'most_common': number_freq.most_common(10),
            'least_common': number_freq.most_common()[-10:],
            'total_unique_numbers': len(number_freq),
            'average_frequency': np.mean(list(number_freq.values())),
            'frequency_distribution': dict(number_freq)
        }
        
        self.analysis_results['number_frequency'] = freq_analysis
        return freq_analysis
    
    def animal_element_analysis(self):
        """Analyze distribution of animals and elements."""
        if self.data is None:
            return None
        
        # Collect animals and elements
        animals = []
        elements = []
        
        animal_columns = [col for col in self.data.columns if col.startswith('animal_')]
        element_columns = [col for col in self.data.columns if col.startswith('element_')]
        
        for _, row in self.data.iterrows():
            for col in animal_columns:
                if pd.notna(row[col]):
                    animals.append(row[col])
            for col in element_columns:
                if pd.notna(row[col]):
                    elements.append(row[col])
        
        animal_freq = Counter(animals)
        element_freq = Counter(elements)
        
        analysis = {
            'animal_distribution': dict(animal_freq),
            'element_distribution': dict(element_freq),
            'most_common_animal': animal_freq.most_common(1)[0] if animal_freq else None,
            'most_common_element': element_freq.most_common(1)[0] if element_freq else None,
            'animal_balance': self._calculate_balance(animal_freq),
            'element_balance': self._calculate_balance(element_freq)
        }
        
        self.analysis_results['animal_element'] = analysis
        return analysis
    
    def _calculate_balance(self, counter):
        """Calculate how balanced a distribution is (0 = perfectly balanced, 1 = completely unbalanced)."""
        if not counter:
            return 0
        
        values = list(counter.values())
        if len(values) <= 1:
            return 0
        
        mean_val = np.mean(values)
        if mean_val == 0:
            return 0
        
        # Calculate coefficient of variation
        cv = np.std(values) / mean_val
        # Normalize to 0-1 scale (approximate)
        return min(cv / 2, 1)
    
    def temporal_analysis(self):
        """Analyze patterns over time."""
        if self.data is None:
            return None
        
        # Convert date column to datetime
        self.data['date'] = pd.to_datetime(self.data['date'])
        
        # Group by date
        daily_stats = self.data.groupby('date').agg({
            'period': 'count',
            'total_numbers': 'sum'
        }).rename(columns={'period': 'draws_per_day'})
        
        # Calculate trends
        analysis = {
            'total_days': len(daily_stats),
            'average_draws_per_day': daily_stats['draws_per_day'].mean(),
            'most_active_day': daily_stats['draws_per_day'].idxmax(),
            'least_active_day': daily_stats['draws_per_day'].idxmin(),
            'daily_statistics': daily_stats.to_dict('index')
        }
        
        self.analysis_results['temporal'] = analysis
        return analysis
    
    def pattern_analysis(self):
        """Analyze patterns in number sequences."""
        if self.data is None:
            return None
        
        patterns = {
            'consecutive_numbers': 0,
            'even_odd_distribution': {'even': 0, 'odd': 0},
            'sum_statistics': [],
            'range_statistics': []
        }
        
        number_columns = [col for col in self.data.columns if col.startswith('number_')]
        
        for _, row in self.data.iterrows():
            numbers = []
            for col in number_columns:
                if pd.notna(row[col]):
                    numbers.append(int(row[col]))
            
            if len(numbers) >= 2:
                # Check for consecutive numbers
                sorted_numbers = sorted(numbers)
                for i in range(len(sorted_numbers) - 1):
                    if sorted_numbers[i+1] - sorted_numbers[i] == 1:
                        patterns['consecutive_numbers'] += 1
                
                # Even/odd distribution
                for num in numbers:
                    if num % 2 == 0:
                        patterns['even_odd_distribution']['even'] += 1
                    else:
                        patterns['even_odd_distribution']['odd'] += 1
                
                # Sum and range statistics
                patterns['sum_statistics'].append(sum(numbers))
                patterns['range_statistics'].append(max(numbers) - min(numbers))
        
        # Calculate averages
        if patterns['sum_statistics']:
            patterns['average_sum'] = np.mean(patterns['sum_statistics'])
            patterns['average_range'] = np.mean(patterns['range_statistics'])
        
        self.analysis_results['patterns'] = patterns
        return patterns

    def create_visualizations(self, output_dir="output/charts"):
        """Create comprehensive data visualizations."""
        if self.data is None:
            print("❌ No data loaded for visualization")
            return False

        # Create output directory
        os.makedirs(output_dir, exist_ok=True)

        # Set style for better looking charts
        plt.style.use('seaborn-v0_8')
        sns.set_palette("husl")

        try:
            # 1. Number Frequency Chart
            self._create_number_frequency_chart(output_dir)

            # 2. Animal Distribution Chart
            self._create_animal_distribution_chart(output_dir)

            # 3. Element Distribution Chart
            self._create_element_distribution_chart(output_dir)

            # 4. Temporal Analysis Chart
            self._create_temporal_chart(output_dir)

            # 5. Even/Odd Distribution Chart
            self._create_even_odd_chart(output_dir)

            # 6. Number Range Distribution
            self._create_range_distribution_chart(output_dir)

            print(f"📊 All visualizations saved to: {output_dir}")
            return True

        except Exception as e:
            print(f"❌ Error creating visualizations: {e}")
            return False

    def _create_number_frequency_chart(self, output_dir):
        """Create number frequency bar chart."""
        if 'number_frequency' not in self.analysis_results:
            self.number_frequency_analysis()

        freq_data = self.analysis_results['number_frequency']['frequency_distribution']

        plt.figure(figsize=(15, 8))
        numbers = sorted(freq_data.keys())
        frequencies = [freq_data[num] for num in numbers]

        bars = plt.bar(numbers, frequencies, alpha=0.7, edgecolor='black', linewidth=0.5)

        # Color bars based on frequency
        max_freq = max(frequencies)
        for bar, freq in zip(bars, frequencies):
            bar.set_color(plt.cm.viridis(freq / max_freq))

        plt.title('彩票号码出现频率分析', fontsize=16, fontweight='bold', pad=20)
        plt.xlabel('号码', fontsize=12)
        plt.ylabel('出现次数', fontsize=12)
        plt.grid(axis='y', alpha=0.3)

        # Add value labels on bars
        for bar, freq in zip(bars, frequencies):
            plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.1,
                    str(freq), ha='center', va='bottom', fontsize=8)

        plt.tight_layout()
        plt.savefig(f"{output_dir}/number_frequency.png", dpi=300, bbox_inches='tight')
        plt.close()

    def _create_animal_distribution_chart(self, output_dir):
        """Create animal distribution pie chart."""
        if 'animal_element' not in self.analysis_results:
            self.animal_element_analysis()

        animal_data = self.analysis_results['animal_element']['animal_distribution']

        plt.figure(figsize=(12, 8))
        animals = list(animal_data.keys())
        counts = list(animal_data.values())

        # Create pie chart
        wedges, texts, autotexts = plt.pie(counts, labels=animals, autopct='%1.1f%%',
                                          startangle=90, textprops={'fontsize': 10})

        # Enhance appearance
        for autotext in autotexts:
            autotext.set_color('white')
            autotext.set_fontweight('bold')

        plt.title('生肖分布统计', fontsize=16, fontweight='bold', pad=20)
        plt.axis('equal')

        plt.tight_layout()
        plt.savefig(f"{output_dir}/animal_distribution.png", dpi=300, bbox_inches='tight')
        plt.close()

    def _create_element_distribution_chart(self, output_dir):
        """Create element distribution donut chart."""
        if 'animal_element' not in self.analysis_results:
            self.animal_element_analysis()

        element_data = self.analysis_results['animal_element']['element_distribution']

        plt.figure(figsize=(10, 8))
        elements = list(element_data.keys())
        counts = list(element_data.values())

        # Create donut chart
        wedges, texts, autotexts = plt.pie(counts, labels=elements, autopct='%1.1f%%',
                                          startangle=90, pctdistance=0.85,
                                          textprops={'fontsize': 12})

        # Create donut hole
        centre_circle = patches.Circle((0,0), 0.70, fc='white')
        fig = plt.gcf()
        fig.gca().add_artist(centre_circle)

        # Enhance appearance
        for autotext in autotexts:
            autotext.set_color('black')
            autotext.set_fontweight('bold')

        plt.title('五行元素分布统计', fontsize=16, fontweight='bold', pad=20)
        plt.axis('equal')

        plt.tight_layout()
        plt.savefig(f"{output_dir}/element_distribution.png", dpi=300, bbox_inches='tight')
        plt.close()

    def _create_temporal_chart(self, output_dir):
        """Create temporal analysis chart."""
        if 'temporal' not in self.analysis_results:
            self.temporal_analysis()

        # Convert date column to datetime if not already
        self.data['date'] = pd.to_datetime(self.data['date'])

        plt.figure(figsize=(14, 6))

        # Group by date and count records
        daily_counts = self.data.groupby('date').size()

        plt.plot(daily_counts.index, daily_counts.values, marker='o', linewidth=2, markersize=6)
        plt.fill_between(daily_counts.index, daily_counts.values, alpha=0.3)

        plt.title('彩票开奖时间趋势分析', fontsize=16, fontweight='bold', pad=20)
        plt.xlabel('日期', fontsize=12)
        plt.ylabel('开奖次数', fontsize=12)
        plt.grid(True, alpha=0.3)

        # Rotate x-axis labels for better readability
        plt.xticks(rotation=45)

        plt.tight_layout()
        plt.savefig(f"{output_dir}/temporal_analysis.png", dpi=300, bbox_inches='tight')
        plt.close()

    def _create_even_odd_chart(self, output_dir):
        """Create even/odd distribution chart."""
        if 'patterns' not in self.analysis_results:
            self.pattern_analysis()

        even_odd_data = self.analysis_results['patterns']['even_odd_distribution']

        plt.figure(figsize=(8, 6))

        categories = ['偶数', '奇数']
        values = [even_odd_data['even'], even_odd_data['odd']]
        colors = ['#FF6B6B', '#4ECDC4']

        bars = plt.bar(categories, values, color=colors, alpha=0.8, edgecolor='black', linewidth=1)

        # Add value labels
        for bar, value in zip(bars, values):
            plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 1,
                    str(value), ha='center', va='bottom', fontsize=14, fontweight='bold')

        plt.title('奇偶数分布统计', fontsize=16, fontweight='bold', pad=20)
        plt.ylabel('出现次数', fontsize=12)
        plt.grid(axis='y', alpha=0.3)

        plt.tight_layout()
        plt.savefig(f"{output_dir}/even_odd_distribution.png", dpi=300, bbox_inches='tight')
        plt.close()

    def _create_range_distribution_chart(self, output_dir):
        """Create number range distribution histogram."""
        if 'patterns' not in self.analysis_results:
            self.pattern_analysis()

        range_data = self.analysis_results['patterns']['range_statistics']

        plt.figure(figsize=(10, 6))

        plt.hist(range_data, bins=10, alpha=0.7, color='skyblue', edgecolor='black', linewidth=1)

        plt.title('号码范围分布直方图', fontsize=16, fontweight='bold', pad=20)
        plt.xlabel('号码范围 (最大值 - 最小值)', fontsize=12)
        plt.ylabel('频次', fontsize=12)
        plt.grid(axis='y', alpha=0.3)

        # Add statistics text
        mean_range = np.mean(range_data)
        plt.axvline(mean_range, color='red', linestyle='--', linewidth=2, label=f'平均范围: {mean_range:.1f}')
        plt.legend()

        plt.tight_layout()
        plt.savefig(f"{output_dir}/range_distribution.png", dpi=300, bbox_inches='tight')
        plt.close()
    
    def generate_comprehensive_report(self):
        """Generate a comprehensive analysis report."""
        print("🔍 Generating Comprehensive Lottery Data Analysis Report")
        print("=" * 60)
        
        # Run all analyses
        basic = self.basic_statistics()
        frequency = self.number_frequency_analysis()
        animal_element = self.animal_element_analysis()
        temporal = self.temporal_analysis()
        patterns = self.pattern_analysis()
        
        # Print report
        if basic:
            print(f"\n📊 BASIC STATISTICS")
            print(f"Total Records: {basic['total_records']}")
            print(f"Date Range: {basic['date_range']['earliest']} to {basic['date_range']['latest']}")
            print(f"Total Numbers Drawn: {basic['total_numbers_drawn']}")
            print(f"Unique Periods: {basic['unique_periods']}")
        
        if frequency:
            print(f"\n🔢 NUMBER FREQUENCY ANALYSIS")
            print(f"Most Common Numbers: {frequency['most_common'][:5]}")
            print(f"Least Common Numbers: {frequency['least_common'][:5]}")
            print(f"Total Unique Numbers: {frequency['total_unique_numbers']}")
            print(f"Average Frequency: {frequency['average_frequency']:.2f}")
        
        if animal_element:
            print(f"\n🐲 ANIMAL & ELEMENT ANALYSIS")
            if animal_element['most_common_animal']:
                print(f"Most Common Animal: {animal_element['most_common_animal'][0]} ({animal_element['most_common_animal'][1]} times)")
            if animal_element['most_common_element']:
                print(f"Most Common Element: {animal_element['most_common_element'][0]} ({animal_element['most_common_element'][1]} times)")
            print(f"Animal Distribution Balance: {animal_element['animal_balance']:.3f}")
            print(f"Element Distribution Balance: {animal_element['element_balance']:.3f}")
        
        if temporal:
            print(f"\n📅 TEMPORAL ANALYSIS")
            print(f"Total Days Analyzed: {temporal['total_days']}")
            print(f"Average Draws per Day: {temporal['average_draws_per_day']:.2f}")
            print(f"Most Active Day: {temporal['most_active_day']}")
        
        if patterns:
            print(f"\n🔍 PATTERN ANALYSIS")
            print(f"Consecutive Number Occurrences: {patterns['consecutive_numbers']}")
            print(f"Even Numbers: {patterns['even_odd_distribution']['even']}")
            print(f"Odd Numbers: {patterns['even_odd_distribution']['odd']}")
            if 'average_sum' in patterns:
                print(f"Average Sum: {patterns['average_sum']:.2f}")
                print(f"Average Range: {patterns['average_range']:.2f}")
        
        return self.analysis_results
    
    def save_analysis_report(self, output_file=None):
        """Save analysis results to file."""
        if not output_file:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = f"output/analysis_report_{timestamp}.json"
        
        os.makedirs(os.path.dirname(output_file), exist_ok=True)
        
        try:
            # Convert any datetime objects to strings for JSON serialization
            def json_serializer(obj):
                if hasattr(obj, 'isoformat'):
                    return obj.isoformat()
                elif hasattr(obj, '__dict__'):
                    return str(obj)
                return str(obj)

            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(self.analysis_results, f, ensure_ascii=False, indent=2, default=json_serializer)

            print(f"📄 Analysis report saved to: {output_file}")
            return output_file

        except Exception as e:
            print(f"❌ Error saving report: {e}")
            return None

def main():
    """Main function for command-line usage."""
    import sys
    
    if len(sys.argv) < 2:
        print("Usage: python data_analyzer.py <data_file>")
        print("Example: python data_analyzer.py output/lottery_data_20250704_181955.csv")
        return
    
    data_file = sys.argv[1]
    
    # Create analyzer and run analysis
    analyzer = LotteryDataAnalyzer(data_file)
    
    if analyzer.data is not None:
        # Generate comprehensive report
        results = analyzer.generate_comprehensive_report()

        # Create visualizations
        print(f"\n📊 Creating data visualizations...")
        analyzer.create_visualizations()

        # Save report
        analyzer.save_analysis_report()

        print(f"\n✅ Analysis completed successfully!")
        print(f"📁 Check the 'output' directory for:")
        print(f"   - Analysis report (JSON)")
        print(f"   - Data visualizations (PNG charts)")
    else:
        print("❌ Failed to load data file")

if __name__ == "__main__":
    main()
