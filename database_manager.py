#!/usr/bin/env python3
"""
Database Manager for Lottery Data - SQLite database operations.
"""

import sqlite3
import pandas as pd
import json
import os
from datetime import datetime
from pathlib import Path

class LotteryDatabaseManager:
    """Manages SQLite database operations for lottery data."""
    
    def __init__(self, db_path="output/lottery_data.db"):
        """Initialize database manager."""
        self.db_path = db_path
        self.ensure_database_directory()
        self.init_database()
    
    def ensure_database_directory(self):
        """Ensure the database directory exists."""
        db_dir = os.path.dirname(self.db_path)
        if db_dir:
            os.makedirs(db_dir, exist_ok=True)
    
    def init_database(self):
        """Initialize database with required tables."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # Create lottery_records table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS lottery_records (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        date TEXT NOT NULL,
                        period TEXT NOT NULL,
                        raw_numbers TEXT,
                        total_numbers INTEGER,
                        extraction_time TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        UNIQUE(date, period)
                    )
                ''')
                
                # Create lottery_numbers table for individual numbers
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS lottery_numbers (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        record_id INTEGER,
                        position INTEGER,
                        number INTEGER,
                        animal TEXT,
                        element TEXT,
                        combination TEXT,
                        FOREIGN KEY (record_id) REFERENCES lottery_records (id)
                    )
                ''')
                
                # Create indexes for better performance
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_date ON lottery_records(date)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_period ON lottery_records(period)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_number ON lottery_numbers(number)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_animal ON lottery_numbers(animal)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_element ON lottery_numbers(element)')
                
                conn.commit()
                print("✅ Database initialized successfully")
                
        except Exception as e:
            print(f"❌ Error initializing database: {e}")
    
    def insert_lottery_data(self, data_file):
        """Insert lottery data from file into database."""
        try:
            # Load data
            if isinstance(data_file, str):
                if data_file.endswith('.csv'):
                    df = pd.read_csv(data_file)
                elif data_file.endswith('.json'):
                    with open(data_file, 'r', encoding='utf-8') as f:
                        json_data = json.load(f)
                    df = pd.DataFrame(json_data)
                else:
                    raise ValueError(f"Unsupported file format: {data_file}")
            else:
                df = data_file  # Assume it's already a DataFrame
            
            inserted_count = 0
            updated_count = 0
            
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                for _, row in df.iterrows():
                    # Check if record already exists
                    cursor.execute(
                        'SELECT id FROM lottery_records WHERE date = ? AND period = ?',
                        (row['date'], row['period'])
                    )
                    existing_record = cursor.fetchone()
                    
                    if existing_record:
                        # Update existing record
                        record_id = existing_record[0]
                        cursor.execute('''
                            UPDATE lottery_records 
                            SET raw_numbers = ?, total_numbers = ?, extraction_time = ?
                            WHERE id = ?
                        ''', (row['raw_numbers'], row['total_numbers'], row['extraction_time'], record_id))
                        
                        # Delete existing numbers for this record
                        cursor.execute('DELETE FROM lottery_numbers WHERE record_id = ?', (record_id,))
                        updated_count += 1
                    else:
                        # Insert new record
                        cursor.execute('''
                            INSERT INTO lottery_records (date, period, raw_numbers, total_numbers, extraction_time)
                            VALUES (?, ?, ?, ?, ?)
                        ''', (row['date'], row['period'], row['raw_numbers'], row['total_numbers'], row['extraction_time']))
                        
                        record_id = cursor.lastrowid
                        inserted_count += 1
                    
                    # Insert individual numbers
                    for i in range(1, 8):  # Up to 7 numbers
                        number_col = f'number_{i}'
                        animal_col = f'animal_{i}'
                        element_col = f'element_{i}'
                        combination_col = f'combination_{i}'
                        
                        if number_col in row and pd.notna(row[number_col]):
                            cursor.execute('''
                                INSERT INTO lottery_numbers (record_id, position, number, animal, element, combination)
                                VALUES (?, ?, ?, ?, ?, ?)
                            ''', (
                                record_id, i, int(float(row[number_col])),
                                str(row.get(animal_col, '')), str(row.get(element_col, '')),
                                str(row.get(combination_col, ''))
                            ))
                
                conn.commit()
            
            print(f"✅ Database updated: {inserted_count} new records, {updated_count} updated records")
            return True

        except Exception as e:
            print(f"❌ Error inserting data: {e}")
            return False

    def insert_lottery_data_with_validation(self, data_file, validate=True, remove_duplicates=True):
        """Insert lottery data with validation and deduplication."""
        try:
            # Load data
            if isinstance(data_file, str):
                if data_file.endswith('.csv'):
                    df = pd.read_csv(data_file)
                elif data_file.endswith('.json'):
                    with open(data_file, 'r', encoding='utf-8') as f:
                        json_data = json.load(f)
                    df = pd.DataFrame(json_data)
                else:
                    raise ValueError(f"Unsupported file format: {data_file}")
            else:
                df = data_file  # Assume it's already a DataFrame

            print(f"📥 Processing {len(df)} records from {data_file if isinstance(data_file, str) else 'DataFrame'}")

            # Validate data if requested
            if validate:
                try:
                    from data_validator import LotteryDataValidator
                    validator = LotteryDataValidator()

                    validation_results = validator.validate_dataset(df)
                    print(f"🔍 Validation: {validation_results['valid_records']}/{validation_results['total_records']} valid ({validation_results['validation_rate']:.1f}%)")

                    # Filter out invalid records
                    valid_indices = [r['index'] for r in validation_results['results'] if r['valid']]
                    df = df.iloc[valid_indices].reset_index(drop=True)

                    if len(df) == 0:
                        print("⚠️ No valid records to insert")
                        return False
                except ImportError:
                    print("⚠️ Data validator not available, skipping validation")

            # Remove duplicates if requested
            if remove_duplicates:
                try:
                    from data_validator import LotteryDataValidator
                    validator = LotteryDataValidator()

                    # Check for duplicates with existing data
                    existing_data = self.export_to_dataframe()
                    if existing_data is not None and len(existing_data) > 0:
                        # Combine with existing data to check for duplicates
                        combined_df = pd.concat([existing_data, df], ignore_index=True)
                        clean_df, stats = validator.remove_duplicates(combined_df)

                        # Get only the new records (those not in existing data)
                        new_records_count = len(clean_df) - len(existing_data)
                        if new_records_count > 0:
                            df = clean_df.tail(new_records_count).reset_index(drop=True)
                            print(f"🧹 After deduplication: {len(df)} new records to insert")
                        else:
                            print("ℹ️ No new records after deduplication")
                            return True
                    else:
                        # No existing data, just remove duplicates within the new data
                        df, stats = validator.remove_duplicates(df)
                        print(f"🧹 Removed {stats['removed_count']} duplicates from new data")
                except ImportError:
                    print("⚠️ Data validator not available, skipping deduplication")

            # Use the existing insert method
            return self.insert_lottery_data(df)

        except Exception as e:
            print(f"❌ Error in validated insertion: {e}")
            return False

    def export_to_dataframe(self):
        """Export all lottery data to pandas DataFrame."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                query = '''
                    SELECT
                        lr.date, lr.period, lr.raw_numbers, lr.total_numbers, lr.extraction_time,
                        ln.position, ln.number, ln.animal, ln.element, ln.combination
                    FROM lottery_records lr
                    LEFT JOIN lottery_numbers ln ON lr.id = ln.record_id
                    ORDER BY lr.date DESC, lr.period DESC, ln.position
                '''

                df = pd.read_sql_query(query, conn)

                if len(df) == 0:
                    return None

                # Pivot the data to have one row per record
                pivot_data = []
                for (date, period), group in df.groupby(['date', 'period']):
                    record = {
                        'date': date,
                        'period': period,
                        'raw_numbers': group.iloc[0]['raw_numbers'],
                        'total_numbers': group.iloc[0]['total_numbers'],
                        'extraction_time': group.iloc[0]['extraction_time']
                    }

                    # Add individual numbers
                    for _, row in group.iterrows():
                        if pd.notna(row['position']):
                            pos = int(row['position'])
                            record[f'number_{pos}'] = row['number']
                            record[f'animal_{pos}'] = row['animal']
                            record[f'element_{pos}'] = row['element']
                            record[f'combination_{pos}'] = row['combination']

                    pivot_data.append(record)

                return pd.DataFrame(pivot_data)

        except Exception as e:
            print(f"❌ Error exporting to DataFrame: {e}")
            return None
    
    def get_all_records(self):
        """Get all lottery records from database."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                query = '''
                    SELECT lr.*, 
                           GROUP_CONCAT(ln.number || '|' || ln.animal || '|' || ln.element, ';') as numbers_detail
                    FROM lottery_records lr
                    LEFT JOIN lottery_numbers ln ON lr.id = ln.record_id
                    GROUP BY lr.id
                    ORDER BY lr.date DESC, lr.period DESC
                '''
                df = pd.read_sql_query(query, conn)
                return df
                
        except Exception as e:
            print(f"❌ Error retrieving records: {e}")
            return None
    
    def get_records_by_date_range(self, start_date, end_date):
        """Get records within a date range."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                query = '''
                    SELECT lr.*, 
                           GROUP_CONCAT(ln.number || '|' || ln.animal || '|' || ln.element, ';') as numbers_detail
                    FROM lottery_records lr
                    LEFT JOIN lottery_numbers ln ON lr.id = ln.record_id
                    WHERE lr.date BETWEEN ? AND ?
                    GROUP BY lr.id
                    ORDER BY lr.date DESC, lr.period DESC
                '''
                df = pd.read_sql_query(query, conn, params=(start_date, end_date))
                return df
                
        except Exception as e:
            print(f"❌ Error retrieving records by date range: {e}")
            return None
    
    def get_number_statistics(self):
        """Get number frequency statistics."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                query = '''
                    SELECT number, COUNT(*) as frequency
                    FROM lottery_numbers
                    GROUP BY number
                    ORDER BY frequency DESC, number ASC
                '''
                df = pd.read_sql_query(query, conn)
                return df
                
        except Exception as e:
            print(f"❌ Error retrieving number statistics: {e}")
            return None
    
    def get_animal_statistics(self):
        """Get animal frequency statistics."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                query = '''
                    SELECT animal, COUNT(*) as frequency
                    FROM lottery_numbers
                    WHERE animal != ''
                    GROUP BY animal
                    ORDER BY frequency DESC
                '''
                df = pd.read_sql_query(query, conn)
                return df
                
        except Exception as e:
            print(f"❌ Error retrieving animal statistics: {e}")
            return None
    
    def get_element_statistics(self):
        """Get element frequency statistics."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                query = '''
                    SELECT element, COUNT(*) as frequency
                    FROM lottery_numbers
                    WHERE element != ''
                    GROUP BY element
                    ORDER BY frequency DESC
                '''
                df = pd.read_sql_query(query, conn)
                return df
                
        except Exception as e:
            print(f"❌ Error retrieving element statistics: {e}")
            return None
    
    def search_by_number(self, number):
        """Search records containing a specific number."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                query = '''
                    SELECT DISTINCT lr.date, lr.period, lr.raw_numbers,
                           ln.position, ln.animal, ln.element, ln.combination
                    FROM lottery_records lr
                    JOIN lottery_numbers ln ON lr.id = ln.record_id
                    WHERE ln.number = ?
                    ORDER BY lr.date DESC, lr.period DESC
                '''
                df = pd.read_sql_query(query, conn, params=(number,))
                return df
                
        except Exception as e:
            print(f"❌ Error searching by number: {e}")
            return None
    
    def get_database_info(self):
        """Get database information and statistics."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # Get record count
                cursor.execute('SELECT COUNT(*) FROM lottery_records')
                record_count = cursor.fetchone()[0]
                
                # Get number count
                cursor.execute('SELECT COUNT(*) FROM lottery_numbers')
                number_count = cursor.fetchone()[0]
                
                # Get date range
                cursor.execute('SELECT MIN(date), MAX(date) FROM lottery_records')
                date_range = cursor.fetchone()
                
                # Get unique animals and elements
                cursor.execute('SELECT COUNT(DISTINCT animal) FROM lottery_numbers WHERE animal != ""')
                unique_animals = cursor.fetchone()[0]
                
                cursor.execute('SELECT COUNT(DISTINCT element) FROM lottery_numbers WHERE element != ""')
                unique_elements = cursor.fetchone()[0]
                
                info = {
                    'database_path': self.db_path,
                    'total_records': record_count,
                    'total_numbers': number_count,
                    'date_range': {
                        'earliest': date_range[0],
                        'latest': date_range[1]
                    },
                    'unique_animals': unique_animals,
                    'unique_elements': unique_elements,
                    'database_size': os.path.getsize(self.db_path) if os.path.exists(self.db_path) else 0
                }
                
                return info
                
        except Exception as e:
            print(f"❌ Error getting database info: {e}")
            return None
    
    def export_to_csv(self, output_file=None):
        """Export all data to CSV file."""
        if not output_file:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = f"output/database_export_{timestamp}.csv"
        
        try:
            df = self.get_all_records()
            if df is not None:
                df.to_csv(output_file, index=False)
                print(f"✅ Data exported to: {output_file}")
                return output_file
            else:
                print("❌ No data to export")
                return None
                
        except Exception as e:
            print(f"❌ Error exporting data: {e}")
            return None

def main():
    """Main function for command-line usage."""
    import sys
    
    db_manager = LotteryDatabaseManager()
    
    if len(sys.argv) < 2:
        # Show database info
        info = db_manager.get_database_info()
        if info:
            print("📊 Database Information")
            print("=" * 40)
            print(f"Database Path: {info['database_path']}")
            print(f"Total Records: {info['total_records']}")
            print(f"Total Numbers: {info['total_numbers']}")
            print(f"Date Range: {info['date_range']['earliest']} to {info['date_range']['latest']}")
            print(f"Unique Animals: {info['unique_animals']}")
            print(f"Unique Elements: {info['unique_elements']}")
            print(f"Database Size: {info['database_size']} bytes")
        return
    
    command = sys.argv[1]
    
    if command == "import" and len(sys.argv) > 2:
        data_file = sys.argv[2]
        print(f"📥 Importing data from: {data_file}")
        db_manager.insert_lottery_data(data_file)
        
    elif command == "export":
        output_file = sys.argv[2] if len(sys.argv) > 2 else None
        print("📤 Exporting data...")
        db_manager.export_to_csv(output_file)
        
    elif command == "stats":
        print("📊 Number Statistics:")
        number_stats = db_manager.get_number_statistics()
        if number_stats is not None:
            print(number_stats.head(10))
        
        print("\n🐲 Animal Statistics:")
        animal_stats = db_manager.get_animal_statistics()
        if animal_stats is not None:
            print(animal_stats)
            
        print("\n🔥 Element Statistics:")
        element_stats = db_manager.get_element_statistics()
        if element_stats is not None:
            print(element_stats)
    
    else:
        print("Usage:")
        print("  python database_manager.py                    # Show database info")
        print("  python database_manager.py import <file>      # Import data from file")
        print("  python database_manager.py export [file]      # Export data to CSV")
        print("  python database_manager.py stats              # Show statistics")

if __name__ == "__main__":
    main()
