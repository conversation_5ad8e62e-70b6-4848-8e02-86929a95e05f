# Lottery Records Web Scraper

A comprehensive Python web scraping solution to extract lottery data from JavaScript-rendered websites and save it to local files in multiple formats.

## Features

- 🌐 **JavaScript Support**: Uses Selenium WebDriver to handle dynamic content
- 📊 **Multiple Export Formats**: Saves data to CSV, Excel, and JSON formats
- 🔄 **Pagination Handling**: Automatically navigates through multiple pages
- 🧹 **Data Cleaning**: Cleans and validates extracted data
- 📝 **Comprehensive Logging**: Detailed logging for debugging and monitoring
- ⚙️ **Configurable**: Easy to customize through configuration files
- 🛡️ **Error Handling**: Robust error handling and retry mechanisms
- 🚀 **Rate Limiting**: Respectful scraping with configurable delays

## Installation

1. **Clone or download the project files**

2. **Install Python dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Install Chrome browser** (required for Selenium WebDriver)
   - The script will automatically download and manage ChromeDriver

## Usage

### Quick Start

Run the scraper with default settings:
```bash
python lottery_scraper.py
```

Or use the runner script:
```bash
python run_scraper.py
```

### Advanced Usage

Use the runner script with custom options:
```bash
# Run with custom URL and output directory
python run_scraper.py --url "your-url-here" --output "custom_output"

# Run in visible browser mode (not headless)
python run_scraper.py --headless false

# Limit to 10 pages with 5-second delays
python run_scraper.py --max-pages 10 --delay 5
```

### Configuration

Edit `config.py` to customize:
- Target URL
- Output directory and file formats
- Browser settings (headless mode, timeouts)
- Rate limiting and retry settings
- Data extraction keywords

## Output Files

The scraper creates several files in the output directory:

- **`lottery_data_YYYYMMDD_HHMMSS.csv`**: Main data in CSV format
- **`lottery_data_YYYYMMDD_HHMMSS.xlsx`**: Main data in Excel format
- **`lottery_data_YYYYMMDD_HHMMSS.json`**: Main data in JSON format
- **`page_source.html`**: Raw HTML source for debugging
- **`page_text.txt`**: Extracted text content for analysis
- **`scraper_log_YYYYMMDD_HHMMSS.log`**: Detailed execution log

## Data Extraction Strategy

The scraper uses multiple strategies to extract data:

1. **Table Extraction**: Looks for HTML tables and extracts structured data
2. **Element Extraction**: Searches for div/li elements with relevant classes
3. **General Text Extraction**: Falls back to extracting any meaningful text content

## Troubleshooting

### Common Issues

1. **"Please enable JavaScript" error**:
   - This is expected - the scraper uses Selenium to handle JavaScript

2. **ChromeDriver issues**:
   - The script automatically downloads ChromeDriver
   - Ensure Chrome browser is installed on your system

3. **No data extracted**:
   - Check the log files for detailed error information
   - Verify the website structure hasn't changed
   - Try running in non-headless mode to see what's happening

4. **Slow performance**:
   - Adjust the delay settings in `config.py`
   - Reduce the maximum number of pages to scrape

### Debug Mode

To run in debug mode (visible browser):
```bash
python run_scraper.py --headless false
```

This allows you to see what the browser is doing and identify any issues.

## Legal and Ethical Considerations

- ⚖️ **Respect robots.txt**: Check the website's robots.txt file
- 🕐 **Rate Limiting**: The scraper includes delays to avoid overwhelming servers
- 📋 **Terms of Service**: Ensure compliance with the website's terms of service
- 🔒 **Data Privacy**: Handle extracted data responsibly

## File Structure

```
├── lottery_scraper.py      # Main scraper class
├── run_scraper.py         # Command-line runner script
├── config.py              # Configuration settings
├── requirements.txt       # Python dependencies
├── README.md             # This file
└── output/               # Output directory (created automatically)
    ├── *.csv            # Data files
    ├── *.xlsx           # Excel files
    ├── *.json           # JSON backups
    ├── *.html           # Raw HTML sources
    ├── *.txt            # Text extracts
    └── *.log            # Log files
```

## Contributing

Feel free to submit issues, feature requests, or pull requests to improve the scraper.

## License

This project is provided as-is for educational and research purposes.
