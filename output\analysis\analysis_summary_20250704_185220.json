{"analysis_timestamp": "20250704_185220", "data_records": 18, "analysis_methods": ["frequency", "markov", "time_series", "machine_learning", "patterns"], "key_insights": {"hot_numbers": [6, 10, 47, 15, 43, 17, 39, 14, 35, 41], "cold_numbers": [44, 4, 49, 16, 34, 40, 37, 8, 36, 18], "most_common_pairs": [[6, 15], [13, 17], [15, 35], [15, 39], [35, 39]], "average_consecutive": 0.7777777777777778, "most_common_even_odd": "2E4O", "sum_trend": "increasing", "trend_strength": 0.012254969336824554, "best_prediction_models": {"sum": "LinearRegression", "avg": "LinearRegression", "range": "RandomForest", "even_count": "RandomForest"}}}