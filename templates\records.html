{% extends "base.html" %}
{% block content %}
<h1>📋 Records</h1>
<div class="card">
    <div class="card-header">
        <div class="row">
            <div class="col-md-6">
                <h5>Lottery Records</h5>
            </div>
            <div class="col-md-6">
                <div class="input-group">
                    <input type="number" class="form-control" id="searchNumber" placeholder="Search by number">
                    <button class="btn btn-primary" onclick="searchByNumber()">Search</button>
                </div>
            </div>
        </div>
    </div>
    <div class="card-body">
        <div id="recordsTable"></div>
        <div id="searchResults" style="display:none;"></div>
    </div>
</div>
{% endblock %}
{% block scripts %}
<script>
let currentPage = 1;

function loadRecords(page = 1) {
    fetch(`/api/records?page=${page}&per_page=20`)
        .then(response => response.json())
        .then(data => {
            let html = '<div class="table-responsive"><table class="table table-striped"><thead><tr><th>Date</th><th>Period</th><th>Numbers</th><th>Total</th></tr></thead><tbody>';
            data.records.forEach(record => {
                html += `<tr><td>${record.date}</td><td>${record.period}</td><td>${record.raw_numbers}</td><td>${record.total_numbers}</td></tr>`;
            });
            html += '</tbody></table></div>';

            // Add pagination
            html += '<nav><ul class="pagination justify-content-center">';
            for (let i = 1; i <= data.total_pages; i++) {
                html += `<li class="page-item ${i === page ? 'active' : ''}"><a class="page-link" href="#" onclick="loadRecords(${i})">${i}</a></li>`;
            }
            html += '</ul></nav>';

            document.getElementById('recordsTable').innerHTML = html;
            currentPage = page;
        });
}

function searchByNumber() {
    const number = document.getElementById('searchNumber').value;
    if (!number) return;

    fetch(`/api/search?number=${number}`)
        .then(response => response.json())
        .then(data => {
            let html = '<h6>Search Results for Number ' + number + ':</h6>';
            html += '<div class="table-responsive"><table class="table table-striped"><thead><tr><th>Date</th><th>Period</th><th>Position</th><th>Animal</th><th>Element</th></tr></thead><tbody>';
            data.results.forEach(result => {
                html += `<tr><td>${result.date}</td><td>${result.period}</td><td>${result.position}</td><td>${result.animal}</td><td>${result.element}</td></tr>`;
            });
            html += '</tbody></table></div>';
            html += '<button class="btn btn-secondary" onclick="hideSearch()">Back to All Records</button>';

            document.getElementById('searchResults').innerHTML = html;
            document.getElementById('searchResults').style.display = 'block';
            document.getElementById('recordsTable').style.display = 'none';
        });
}

function hideSearch() {
    document.getElementById('searchResults').style.display = 'none';
    document.getElementById('recordsTable').style.display = 'block';
    document.getElementById('searchNumber').value = '';
}

// Load initial records
loadRecords();
</script>
{% endblock %}