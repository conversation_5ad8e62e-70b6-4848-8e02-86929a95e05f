#!/usr/bin/env python3
"""
Enhanced Lottery Data Scraper - Complete Historical Data Collection (1-184 periods).
"""

import os
import time
import json
import pandas as pd
import requests
from datetime import datetime, timedelta
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from webdriver_manager.chrome import ChromeDriverManager
import logging
import re
from bs4 import BeautifulSoup

class EnhancedLotteryScraper:
    """Enhanced scraper for complete historical lottery data collection."""
    
    def __init__(self):
        """Initialize the enhanced scraper."""
        self.setup_logging()
        self.base_url = "https://wid8-baidu630.gabd11133ff.com/pages/historyRecord/lotteryRecord/index"
        self.driver = None
        self.all_records = []
        self.target_periods = 184
        
    def setup_logging(self):
        """Setup logging configuration."""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('logs/enhanced_scraper.log', encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger('EnhancedScraper')
        
        # Create logs directory
        os.makedirs('logs', exist_ok=True)
    
    def setup_driver(self):
        """Setup Chrome WebDriver with enhanced options."""
        try:
            chrome_options = Options()
            chrome_options.add_argument('--headless')  # Run in background
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--window-size=1920,1080')
            chrome_options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')
            
            # Try to setup ChromeDriver
            try:
                service = Service(ChromeDriverManager().install())
                self.driver = webdriver.Chrome(service=service, options=chrome_options)
                self.logger.info("✅ ChromeDriver setup successful")
                return True
            except Exception as e:
                self.logger.warning(f"ChromeDriverManager failed: {e}")
                # Try alternative approach
                self.driver = webdriver.Chrome(options=chrome_options)
                self.logger.info("✅ ChromeDriver setup successful (alternative)")
                return True
                
        except Exception as e:
            self.logger.error(f"❌ Failed to setup WebDriver: {e}")
            return False
    
    def scrape_period_range(self, start_period=1, end_period=184):
        """Scrape data for a range of periods."""
        try:
            self.logger.info(f"🔍 Scraping periods {start_period} to {end_period}")
            
            # Navigate to the main page
            url = f"{self.base_url}?route=aomen&"
            self.driver.get(url)
            time.sleep(3)
            
            # Try different strategies to get historical data
            records = []
            
            # Strategy 1: Check for pagination or period selection
            records.extend(self.try_pagination_scraping())
            
            # Strategy 2: Try direct URL manipulation for different periods
            if len(records) < end_period:
                records.extend(self.try_period_url_scraping(start_period, end_period))
            
            # Strategy 3: Try API endpoints if available
            if len(records) < end_period:
                records.extend(self.try_api_scraping(start_period, end_period))
            
            self.logger.info(f"📊 Total records collected: {len(records)}")
            return records
            
        except Exception as e:
            self.logger.error(f"❌ Error in period range scraping: {e}")
            return []
    
    def try_pagination_scraping(self):
        """Try to scrape using pagination controls."""
        records = []
        try:
            page_num = 1
            max_pages = 20  # Reasonable limit
            
            while page_num <= max_pages:
                self.logger.info(f"📄 Scraping page {page_num}")
                
                # Extract records from current page
                page_records = self.extract_records_from_page()
                if not page_records:
                    self.logger.info("No more records found, stopping pagination")
                    break
                
                records.extend(page_records)
                
                # Try to find and click next page button
                if not self.go_to_next_page():
                    self.logger.info("No next page available")
                    break
                
                page_num += 1
                time.sleep(2)  # Rate limiting
            
        except Exception as e:
            self.logger.error(f"Error in pagination scraping: {e}")
        
        return records
    
    def try_period_url_scraping(self, start_period, end_period):
        """Try scraping by manipulating URLs for different periods."""
        records = []
        try:
            # Try different URL patterns that might contain period information
            url_patterns = [
                f"{self.base_url}?route=aomen&period={{period}}",
                f"{self.base_url}?route=aomen&page={{page}}",
                f"{self.base_url}?route=aomen&start={{period}}&end={{period}}",
            ]
            
            for pattern in url_patterns:
                self.logger.info(f"🔗 Trying URL pattern: {pattern}")
                
                # Try a few different period values
                for period in range(start_period, min(start_period + 10, end_period + 1)):
                    try:
                        url = pattern.format(period=period, page=period)
                        self.driver.get(url)
                        time.sleep(1)
                        
                        page_records = self.extract_records_from_page()
                        if page_records:
                            records.extend(page_records)
                            self.logger.info(f"✅ Found {len(page_records)} records for period {period}")
                        
                    except Exception as e:
                        self.logger.debug(f"URL pattern failed for period {period}: {e}")
                        continue
                
                if records:
                    break  # If we found records with this pattern, use it
            
        except Exception as e:
            self.logger.error(f"Error in URL scraping: {e}")
        
        return records
    
    def try_api_scraping(self, start_period, end_period):
        """Try to find and use API endpoints for data."""
        records = []
        try:
            # Check network requests for API calls
            self.logger.info("🔍 Looking for API endpoints...")
            
            # Common API endpoint patterns
            api_patterns = [
                f"{self.base_url.replace('/pages/', '/api/')}/history",
                f"{self.base_url.replace('/pages/', '/api/')}/records",
                "https://wid8-baidu630.gabd11133ff.com/api/lottery/history",
                "https://wid8-baidu630.gabd11133ff.com/api/records/aomen",
            ]
            
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                'Referer': self.base_url,
                'Accept': 'application/json, text/plain, */*',
            }
            
            for api_url in api_patterns:
                try:
                    self.logger.info(f"🌐 Trying API: {api_url}")
                    
                    # Try different parameter combinations
                    params_list = [
                        {'route': 'aomen', 'limit': 200},
                        {'type': 'aomen', 'page': 1, 'size': 100},
                        {'game': 'aomen', 'start': start_period, 'end': end_period},
                    ]
                    
                    for params in params_list:
                        response = requests.get(api_url, params=params, headers=headers, timeout=10)
                        if response.status_code == 200:
                            try:
                                data = response.json()
                                api_records = self.parse_api_response(data)
                                if api_records:
                                    records.extend(api_records)
                                    self.logger.info(f"✅ API returned {len(api_records)} records")
                                    return records  # Success, return immediately
                            except json.JSONDecodeError:
                                continue
                        
                except requests.RequestException as e:
                    self.logger.debug(f"API request failed: {e}")
                    continue
            
        except Exception as e:
            self.logger.error(f"Error in API scraping: {e}")
        
        return records
    
    def extract_records_from_page(self):
        """Extract lottery records from current page."""
        records = []
        try:
            # Wait for content to load
            WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )
            
            # Get page source and parse with BeautifulSoup
            soup = BeautifulSoup(self.driver.page_source, 'html.parser')
            
            # Try different selectors for lottery data
            selectors = [
                'table tr',  # Standard table rows
                '.lottery-record',  # Custom lottery record class
                '.record-item',  # Record item class
                '[data-period]',  # Elements with period data
                '.history-item',  # History item class
            ]
            
            for selector in selectors:
                elements = soup.select(selector)
                if elements:
                    self.logger.info(f"📋 Found {len(elements)} elements with selector: {selector}")
                    
                    for element in elements:
                        record = self.parse_record_element(element)
                        if record:
                            records.append(record)
                    
                    if records:
                        break  # Use the first successful selector
            
            # If no structured data found, try regex extraction
            if not records:
                records = self.extract_with_regex(soup.get_text())
            
        except Exception as e:
            self.logger.error(f"Error extracting records from page: {e}")
        
        return records
    
    def parse_record_element(self, element):
        """Parse a single record element."""
        try:
            text = element.get_text(strip=True)
            
            # Look for period number
            period_match = re.search(r'(\d+)期', text)
            if not period_match:
                return None
            
            period = period_match.group(1)
            
            # Look for date
            date_match = re.search(r'(\d{4}-\d{2}-\d{2})', text)
            date = date_match.group(1) if date_match else ''
            
            # Look for numbers
            number_matches = re.findall(r'\b(\d{1,2})\b', text)
            numbers = [int(n) for n in number_matches if 1 <= int(n) <= 49]
            
            if len(numbers) >= 6:  # At least 6 numbers expected
                # Separate regular numbers and special number
                regular_numbers = numbers[:-1]  # All but last
                special_number = numbers[-1]    # Last number
                
                record = {
                    'period': f"{period}期",
                    'date': date,
                    'numbers': regular_numbers,
                    'special_number': special_number,
                    'raw_text': text
                }
                
                return record
            
        except Exception as e:
            self.logger.debug(f"Error parsing record element: {e}")
        
        return None
    
    def extract_with_regex(self, text):
        """Extract records using regex patterns."""
        records = []
        try:
            # Pattern for lottery records: period, date, numbers
            pattern = r'(\d+)期.*?(\d{4}-\d{2}-\d{2}).*?((?:\d{1,2}[,，\s]+){5,}\d{1,2})'
            
            matches = re.findall(pattern, text, re.DOTALL)
            
            for match in matches:
                period, date, numbers_str = match
                
                # Extract numbers
                numbers = re.findall(r'\d{1,2}', numbers_str)
                numbers = [int(n) for n in numbers if 1 <= int(n) <= 49]
                
                if len(numbers) >= 6:
                    record = {
                        'period': f"{period}期",
                        'date': date,
                        'numbers': numbers[:-1],
                        'special_number': numbers[-1],
                        'raw_text': f"{period}期 {date} {numbers_str}"
                    }
                    records.append(record)
            
        except Exception as e:
            self.logger.error(f"Error in regex extraction: {e}")
        
        return records
    
    def parse_api_response(self, data):
        """Parse API response data."""
        records = []
        try:
            # Handle different API response formats
            if isinstance(data, dict):
                if 'data' in data:
                    data = data['data']
                elif 'records' in data:
                    data = data['records']
                elif 'list' in data:
                    data = data['list']
            
            if isinstance(data, list):
                for item in data:
                    record = self.parse_api_record(item)
                    if record:
                        records.append(record)
            
        except Exception as e:
            self.logger.error(f"Error parsing API response: {e}")
        
        return records
    
    def parse_api_record(self, item):
        """Parse a single API record."""
        try:
            if isinstance(item, dict):
                # Try different field names
                period = item.get('period') or item.get('issue') or item.get('期数')
                date = item.get('date') or item.get('draw_date') or item.get('开奖日期')
                numbers = item.get('numbers') or item.get('winning_numbers') or item.get('开奖号码')
                special = item.get('special') or item.get('special_number') or item.get('特码')
                
                if period and numbers:
                    return {
                        'period': str(period),
                        'date': str(date) if date else '',
                        'numbers': numbers if isinstance(numbers, list) else [],
                        'special_number': special,
                        'source': 'api'
                    }
            
        except Exception as e:
            self.logger.debug(f"Error parsing API record: {e}")
        
        return None
    
    def go_to_next_page(self):
        """Try to navigate to next page."""
        try:
            # Common next page selectors
            next_selectors = [
                'a[aria-label="Next"]',
                '.next-page',
                '.pagination-next',
                'button:contains("下一页")',
                'a:contains("下一页")',
                '.page-next',
            ]
            
            for selector in next_selectors:
                try:
                    element = self.driver.find_element(By.CSS_SELECTOR, selector)
                    if element.is_enabled():
                        element.click()
                        time.sleep(2)
                        return True
                except:
                    continue
            
            return False
            
        except Exception as e:
            self.logger.debug(f"Error going to next page: {e}")
            return False
    
    def save_comprehensive_data(self, records):
        """Save data in multiple formats with comprehensive information."""
        try:
            if not records:
                self.logger.warning("No records to save")
                return False
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            
            # Create output directory
            os.makedirs('output', exist_ok=True)
            
            # Convert to DataFrame for easier manipulation
            df_data = []
            for record in records:
                row = {
                    '期数': record.get('period', ''),
                    '日期': record.get('date', ''),
                    '开奖号码(平码)': ', '.join(map(str, record.get('numbers', []))),
                    '特别号码(特码)': record.get('special_number', ''),
                    '号码总数': len(record.get('numbers', [])),
                    '数据源': record.get('source', 'web')
                }
                
                # Add individual number columns
                numbers = record.get('numbers', [])
                for i in range(6):  # Assume max 6 regular numbers
                    row[f'号码_{i+1}'] = numbers[i] if i < len(numbers) else ''
                
                df_data.append(row)
            
            df = pd.DataFrame(df_data)
            
            # Sort by period number
            df['期数_数字'] = df['期数'].str.extract(r'(\d+)').astype(int)
            df = df.sort_values('期数_数字').drop('期数_数字', axis=1)
            
            # Save in multiple formats
            csv_file = f"output/complete_lottery_data_{timestamp}.csv"
            excel_file = f"output/complete_lottery_data_{timestamp}.xlsx"
            json_file = f"output/complete_lottery_data_{timestamp}.json"
            
            df.to_csv(csv_file, index=False, encoding='utf-8-sig')
            df.to_excel(excel_file, index=False)
            
            # Save JSON with original structure
            with open(json_file, 'w', encoding='utf-8') as f:
                json.dump(records, f, ensure_ascii=False, indent=2)
            
            self.logger.info(f"✅ Data saved successfully:")
            self.logger.info(f"   📄 CSV: {csv_file}")
            self.logger.info(f"   📊 Excel: {excel_file}")
            self.logger.info(f"   🔗 JSON: {json_file}")
            self.logger.info(f"   📈 Total records: {len(records)}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Error saving data: {e}")
            return False
    
    def run_complete_scraping(self):
        """Run complete scraping process for all historical data."""
        try:
            self.logger.info("🚀 Starting complete historical data scraping")
            
            # Setup WebDriver
            if not self.setup_driver():
                return False
            
            # Scrape all available periods
            all_records = self.scrape_period_range(1, self.target_periods)
            
            if all_records:
                # Save comprehensive data
                success = self.save_comprehensive_data(all_records)
                
                if success:
                    self.logger.info(f"🎉 Complete scraping finished successfully!")
                    self.logger.info(f"📊 Collected {len(all_records)} total records")
                    return True
                else:
                    self.logger.error("❌ Failed to save data")
                    return False
            else:
                self.logger.error("❌ No records collected")
                return False
                
        except Exception as e:
            self.logger.error(f"❌ Error in complete scraping: {e}")
            return False
        finally:
            if self.driver:
                self.driver.quit()
                self.logger.info("🔒 WebDriver closed")

def main():
    """Main function to run enhanced scraping."""
    scraper = EnhancedLotteryScraper()
    success = scraper.run_complete_scraping()
    
    if success:
        print("✅ Enhanced scraping completed successfully!")
    else:
        print("❌ Enhanced scraping failed!")
    
    return success

if __name__ == "__main__":
    main()
