#!/usr/bin/env python3
"""
Prediction Inference Engine for Lottery Numbers.
"""

import os
import pandas as pd
import numpy as np
import json
import logging
from datetime import datetime, timedelta
from collections import Counter, defaultdict
from itertools import combinations
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.linear_model import LinearRegression
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split
import warnings
warnings.filterwarnings('ignore')

# Set Chinese font for matplotlib
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

class LotteryPredictionEngine:
    """Advanced lottery prediction engine using multiple algorithms."""
    
    def __init__(self, data_file=None):
        """Initialize the prediction engine."""
        self.setup_logging()
        self.data = None
        self.processed_data = None
        self.trained_models = {}
        self.prediction_strategies = {}
        self.ensemble_predictions = {}
        
        if data_file:
            self.load_and_preprocess_data(data_file)
    
    def setup_logging(self):
        """Setup logging configuration."""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('logs/prediction_engine.log', encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger('PredictionEngine')
        
        # Create logs directory
        os.makedirs('logs', exist_ok=True)
    
    def load_and_preprocess_data(self, data_file):
        """Load and preprocess lottery data."""
        try:
            if data_file.endswith('.csv'):
                self.data = pd.read_csv(data_file)
            elif data_file.endswith('.json'):
                with open(data_file, 'r', encoding='utf-8') as f:
                    json_data = json.load(f)
                self.data = pd.DataFrame(json_data)
            
            self.logger.info(f"✅ Loaded {len(self.data)} records from {data_file}")
            self.preprocess_data()
            
        except Exception as e:
            self.logger.error(f"❌ Error loading data: {e}")
            raise
    
    def preprocess_data(self):
        """Preprocess data for prediction."""
        try:
            # Extract individual numbers and features
            numbers_data = []
            for _, row in self.data.iterrows():
                # Extract period number
                period_str = str(row.get('period', row.get('期数', '')))
                period_num = int(''.join(filter(str.isdigit, period_str))) if period_str else 0
                
                # Extract date
                date_str = str(row.get('date', row.get('日期', '')))
                
                # Extract numbers
                numbers = []
                for i in range(1, 8):
                    num_col = f'number_{i}'
                    if num_col in row and pd.notna(row[num_col]):
                        numbers.append(int(row[num_col]))
                
                if numbers and len(numbers) >= 6:
                    numbers_data.append({
                        'period': period_num,
                        'date': date_str,
                        'numbers': numbers[:6],
                        'special': numbers[6] if len(numbers) > 6 else numbers[-1],
                        'sum': sum(numbers[:6]),
                        'avg': np.mean(numbers[:6]),
                        'range': max(numbers[:6]) - min(numbers[:6]),
                        'even_count': sum(1 for n in numbers[:6] if n % 2 == 0),
                        'odd_count': sum(1 for n in numbers[:6] if n % 2 == 1),
                        'min_num': min(numbers[:6]),
                        'max_num': max(numbers[:6])
                    })
            
            self.processed_data = pd.DataFrame(numbers_data)
            self.processed_data = self.processed_data.sort_values('period').reset_index(drop=True)
            
            self.logger.info(f"✅ Preprocessed {len(self.processed_data)} records")
            
        except Exception as e:
            self.logger.error(f"❌ Error preprocessing data: {e}")
            raise
    
    def frequency_based_prediction(self):
        """Generate predictions based on frequency analysis."""
        try:
            self.logger.info("📊 Generating frequency-based predictions...")
            
            # Analyze number frequencies
            all_numbers = []
            for _, row in self.processed_data.iterrows():
                all_numbers.extend(row['numbers'])
            
            number_freq = Counter(all_numbers)
            special_freq = Counter(self.processed_data['special'].tolist())
            
            # Hot and cold numbers
            hot_numbers = [num for num, _ in number_freq.most_common(15)]
            cold_numbers = [num for num, _ in number_freq.most_common()[-15:]]
            
            # Position-based frequency
            position_freq = {}
            for pos in range(6):
                position_numbers = [row['numbers'][pos] for _, row in self.processed_data.iterrows() 
                                 if len(row['numbers']) > pos]
                position_freq[pos] = Counter(position_numbers)
            
            # Generate predictions
            predictions = {
                'hot_numbers_strategy': hot_numbers[:6],
                'balanced_strategy': [],
                'position_based_strategy': [],
                'cold_numbers_comeback': cold_numbers[:6]
            }
            
            # Balanced strategy (mix of hot and cold)
            balanced = hot_numbers[:3] + cold_numbers[:3]
            predictions['balanced_strategy'] = sorted(balanced)
            
            # Position-based strategy
            position_based = []
            for pos in range(6):
                if pos in position_freq:
                    most_common = position_freq[pos].most_common(3)
                    if most_common:
                        position_based.append(most_common[0][0])
            
            # Fill remaining positions if needed
            while len(position_based) < 6:
                for num in hot_numbers:
                    if num not in position_based:
                        position_based.append(num)
                        break
            
            predictions['position_based_strategy'] = position_based[:6]
            
            # Predict special number
            special_prediction = special_freq.most_common(1)[0][0] if special_freq else 1
            
            self.prediction_strategies['frequency_based'] = {
                'predictions': predictions,
                'special_prediction': special_prediction,
                'confidence_scores': {
                    'hot_numbers_strategy': 0.7,
                    'balanced_strategy': 0.6,
                    'position_based_strategy': 0.5,
                    'cold_numbers_comeback': 0.3
                }
            }
            
            self.logger.info("✅ Frequency-based predictions generated")
            
        except Exception as e:
            self.logger.error(f"❌ Error in frequency-based prediction: {e}")
    
    def pattern_based_prediction(self):
        """Generate predictions based on pattern analysis."""
        try:
            self.logger.info("🔍 Generating pattern-based predictions...")
            
            # Analyze recent patterns
            recent_data = self.processed_data.tail(10)  # Last 10 periods
            
            # Sum pattern analysis
            recent_sums = recent_data['sum'].tolist()
            avg_sum = np.mean(recent_sums)
            sum_trend = np.polyfit(range(len(recent_sums)), recent_sums, 1)[0]
            
            # Even/odd pattern analysis
            recent_even_counts = recent_data['even_count'].tolist()
            avg_even_count = np.mean(recent_even_counts)
            
            # Range pattern analysis
            recent_ranges = recent_data['range'].tolist()
            avg_range = np.mean(recent_ranges)
            
            # Generate pattern-based predictions
            target_sum = int(avg_sum + sum_trend)  # Predict next sum based on trend
            target_even_count = int(round(avg_even_count))
            target_range = int(avg_range)
            
            # Generate numbers that match the patterns
            pattern_predictions = self.generate_numbers_for_pattern(
                target_sum, target_even_count, target_range
            )
            
            self.prediction_strategies['pattern_based'] = {
                'predictions': pattern_predictions,
                'target_sum': target_sum,
                'target_even_count': target_even_count,
                'target_range': target_range,
                'confidence_score': 0.6
            }
            
            self.logger.info("✅ Pattern-based predictions generated")
            
        except Exception as e:
            self.logger.error(f"❌ Error in pattern-based prediction: {e}")
    
    def generate_numbers_for_pattern(self, target_sum, target_even_count, target_range):
        """Generate numbers that match specific patterns."""
        try:
            # Get frequency data for guidance
            all_numbers = []
            for _, row in self.processed_data.iterrows():
                all_numbers.extend(row['numbers'])
            number_freq = Counter(all_numbers)
            
            # Create weighted list based on frequency
            weighted_numbers = []
            for num in range(1, 50):
                weight = number_freq.get(num, 1)
                weighted_numbers.extend([num] * weight)
            
            best_combination = None
            best_score = float('inf')
            
            # Try multiple random combinations
            for _ in range(1000):
                # Generate random combination
                numbers = sorted(np.random.choice(weighted_numbers, 6, replace=False))
                
                # Calculate how well it matches target patterns
                actual_sum = sum(numbers)
                actual_even_count = sum(1 for n in numbers if n % 2 == 0)
                actual_range = max(numbers) - min(numbers)
                
                # Score based on pattern matching
                sum_diff = abs(actual_sum - target_sum)
                even_diff = abs(actual_even_count - target_even_count)
                range_diff = abs(actual_range - target_range)
                
                score = sum_diff + even_diff * 10 + range_diff
                
                if score < best_score:
                    best_score = score
                    best_combination = numbers
            
            return best_combination if best_combination else [1, 2, 3, 4, 5, 6]
            
        except Exception as e:
            self.logger.warning(f"⚠️ Error generating pattern numbers: {e}")
            return [1, 2, 3, 4, 5, 6]
    
    def machine_learning_prediction(self):
        """Generate predictions using machine learning models."""
        try:
            self.logger.info("🤖 Generating ML-based predictions...")
            
            # Create features
            features_df = self.processed_data.copy()
            
            # Create lag features
            for lag in [1, 2, 3]:
                features_df[f'sum_lag_{lag}'] = features_df['sum'].shift(lag)
                features_df[f'avg_lag_{lag}'] = features_df['avg'].shift(lag)
                features_df[f'range_lag_{lag}'] = features_df['range'].shift(lag)
                features_df[f'even_count_lag_{lag}'] = features_df['even_count'].shift(lag)
            
            # Create moving averages
            for window in [3, 5]:
                features_df[f'sum_ma_{window}'] = features_df['sum'].rolling(window=window).mean()
                features_df[f'avg_ma_{window}'] = features_df['avg'].rolling(window=window).mean()
            
            # Drop rows with NaN values
            features_df = features_df.dropna()
            
            if len(features_df) < 5:
                self.logger.warning("⚠️ Insufficient data for ML prediction")
                return
            
            # Define features and targets
            feature_columns = [col for col in features_df.columns 
                             if any(x in col for x in ['lag', 'ma']) or col == 'period']
            X = features_df[feature_columns]
            
            # Train models for different targets
            models = {
                'RandomForest': RandomForestRegressor(n_estimators=50, random_state=42),
                'GradientBoosting': GradientBoostingRegressor(n_estimators=50, random_state=42),
                'LinearRegression': LinearRegression()
            }
            
            targets = ['sum', 'avg', 'range', 'even_count']
            ml_predictions = {}
            
            for target in targets:
                y = features_df[target]
                
                # Split data (use most recent for prediction)
                X_train, X_test = X[:-1], X[-1:]
                y_train, y_test = y[:-1], y[-1:]
                
                target_predictions = {}
                
                for model_name, model in models.items():
                    try:
                        if model_name == 'LinearRegression':
                            scaler = StandardScaler()
                            X_train_scaled = scaler.fit_transform(X_train)
                            X_test_scaled = scaler.transform(X_test)
                            
                            model.fit(X_train_scaled, y_train)
                            pred = model.predict(X_test_scaled)[0]
                        else:
                            model.fit(X_train, y_train)
                            pred = model.predict(X_test)[0]
                        
                        target_predictions[model_name] = pred
                        
                    except Exception as e:
                        self.logger.warning(f"⚠️ ML model {model_name} failed for {target}: {e}")
                        continue
                
                if target_predictions:
                    # Use ensemble average
                    ml_predictions[target] = np.mean(list(target_predictions.values()))
            
            # Generate numbers based on ML predictions
            if ml_predictions:
                predicted_sum = int(ml_predictions.get('sum', 120))
                predicted_even_count = int(round(ml_predictions.get('even_count', 3)))
                predicted_range = int(ml_predictions.get('range', 30))
                
                ml_numbers = self.generate_numbers_for_pattern(
                    predicted_sum, predicted_even_count, predicted_range
                )
                
                self.prediction_strategies['machine_learning'] = {
                    'predictions': ml_numbers,
                    'predicted_features': ml_predictions,
                    'confidence_score': 0.7
                }
            
            self.logger.info("✅ ML-based predictions generated")
            
        except Exception as e:
            self.logger.error(f"❌ Error in ML prediction: {e}")
    
    def markov_chain_prediction(self):
        """Generate predictions using Markov chain analysis."""
        try:
            self.logger.info("🔗 Generating Markov chain predictions...")
            
            # Build transition matrix
            transitions = defaultdict(lambda: defaultdict(int))
            
            for i in range(len(self.processed_data) - 1):
                current_numbers = set(self.processed_data.iloc[i]['numbers'])
                next_numbers = set(self.processed_data.iloc[i + 1]['numbers'])
                
                for curr_num in current_numbers:
                    for next_num in next_numbers:
                        transitions[curr_num][next_num] += 1
            
            # Convert to probabilities
            transition_probs = {}
            for curr_num, next_counts in transitions.items():
                total = sum(next_counts.values())
                if total > 0:
                    transition_probs[curr_num] = {
                        next_num: count / total 
                        for next_num, count in next_counts.items()
                    }
            
            # Generate predictions based on last period
            last_numbers = set(self.processed_data.iloc[-1]['numbers'])
            markov_predictions = []
            
            # For each number in last period, find most likely next numbers
            candidate_numbers = defaultdict(float)
            
            for last_num in last_numbers:
                if last_num in transition_probs:
                    for next_num, prob in transition_probs[last_num].items():
                        candidate_numbers[next_num] += prob
            
            # Select top candidates
            if candidate_numbers:
                sorted_candidates = sorted(candidate_numbers.items(), key=lambda x: x[1], reverse=True)
                markov_predictions = [num for num, _ in sorted_candidates[:6]]
            
            # Fill with frequency-based if not enough
            if len(markov_predictions) < 6:
                all_numbers = []
                for _, row in self.processed_data.iterrows():
                    all_numbers.extend(row['numbers'])
                number_freq = Counter(all_numbers)
                
                for num, _ in number_freq.most_common():
                    if num not in markov_predictions:
                        markov_predictions.append(num)
                        if len(markov_predictions) >= 6:
                            break
            
            self.prediction_strategies['markov_chain'] = {
                'predictions': sorted(markov_predictions[:6]),
                'transition_probabilities': dict(transition_probs),
                'confidence_score': 0.5
            }
            
            self.logger.info("✅ Markov chain predictions generated")
            
        except Exception as e:
            self.logger.error(f"❌ Error in Markov chain prediction: {e}")
    
    def ensemble_prediction(self):
        """Generate ensemble predictions combining all strategies."""
        try:
            self.logger.info("🎯 Generating ensemble predictions...")
            
            # Collect all predictions
            all_predictions = []
            strategy_weights = {}
            
            for strategy_name, strategy_data in self.prediction_strategies.items():
                if 'predictions' in strategy_data:
                    predictions = strategy_data['predictions']
                    confidence = strategy_data.get('confidence_score', 0.5)
                    
                    if isinstance(predictions, dict):
                        # Handle multiple prediction sets
                        for pred_name, pred_numbers in predictions.items():
                            all_predictions.append((pred_numbers, confidence))
                            strategy_weights[f"{strategy_name}_{pred_name}"] = confidence
                    else:
                        # Handle single prediction set
                        all_predictions.append((predictions, confidence))
                        strategy_weights[strategy_name] = confidence
            
            # Count number occurrences with weights
            number_scores = defaultdict(float)
            
            for predictions, weight in all_predictions:
                for num in predictions:
                    number_scores[num] += weight
            
            # Select top numbers
            sorted_numbers = sorted(number_scores.items(), key=lambda x: x[1], reverse=True)
            ensemble_numbers = [num for num, _ in sorted_numbers[:6]]
            
            # Ensure we have 6 numbers
            if len(ensemble_numbers) < 6:
                # Fill with most frequent numbers
                all_numbers = []
                for _, row in self.processed_data.iterrows():
                    all_numbers.extend(row['numbers'])
                number_freq = Counter(all_numbers)
                
                for num, _ in number_freq.most_common():
                    if num not in ensemble_numbers:
                        ensemble_numbers.append(num)
                        if len(ensemble_numbers) >= 6:
                            break
            
            # Predict special number (use frequency-based)
            special_freq = Counter(self.processed_data['special'].tolist())
            ensemble_special = special_freq.most_common(1)[0][0] if special_freq else 1
            
            self.ensemble_predictions = {
                'regular_numbers': sorted(ensemble_numbers[:6]),
                'special_number': ensemble_special,
                'strategy_weights': strategy_weights,
                'number_scores': dict(number_scores),
                'confidence_score': np.mean([w for w in strategy_weights.values()])
            }
            
            self.logger.info("✅ Ensemble predictions generated")
            
        except Exception as e:
            self.logger.error(f"❌ Error in ensemble prediction: {e}")
    
    def generate_next_period_prediction(self):
        """Generate prediction for the next period."""
        try:
            self.logger.info("🔮 Generating next period prediction...")
            
            # Run all prediction strategies
            self.frequency_based_prediction()
            self.pattern_based_prediction()
            self.machine_learning_prediction()
            self.markov_chain_prediction()
            
            # Generate ensemble prediction
            self.ensemble_prediction()
            
            # Determine next period number
            last_period = self.processed_data['period'].max()
            next_period = last_period + 1
            
            # Create prediction result
            prediction_result = {
                'next_period': next_period,
                'prediction_date': datetime.now().strftime("%Y-%m-%d"),
                'ensemble_prediction': self.ensemble_predictions,
                'individual_strategies': self.prediction_strategies,
                'data_summary': {
                    'training_periods': len(self.processed_data),
                    'last_period': last_period,
                    'prediction_confidence': self.ensemble_predictions.get('confidence_score', 0.5)
                }
            }
            
            return prediction_result
            
        except Exception as e:
            self.logger.error(f"❌ Error generating next period prediction: {e}")
            return None
    
    def save_prediction_report(self, prediction_result):
        """Save prediction report to file."""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

            # Create output directory
            os.makedirs('output/predictions', exist_ok=True)

            # Convert numpy types to native Python types for JSON serialization
            def convert_numpy_types(obj):
                if isinstance(obj, dict):
                    return {str(k) if not isinstance(k, (str, int, float, bool)) else k: convert_numpy_types(v) for k, v in obj.items()}
                elif isinstance(obj, list):
                    return [convert_numpy_types(item) for item in obj]
                elif isinstance(obj, tuple):
                    return list(convert_numpy_types(list(obj)))
                elif isinstance(obj, (np.integer, np.int32, np.int64)):
                    return int(obj)
                elif isinstance(obj, (np.floating, np.float32, np.float64)):
                    return float(obj)
                elif isinstance(obj, np.ndarray):
                    return obj.tolist()
                elif hasattr(obj, 'item'):  # Handle other numpy scalars
                    return obj.item()
                else:
                    return obj

            serializable_result = convert_numpy_types(prediction_result)

            # Save detailed prediction
            report_file = f"output/predictions/prediction_report_{timestamp}.json"
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(serializable_result, f, ensure_ascii=False, indent=2, default=str)
            
            # Create human-readable summary
            summary = self.create_prediction_summary(prediction_result)
            summary_file = f"output/predictions/prediction_summary_{timestamp}.txt"
            with open(summary_file, 'w', encoding='utf-8') as f:
                f.write(summary)
            
            self.logger.info(f"✅ Prediction report saved:")
            self.logger.info(f"   📄 Detailed: {report_file}")
            self.logger.info(f"   📋 Summary: {summary_file}")
            
            return report_file, summary_file
            
        except Exception as e:
            self.logger.error(f"❌ Error saving prediction report: {e}")
            return None, None
    
    def create_prediction_summary(self, prediction_result):
        """Create human-readable prediction summary."""
        try:
            ensemble = prediction_result.get('ensemble_prediction', {})
            regular_numbers = ensemble.get('regular_numbers', [])
            special_number = ensemble.get('special_number', 0)
            confidence = ensemble.get('confidence_score', 0)
            
            summary = f"""
🎯 彩票预测报告
================

📅 预测期数: {prediction_result.get('next_period', 'N/A')}期
📅 预测日期: {prediction_result.get('prediction_date', 'N/A')}

🎲 推荐号码 (平码): {', '.join(map(str, regular_numbers))}
⭐ 特别号码 (特码): {special_number}

📊 预测信心度: {confidence:.1%}

🔍 各策略预测结果:
"""
            
            strategies = prediction_result.get('individual_strategies', {})
            for strategy_name, strategy_data in strategies.items():
                summary += f"\n📈 {strategy_name.upper()}策略:\n"
                
                if 'predictions' in strategy_data:
                    predictions = strategy_data['predictions']
                    if isinstance(predictions, dict):
                        for pred_name, pred_numbers in predictions.items():
                            summary += f"   - {pred_name}: {', '.join(map(str, pred_numbers))}\n"
                    else:
                        summary += f"   - 推荐: {', '.join(map(str, predictions))}\n"
                
                confidence_score = strategy_data.get('confidence_score', 0)
                summary += f"   - 信心度: {confidence_score:.1%}\n"
            
            summary += f"""
📈 数据统计:
- 训练期数: {prediction_result.get('data_summary', {}).get('training_periods', 0)}期
- 最后期数: {prediction_result.get('data_summary', {}).get('last_period', 0)}期

⚠️  免责声明: 此预测仅供参考，彩票具有随机性，请理性投注。
"""
            
            return summary
            
        except Exception as e:
            self.logger.warning(f"⚠️ Error creating summary: {e}")
            return "预测摘要生成失败"

def main():
    """Main function to run prediction engine."""
    # Use existing data file
    data_file = "output/lottery_data_20250704_181955.csv"
    
    if not os.path.exists(data_file):
        print(f"❌ Data file not found: {data_file}")
        return False
    
    # Create prediction engine
    engine = LotteryPredictionEngine(data_file)
    
    # Generate prediction
    prediction_result = engine.generate_next_period_prediction()
    
    if prediction_result:
        # Save prediction report
        report_file, summary_file = engine.save_prediction_report(prediction_result)
        
        if report_file and summary_file:
            print("✅ Prediction completed successfully!")
            print(f"📊 Check {report_file} for detailed results")
            print(f"📋 Check {summary_file} for summary")
            
            # Display prediction summary
            ensemble = prediction_result.get('ensemble_prediction', {})
            regular_numbers = ensemble.get('regular_numbers', [])
            special_number = ensemble.get('special_number', 0)
            
            print(f"\n🎯 下期预测 ({prediction_result.get('next_period')}期):")
            print(f"   平码: {', '.join(map(str, regular_numbers))}")
            print(f"   特码: {special_number}")
            print(f"   信心度: {ensemble.get('confidence_score', 0):.1%}")
        else:
            print("❌ Failed to save prediction report!")
    else:
        print("❌ Prediction generation failed!")
    
    return prediction_result is not None

if __name__ == "__main__":
    main()
