#!/usr/bin/env python3
"""
Lottery Records Web Scraper
Extracts lottery data from the specified website and saves to local files.
"""

import time
import logging
import pandas as pd
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from webdriver_manager.chrome import ChromeDriverManager
from bs4 import BeautifulSoup
import os
import json
import config

class LotteryScraper:
    def __init__(self, url=None, output_dir=None):
        """
        Initialize the lottery scraper.

        Args:
            url (str): Target URL to scrape (defaults to config value)
            output_dir (str): Directory to save output files (defaults to config value)
        """
        self.url = url or config.TARGET_URL
        self.output_dir = output_dir or config.OUTPUT_DIR
        self.driver = None
        self.data = []

        # Configuration settings
        self.headless_mode = config.HEADLESS_MODE
        self.max_pages = config.MAX_PAGES
        self.delay_between_pages = config.DELAY_BETWEEN_PAGES
        self.retry_attempts = config.RETRY_ATTEMPTS

        # Create output directory if it doesn't exist
        os.makedirs(self.output_dir, exist_ok=True)

        # Setup logging
        self.setup_logging()
        
    def setup_logging(self):
        """Setup logging configuration."""
        log_filename = os.path.join(self.output_dir, f"scraper_log_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_filename),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
        
    def setup_driver(self):
        """Setup Chrome WebDriver with appropriate options."""
        try:
            chrome_options = Options()

            # Use configuration for headless mode
            if self.headless_mode:
                chrome_options.add_argument("--headless")

            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--disable-gpu")
            chrome_options.add_argument("--window-size=1920,1080")
            chrome_options.add_argument(f"--user-agent={config.USER_AGENT}")

            # Install and setup ChromeDriver
            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            self.driver.implicitly_wait(config.IMPLICIT_WAIT)
            self.driver.set_page_load_timeout(config.PAGE_LOAD_TIMEOUT)

            self.logger.info("WebDriver setup completed successfully")
            return True

        except Exception as e:
            self.logger.error(f"Failed to setup WebDriver: {str(e)}")
            return False
    
    def load_page(self):
        """Load the target webpage and wait for content to load."""
        try:
            self.logger.info(f"Loading page: {self.url}")
            self.driver.get(self.url)
            
            # Wait for page to load - adjust selector based on actual page content
            wait = WebDriverWait(self.driver, 30)
            
            # Try to wait for common elements that might indicate the page has loaded
            try:
                # Wait for any table, list, or data container to appear
                wait.until(EC.presence_of_element_located((By.TAG_NAME, "table")))
                self.logger.info("Table element found - page loaded successfully")
            except TimeoutException:
                try:
                    # Try waiting for div containers that might hold data
                    wait.until(EC.presence_of_element_located((By.CLASS_NAME, "record")))
                    self.logger.info("Record container found - page loaded successfully")
                except TimeoutException:
                    # If specific elements aren't found, wait for general content
                    time.sleep(10)  # Give extra time for JavaScript to execute
                    self.logger.warning("Specific elements not found, proceeding with general wait")
            
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to load page: {str(e)}")
            return False
    
    def extract_data(self):
        """Extract lottery data from the loaded page."""
        try:
            self.logger.info("Starting data extraction...")
            
            # Get page source and parse with BeautifulSoup
            page_source = self.driver.page_source
            soup = BeautifulSoup(page_source, 'html.parser')
            
            # Save raw HTML for debugging
            with open(os.path.join(self.output_dir, "page_source.html"), "w", encoding="utf-8") as f:
                f.write(page_source)
            
            # Try different strategies to find data
            data_extracted = False
            
            # Strategy 1: Look for tables
            tables = soup.find_all('table')
            if tables:
                self.logger.info(f"Found {len(tables)} table(s)")
                data_extracted = self.extract_from_tables(tables)
            
            # Strategy 2: Look for structured divs/lists if tables not found
            if not data_extracted:
                # Find elements with relevant class names
                records = []
                for tag in ['div', 'li']:
                    elements = soup.find_all(tag)
                    for element in elements:
                        class_attr = element.get('class', [])
                        if class_attr and any(
                            keyword in ' '.join(class_attr).lower()
                            for keyword in ['record', 'item', 'row', 'data', 'result']
                        ):
                            records.append(element)

                if records:
                    self.logger.info(f"Found {len(records)} potential record elements")
                    data_extracted = self.extract_from_elements(records)
            
            # Strategy 3: Extract lottery records specifically
            if not data_extracted:
                self.logger.info("Attempting to extract lottery records...")
                data_extracted = self.extract_lottery_records(soup)

            # Strategy 4: Look for any structured data as fallback
            if not data_extracted:
                self.logger.info("Attempting to extract any structured data...")
                data_extracted = self.extract_general_data(soup)
            
            if data_extracted:
                self.logger.info(f"Successfully extracted {len(self.data)} records")
                return True
            else:
                self.logger.warning("No data could be extracted from the page")
                return False
                
        except Exception as e:
            self.logger.error(f"Error during data extraction: {str(e)}")
            return False
    
    def extract_from_tables(self, tables):
        """Extract data from HTML tables."""
        try:
            for i, table in enumerate(tables):
                self.logger.info(f"Processing table {i+1}")
                
                # Get headers
                headers = []
                header_row = table.find('tr')
                if header_row:
                    headers = [th.get_text(strip=True) for th in header_row.find_all(['th', 'td'])]
                
                # Get data rows
                rows = table.find_all('tr')[1:] if header_row else table.find_all('tr')
                
                for row in rows:
                    cells = row.find_all(['td', 'th'])
                    if cells:
                        row_data = {}
                        for j, cell in enumerate(cells):
                            header = headers[j] if j < len(headers) else f"Column_{j+1}"
                            row_data[header] = cell.get_text(strip=True)
                        
                        if any(row_data.values()):  # Only add non-empty rows
                            row_data['table_index'] = i + 1
                            row_data['extraction_time'] = datetime.now().isoformat()
                            self.data.append(row_data)
            
            return len(self.data) > 0
            
        except Exception as e:
            self.logger.error(f"Error extracting from tables: {str(e)}")
            return False

    def extract_from_elements(self, elements):
        """Extract data from div/li elements."""
        try:
            for i, element in enumerate(elements):
                # Try to extract structured data from each element
                text_content = element.get_text(strip=True)
                if text_content:
                    # Look for patterns in the text that might indicate lottery data
                    row_data = {
                        'content': text_content,
                        'element_type': element.name,
                        'element_class': element.get('class', []),
                        'element_index': i + 1,
                        'extraction_time': datetime.now().isoformat()
                    }

                    # Try to extract specific data if patterns are found
                    # This can be customized based on the actual data structure
                    if any(keyword in text_content.lower() for keyword in config.LOTTERY_KEYWORDS):
                        self.data.append(row_data)

            return len(self.data) > 0

        except Exception as e:
            self.logger.error(f"Error extracting from elements: {str(e)}")
            return False

    def extract_lottery_records(self, soup):
        """Extract structured lottery records from the page."""
        try:
            self.logger.info("Extracting structured lottery records...")

            # Get all text content
            all_text = soup.get_text()

            # Look for lottery record patterns
            import re

            # Pattern to match lottery records: Date + Period + Numbers
            # Example: 2025-07-03 184期08狗/木32狗/火20狗/土30鼠/水18鼠/火35羊/土28虎/土
            pattern = r'(\d{4}-\d{2}-\d{2})\s+(\d+期)([^2]+?)(?=\d{4}-\d{2}-\d{2}|\s*$)'

            matches = re.findall(pattern, all_text)

            for match in matches:
                date, period, numbers_text = match

                # Clean up the numbers text
                numbers_text = numbers_text.strip()

                # Extract individual number/animal/element combinations
                # Pattern: number + animal + / + element
                number_pattern = r'(\d+)([^/\d]+)/([^/\d]+)'
                number_matches = re.findall(number_pattern, numbers_text)

                if number_matches:
                    # Create structured record
                    record = {
                        'date': date,
                        'period': period,
                        'raw_numbers': numbers_text,
                        'extraction_time': datetime.now().isoformat()
                    }

                    # Add individual numbers
                    for i, (number, animal, element) in enumerate(number_matches):
                        record[f'number_{i+1}'] = number
                        record[f'animal_{i+1}'] = animal
                        record[f'element_{i+1}'] = element
                        record[f'combination_{i+1}'] = f"{number}{animal}/{element}"

                    record['total_numbers'] = len(number_matches)
                    self.data.append(record)

            self.logger.info(f"Extracted {len(matches)} lottery records")
            return len(matches) > 0

        except Exception as e:
            self.logger.error(f"Error extracting lottery records: {str(e)}")
            return False

    def extract_general_data(self, soup):
        """Extract any available data as fallback."""
        try:
            # Get all text content and try to identify data patterns
            all_text = soup.get_text()

            # Save all text for manual inspection
            with open(os.path.join(self.output_dir, "page_text.txt"), "w", encoding="utf-8") as f:
                f.write(all_text)

            # Try to find any structured content
            potential_data = soup.find_all(text=True)
            meaningful_text = [text.strip() for text in potential_data if text.strip() and len(text.strip()) > 3]

            if meaningful_text:
                # Group text into potential records
                for i, text in enumerate(meaningful_text[:100]):  # Limit to first 100 items
                    if any(char.isdigit() for char in text):  # Only include text with numbers
                        row_data = {
                            'text_content': text,
                            'text_index': i + 1,
                            'extraction_time': datetime.now().isoformat()
                        }
                        self.data.append(row_data)

            return len(self.data) > 0

        except Exception as e:
            self.logger.error(f"Error in general data extraction: {str(e)}")
            return False

    def handle_pagination(self):
        """Handle pagination if present on the website."""
        try:
            # Look for pagination elements
            pagination_elements = self.driver.find_elements(By.XPATH, "//a[contains(@class, 'page') or contains(text(), '下一页') or contains(text(), 'Next')]")

            if pagination_elements:
                self.logger.info("Pagination detected, attempting to navigate through pages")
                page_count = 1

                while page_count < self.max_pages:
                    self.logger.info(f"Processing page {page_count}")

                    # Extract data from current page
                    if not self.extract_data():
                        self.logger.warning(f"No data extracted from page {page_count}")

                    # Try to find and click next page button
                    try:
                        next_button = self.driver.find_element(By.XPATH, "//a[contains(text(), '下一页') or contains(text(), 'Next') or contains(@class, 'next')]")
                        if next_button.is_enabled():
                            next_button.click()
                            time.sleep(self.delay_between_pages)  # Wait for page to load
                            page_count += 1
                        else:
                            self.logger.info("Reached last page")
                            break
                    except NoSuchElementException:
                        self.logger.info("No more pages found")
                        break

            else:
                self.logger.info("No pagination detected, processing single page")
                return self.extract_data()

            return True

        except Exception as e:
            self.logger.error(f"Error handling pagination: {str(e)}")
            return False

    def clean_data(self):
        """Clean and validate extracted data."""
        try:
            if not self.data:
                self.logger.warning("No data to clean")
                return False

            self.logger.info(f"Cleaning {len(self.data)} records")

            cleaned_data = []
            for record in self.data:
                # Remove empty values and clean text
                cleaned_record = {}
                for key, value in record.items():
                    if value and str(value).strip():
                        # Clean the value
                        cleaned_value = str(value).strip()
                        # Remove extra whitespace
                        cleaned_value = ' '.join(cleaned_value.split())
                        cleaned_record[key] = cleaned_value

                if cleaned_record:  # Only keep non-empty records
                    cleaned_data.append(cleaned_record)

            self.data = cleaned_data
            self.logger.info(f"Data cleaning completed. {len(self.data)} records retained")
            return True

        except Exception as e:
            self.logger.error(f"Error cleaning data: {str(e)}")
            return False

    def save_to_files(self):
        """Save extracted data to CSV and Excel files."""
        try:
            if not self.data:
                self.logger.warning("No data to save")
                return False

            # Create DataFrame
            df = pd.DataFrame(self.data)

            # Generate timestamp for filenames
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

            files_saved = []

            # Save to CSV
            if config.SAVE_CSV:
                csv_filename = os.path.join(self.output_dir, f"lottery_data_{timestamp}.csv")
                df.to_csv(csv_filename, index=False, encoding='utf-8-sig')
                self.logger.info(f"Data saved to CSV: {csv_filename}")
                files_saved.append(f"CSV: {csv_filename}")

            # Save to Excel
            if config.SAVE_EXCEL:
                excel_filename = os.path.join(self.output_dir, f"lottery_data_{timestamp}.xlsx")
                df.to_excel(excel_filename, index=False, engine='openpyxl')
                self.logger.info(f"Data saved to Excel: {excel_filename}")
                files_saved.append(f"Excel: {excel_filename}")

            # Save to JSON for backup
            if config.SAVE_JSON:
                json_filename = os.path.join(self.output_dir, f"lottery_data_{timestamp}.json")
                with open(json_filename, 'w', encoding='utf-8') as f:
                    json.dump(self.data, f, ensure_ascii=False, indent=2)
                self.logger.info(f"Data saved to JSON: {json_filename}")
                files_saved.append(f"JSON: {json_filename}")

            # Print summary
            print(f"\n=== SCRAPING SUMMARY ===")
            print(f"Total records extracted: {len(self.data)}")
            print(f"Files saved:")
            for file_info in files_saved:
                print(f"  - {file_info}")
            print(f"========================\n")

            return True

        except Exception as e:
            self.logger.error(f"Error saving data: {str(e)}")
            return False

    def run(self):
        """Main method to run the scraping process."""
        try:
            self.logger.info("Starting lottery data scraping process")

            # Setup WebDriver
            if not self.setup_driver():
                return False

            # Load the webpage
            if not self.load_page():
                return False

            # Handle pagination and extract data
            if not self.handle_pagination():
                return False

            # Clean the extracted data
            if not self.clean_data():
                return False

            # Save data to files
            if not self.save_to_files():
                return False

            self.logger.info("Scraping process completed successfully")
            return True

        except Exception as e:
            self.logger.error(f"Error in main scraping process: {str(e)}")
            return False

        finally:
            # Clean up WebDriver
            if self.driver:
                self.driver.quit()
                self.logger.info("WebDriver closed")


def main():
    """Main function to run the scraper."""
    # Target URL
    url = "https://wid8-baidu630.gabd11133ff.com/pages/historyRecord/lotteryRecord/index?route=aomen&"

    # Create scraper instance
    scraper = LotteryScraper(url)

    # Run the scraping process
    success = scraper.run()

    if success:
        print("✅ Scraping completed successfully!")
    else:
        print("❌ Scraping failed. Check the log files for details.")


if __name__ == "__main__":
    main()
