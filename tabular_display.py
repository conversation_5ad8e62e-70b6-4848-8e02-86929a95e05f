#!/usr/bin/env python3
"""
Tabular Display Interface for Lottery Data and Predictions.
"""

import os
import json
import logging
import csv
from datetime import datetime
from flask import Flask, request, jsonify
import sqlite3

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)

class LotteryTabularDisplay:
    """Tabular display system for lottery data."""
    
    def __init__(self):
        """Initialize the tabular display system."""
        self.data = None
        self.predictions = None
        self.load_data()
        self.load_predictions()
    
    def load_data(self):
        """Load lottery data from CSV file."""
        try:
            data_file = "output/lottery_data_20250704_181955.csv"
            if os.path.exists(data_file):
                self.data = []
                with open(data_file, 'r', encoding='utf-8') as f:
                    reader = csv.DictReader(f)
                    for row in reader:
                        self.data.append(row)
                logger.info(f"✅ Loaded {len(self.data)} lottery records")
            else:
                logger.warning("⚠️ No lottery data file found")
                self.data = []
        except Exception as e:
            logger.error(f"❌ Error loading data: {e}")
            self.data = []
    
    def load_predictions(self):
        """Load latest predictions."""
        try:
            predictions_dir = "output/predictions"
            if os.path.exists(predictions_dir):
                # Find latest prediction file
                prediction_files = [f for f in os.listdir(predictions_dir) if f.startswith('prediction_report_') and f.endswith('.json')]
                if prediction_files:
                    latest_file = sorted(prediction_files)[-1]
                    with open(os.path.join(predictions_dir, latest_file), 'r', encoding='utf-8') as f:
                        self.predictions = json.load(f)
                    logger.info(f"✅ Loaded predictions from {latest_file}")
                else:
                    logger.warning("⚠️ No prediction files found")
                    self.predictions = None
            else:
                logger.warning("⚠️ No predictions directory found")
                self.predictions = None
        except Exception as e:
            logger.error(f"❌ Error loading predictions: {e}")
            self.predictions = None
    
    def format_lottery_data(self):
        """Format lottery data for tabular display."""
        try:
            if not self.data:
                return []

            formatted_data = []
            for row in self.data:
                # Extract period number
                period_str = str(row.get('period', row.get('期数', '')))
                period_num = ''.join(filter(str.isdigit, period_str)) if period_str else '0'

                # Extract date
                date_str = str(row.get('date', row.get('日期', '')))

                # Extract numbers
                numbers = []
                for i in range(1, 8):
                    num_col = f'number_{i}'
                    if num_col in row and row[num_col] and row[num_col].strip():
                        try:
                            numbers.append(f"{int(float(row[num_col])):02d}")
                        except (ValueError, TypeError):
                            continue

                # Format regular numbers and special number
                if len(numbers) >= 6:
                    regular_numbers = ', '.join(numbers[:6])
                    special_number = numbers[6] if len(numbers) > 6 else numbers[-1]
                else:
                    regular_numbers = "数据不完整"
                    special_number = "N/A"

                formatted_data.append({
                    'period': f"{period_num}期",
                    'date': date_str,
                    'regular_numbers': regular_numbers,
                    'special_number': special_number
                })

            # Sort by period number (descending - newest first)
            formatted_data.sort(key=lambda x: int(x['period'].replace('期', '')), reverse=True)

            return formatted_data

        except Exception as e:
            logger.error(f"❌ Error formatting data: {e}")
            return []
    
    def format_prediction_data(self):
        """Format prediction data for display."""
        try:
            if not self.predictions:
                return None
            
            ensemble = self.predictions.get('ensemble_prediction', {})
            regular_numbers = ensemble.get('regular_numbers', [])
            special_number = ensemble.get('special_number', 0)
            confidence = ensemble.get('confidence_score', 0)
            
            # Format numbers with leading zeros
            formatted_regular = ', '.join([f"{num:02d}" for num in regular_numbers])
            formatted_special = f"{special_number:02d}"
            
            prediction_data = {
                'period': f"{self.predictions.get('next_period', 'N/A')}期",
                'date': self.predictions.get('prediction_date', 'N/A'),
                'regular_numbers': formatted_regular,
                'special_number': formatted_special,
                'confidence': f"{confidence:.1%}",
                'strategies': self.predictions.get('individual_strategies', {})
            }
            
            return prediction_data
            
        except Exception as e:
            logger.error(f"❌ Error formatting predictions: {e}")
            return None

# Create display instance
display = LotteryTabularDisplay()

@app.route('/')
def index():
    """Main page with tabular display."""
    try:
        # Get formatted data
        lottery_data = display.format_lottery_data()
        prediction_data = display.format_prediction_data()
        
        # Create HTML template
        html_template = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>彩票数据分析系统 - 表格显示</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 30px;
        }
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
            font-size: 2.5em;
        }
        h2 {
            color: #444;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
            margin-top: 40px;
        }
        .prediction-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
        }
        .prediction-section h2 {
            color: white;
            border-bottom: 2px solid white;
        }
        .prediction-highlight {
            font-size: 1.5em;
            font-weight: bold;
            text-align: center;
            margin: 20px 0;
            padding: 15px;
            background-color: rgba(255,255,255,0.2);
            border-radius: 8px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            font-size: 14px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: center;
        }
        th {
            background-color: #007bff;
            color: white;
            font-weight: bold;
            position: sticky;
            top: 0;
        }
        tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        tr:hover {
            background-color: #e3f2fd;
        }
        .period-col {
            font-weight: bold;
            color: #007bff;
        }
        .numbers-col {
            font-family: 'Courier New', monospace;
            font-weight: bold;
            color: #d32f2f;
        }
        .special-col {
            font-family: 'Courier New', monospace;
            font-weight: bold;
            color: #ff6f00;
            background-color: #fff3e0;
        }
        .confidence {
            color: #4caf50;
            font-weight: bold;
        }
        .stats {
            display: flex;
            justify-content: space-around;
            margin: 20px 0;
            padding: 20px;
            background-color: #f8f9fa;
            border-radius: 8px;
        }
        .stat-item {
            text-align: center;
        }
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #007bff;
        }
        .stat-label {
            color: #666;
            margin-top: 5px;
        }
        .refresh-btn {
            background-color: #28a745;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
        }
        .refresh-btn:hover {
            background-color: #218838;
        }
        .timestamp {
            text-align: center;
            color: #666;
            font-size: 0.9em;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 彩票数据分析系统</h1>
        
        <div class="stats">
            <div class="stat-item">
                <div class="stat-number">{{ total_records }}</div>
                <div class="stat-label">历史记录</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">{{ latest_period }}</div>
                <div class="stat-label">最新期数</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">{{ prediction_confidence }}</div>
                <div class="stat-label">预测信心度</div>
            </div>
        </div>
        
        {% if prediction_data %}
        <div class="prediction-section">
            <h2>🔮 下期预测</h2>
            <div class="prediction-highlight">
                <div>{{ prediction_data.period }} | {{ prediction_data.date }}</div>
                <div style="margin: 10px 0;">
                    开奖号码(平码): {{ prediction_data.regular_numbers }} | 
                    特别号码(特码): {{ prediction_data.special_number }}
                </div>
                <div class="confidence">预测信心度: {{ prediction_data.confidence }}</div>
            </div>
        </div>
        {% endif %}
        
        <h2>📊 历史开奖记录</h2>
        <button class="refresh-btn" onclick="location.reload()">🔄 刷新数据</button>
        
        <table>
            <thead>
                <tr>
                    <th>期数</th>
                    <th>日期</th>
                    <th>开奖号码(平码)</th>
                    <th>特别号码(特码)</th>
                </tr>
            </thead>
            <tbody>
                {% for record in lottery_data %}
                <tr>
                    <td class="period-col">{{ record.period }}</td>
                    <td>{{ record.date }}</td>
                    <td class="numbers-col">{{ record.regular_numbers }}</td>
                    <td class="special-col">{{ record.special_number }}</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
        
        <div class="timestamp">
            最后更新时间: {{ current_time }}
        </div>
    </div>
    
    <script>
        // Auto-refresh every 5 minutes
        setTimeout(function() {
            location.reload();
        }, 300000);
    </script>
</body>
</html>
        """
        
        # Calculate statistics
        total_records = len(lottery_data)
        latest_period = lottery_data[0]['period'] if lottery_data else "N/A"
        prediction_confidence = prediction_data['confidence'] if prediction_data else "N/A"
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        # Render template with Jinja2-like replacement
        html_content = html_template
        html_content = html_content.replace('{{ total_records }}', str(total_records))
        html_content = html_content.replace('{{ latest_period }}', latest_period)
        html_content = html_content.replace('{{ prediction_confidence }}', prediction_confidence)
        html_content = html_content.replace('{{ current_time }}', current_time)
        
        # Handle prediction data
        if prediction_data:
            prediction_section = f"""
            <div class="prediction-section">
                <h2>🔮 下期预测</h2>
                <div class="prediction-highlight">
                    <div>{prediction_data['period']} | {prediction_data['date']}</div>
                    <div style="margin: 10px 0;">
                        开奖号码(平码): {prediction_data['regular_numbers']} | 
                        特别号码(特码): {prediction_data['special_number']}
                    </div>
                    <div class="confidence">预测信心度: {prediction_data['confidence']}</div>
                </div>
            </div>
            """
            html_content = html_content.replace('{% if prediction_data %}', '')
            html_content = html_content.replace('{% endif %}', '')
        else:
            # Remove prediction section
            start_marker = '{% if prediction_data %}'
            end_marker = '{% endif %}'
            start_idx = html_content.find(start_marker)
            end_idx = html_content.find(end_marker) + len(end_marker)
            if start_idx != -1 and end_idx != -1:
                html_content = html_content[:start_idx] + html_content[end_idx:]
        
        # Handle lottery data table
        table_rows = ""
        for record in lottery_data:
            table_rows += f"""
                <tr>
                    <td class="period-col">{record['period']}</td>
                    <td>{record['date']}</td>
                    <td class="numbers-col">{record['regular_numbers']}</td>
                    <td class="special-col">{record['special_number']}</td>
                </tr>
            """
        
        html_content = html_content.replace('{% for record in lottery_data %}', '')
        html_content = html_content.replace('{% endfor %}', table_rows)
        
        return html_content
        
    except Exception as e:
        logger.error(f"❌ Error rendering page: {e}")
        return f"<h1>Error: {e}</h1>"

@app.route('/api/data')
def api_data():
    """API endpoint for lottery data."""
    try:
        lottery_data = display.format_lottery_data()
        prediction_data = display.format_prediction_data()
        
        return jsonify({
            'lottery_data': lottery_data,
            'prediction_data': prediction_data,
            'timestamp': datetime.now().isoformat()
        })
    except Exception as e:
        logger.error(f"❌ API error: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/refresh')
def api_refresh():
    """API endpoint to refresh data."""
    try:
        display.load_data()
        display.load_predictions()
        return jsonify({'status': 'success', 'message': 'Data refreshed successfully'})
    except Exception as e:
        logger.error(f"❌ Refresh error: {e}")
        return jsonify({'error': str(e)}), 500

def main():
    """Main function to run the tabular display server."""
    try:
        print("🚀 Starting Lottery Tabular Display System...")
        print("📊 Loading data and predictions...")
        
        # Display summary
        lottery_data = display.format_lottery_data()
        prediction_data = display.format_prediction_data()
        
        print(f"✅ Loaded {len(lottery_data)} lottery records")
        if prediction_data:
            print(f"🔮 Next prediction: {prediction_data['period']} - {prediction_data['regular_numbers']} | {prediction_data['special_number']}")
        else:
            print("⚠️ No predictions available")
        
        print("\n🌐 Starting web server...")
        print("📱 Access the tabular display at: http://localhost:5001")
        print("🔗 API endpoint: http://localhost:5001/api/data")
        print("🔄 Refresh endpoint: http://localhost:5001/api/refresh")
        print("\n⏹️ Press Ctrl+C to stop the server")
        
        app.run(host='0.0.0.0', port=5001, debug=False)
        
    except KeyboardInterrupt:
        print("\n👋 Server stopped by user")
    except Exception as e:
        print(f"❌ Error starting server: {e}")

if __name__ == "__main__":
    main()
