#!/usr/bin/env python3
"""
Advanced Lottery Data Analysis Engine - Multiple Algorithm Implementation.
"""

import os
import pandas as pd
import numpy as np
import json
import logging
from datetime import datetime, timedelta
from collections import Counter, defaultdict
from itertools import combinations
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.linear_model import LinearRegression
from sklearn.model_selection import train_test_split, cross_val_score
from sklearn.metrics import mean_squared_error, mean_absolute_error
from sklearn.preprocessing import StandardScaler
import warnings
warnings.filterwarnings('ignore')

# Set Chinese font for matplotlib
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

class AdvancedLotteryAnalyzer:
    """Advanced lottery data analyzer with multiple algorithms."""
    
    def __init__(self, data_file=None):
        """Initialize the advanced analyzer."""
        self.setup_logging()
        self.data = None
        self.analysis_results = {}
        self.prediction_models = {}
        
        if data_file:
            self.load_data(data_file)
    
    def setup_logging(self):
        """Setup logging configuration."""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('logs/advanced_analyzer.log', encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger('AdvancedAnalyzer')
        
        # Create logs directory
        os.makedirs('logs', exist_ok=True)
    
    def load_data(self, data_file):
        """Load lottery data from file."""
        try:
            if data_file.endswith('.csv'):
                self.data = pd.read_csv(data_file)
            elif data_file.endswith('.json'):
                with open(data_file, 'r', encoding='utf-8') as f:
                    json_data = json.load(f)
                self.data = pd.DataFrame(json_data)
            else:
                raise ValueError(f"Unsupported file format: {data_file}")
            
            self.logger.info(f"✅ Loaded {len(self.data)} records from {data_file}")
            self.preprocess_data()
            
        except Exception as e:
            self.logger.error(f"❌ Error loading data: {e}")
            raise
    
    def preprocess_data(self):
        """Preprocess the data for analysis."""
        try:
            # Extract individual numbers
            numbers_data = []
            for _, row in self.data.iterrows():
                # Extract period number
                period_str = str(row.get('period', row.get('期数', '')))
                period_num = int(''.join(filter(str.isdigit, period_str))) if period_str else 0
                
                # Extract date
                date_str = str(row.get('date', row.get('日期', '')))
                
                # Extract numbers from different possible columns
                numbers = []
                for i in range(1, 8):  # Up to 7 numbers
                    num_col = f'number_{i}'
                    if num_col in row and pd.notna(row[num_col]):
                        numbers.append(int(row[num_col]))
                
                # If no individual numbers, try to parse from combined string
                if not numbers:
                    raw_numbers = str(row.get('raw_numbers', ''))
                    if raw_numbers:
                        # Extract numbers using regex
                        import re
                        found_numbers = re.findall(r'\b(\d{1,2})\b', raw_numbers)
                        numbers = [int(n) for n in found_numbers if 1 <= int(n) <= 49]
                
                if numbers and len(numbers) >= 6:
                    numbers_data.append({
                        'period': period_num,
                        'date': date_str,
                        'numbers': numbers[:6],  # Take first 6 as regular numbers
                        'special': numbers[6] if len(numbers) > 6 else numbers[-1],  # Last as special
                        'sum': sum(numbers[:6]),
                        'avg': np.mean(numbers[:6]),
                        'range': max(numbers[:6]) - min(numbers[:6]),
                        'even_count': sum(1 for n in numbers[:6] if n % 2 == 0),
                        'odd_count': sum(1 for n in numbers[:6] if n % 2 == 1)
                    })
            
            self.processed_data = pd.DataFrame(numbers_data)
            self.processed_data = self.processed_data.sort_values('period').reset_index(drop=True)
            
            self.logger.info(f"✅ Preprocessed {len(self.processed_data)} records")
            
        except Exception as e:
            self.logger.error(f"❌ Error preprocessing data: {e}")
            raise
    
    def frequency_analysis(self):
        """Perform comprehensive frequency analysis."""
        try:
            self.logger.info("🔍 Performing frequency analysis...")
            
            # Number frequency analysis
            all_numbers = []
            for _, row in self.processed_data.iterrows():
                all_numbers.extend(row['numbers'])
            
            number_freq = Counter(all_numbers)
            
            # Special number frequency
            special_freq = Counter(self.processed_data['special'].tolist())
            
            # Position-based frequency
            position_freq = {}
            for pos in range(6):
                position_numbers = [row['numbers'][pos] for _, row in self.processed_data.iterrows() if len(row['numbers']) > pos]
                position_freq[f'position_{pos+1}'] = Counter(position_numbers)
            
            # Combination frequency (pairs, triples)
            pair_freq = Counter()
            triple_freq = Counter()
            
            for _, row in self.processed_data.iterrows():
                numbers = row['numbers']
                # Pairs
                for pair in combinations(numbers, 2):
                    pair_freq[tuple(sorted(pair))] += 1
                # Triples
                for triple in combinations(numbers, 3):
                    triple_freq[tuple(sorted(triple))] += 1
            
            self.analysis_results['frequency'] = {
                'number_frequency': dict(number_freq),
                'special_frequency': dict(special_freq),
                'position_frequency': {k: dict(v) for k, v in position_freq.items()},
                'pair_frequency': dict(pair_freq.most_common(20)),
                'triple_frequency': dict(triple_freq.most_common(10)),
                'hot_numbers': [num for num, _ in number_freq.most_common(10)],
                'cold_numbers': [num for num, _ in number_freq.most_common()[-10:]],
            }
            
            self.logger.info("✅ Frequency analysis completed")
            
        except Exception as e:
            self.logger.error(f"❌ Error in frequency analysis: {e}")
    
    def markov_chain_analysis(self):
        """Perform Markov chain analysis for sequence prediction."""
        try:
            self.logger.info("🔗 Performing Markov chain analysis...")
            
            # Build transition matrices for different orders
            transition_matrices = {}
            
            # First-order Markov chain (current number -> next number)
            first_order = defaultdict(lambda: defaultdict(int))
            
            for i in range(len(self.processed_data) - 1):
                current_numbers = set(self.processed_data.iloc[i]['numbers'])
                next_numbers = set(self.processed_data.iloc[i + 1]['numbers'])
                
                for curr_num in current_numbers:
                    for next_num in next_numbers:
                        first_order[curr_num][next_num] += 1
            
            # Convert to probabilities
            first_order_prob = {}
            for curr_num, transitions in first_order.items():
                total = sum(transitions.values())
                first_order_prob[curr_num] = {
                    next_num: count / total 
                    for next_num, count in transitions.items()
                }
            
            # Second-order Markov chain (last two numbers -> next number)
            second_order = defaultdict(lambda: defaultdict(int))
            
            for i in range(len(self.processed_data) - 2):
                prev_numbers = tuple(sorted(self.processed_data.iloc[i]['numbers']))
                curr_numbers = tuple(sorted(self.processed_data.iloc[i + 1]['numbers']))
                next_numbers = set(self.processed_data.iloc[i + 2]['numbers'])
                
                state = (prev_numbers, curr_numbers)
                for next_num in next_numbers:
                    second_order[state][next_num] += 1
            
            self.analysis_results['markov'] = {
                'first_order_transitions': first_order_prob,
                'second_order_transitions': dict(second_order),
                'most_likely_transitions': {
                    num: max(transitions.items(), key=lambda x: x[1]) 
                    for num, transitions in first_order_prob.items() 
                    if transitions
                }
            }
            
            self.logger.info("✅ Markov chain analysis completed")
            
        except Exception as e:
            self.logger.error(f"❌ Error in Markov chain analysis: {e}")
    
    def time_series_analysis(self):
        """Perform time series analysis on lottery patterns."""
        try:
            self.logger.info("📈 Performing time series analysis...")
            
            # Analyze trends over time
            time_features = self.processed_data.copy()
            time_features['period_mod_7'] = time_features['period'] % 7  # Weekly pattern
            time_features['period_mod_30'] = time_features['period'] % 30  # Monthly pattern
            
            # Rolling statistics
            window_size = 5
            time_features['sum_rolling_mean'] = time_features['sum'].rolling(window=window_size).mean()
            time_features['avg_rolling_mean'] = time_features['avg'].rolling(window=window_size).mean()
            time_features['range_rolling_mean'] = time_features['range'].rolling(window=window_size).mean()
            
            # Seasonal patterns
            seasonal_patterns = {}
            
            # Weekly patterns
            weekly_stats = time_features.groupby('period_mod_7').agg({
                'sum': ['mean', 'std'],
                'avg': ['mean', 'std'],
                'even_count': 'mean',
                'odd_count': 'mean'
            }).round(2)
            
            seasonal_patterns['weekly'] = weekly_stats.to_dict()
            
            # Trend analysis
            from scipy import stats
            periods = time_features['period'].values
            sums = time_features['sum'].values
            
            slope, intercept, r_value, p_value, std_err = stats.linregress(periods, sums)
            
            trend_analysis = {
                'sum_trend_slope': slope,
                'sum_trend_r_squared': r_value ** 2,
                'sum_trend_p_value': p_value,
                'trend_direction': 'increasing' if slope > 0 else 'decreasing'
            }
            
            self.analysis_results['time_series'] = {
                'seasonal_patterns': seasonal_patterns,
                'trend_analysis': trend_analysis,
                'rolling_statistics': time_features[['period', 'sum_rolling_mean', 'avg_rolling_mean', 'range_rolling_mean']].to_dict('records')
            }
            
            self.logger.info("✅ Time series analysis completed")
            
        except Exception as e:
            self.logger.error(f"❌ Error in time series analysis: {e}")
    
    def machine_learning_analysis(self):
        """Perform machine learning analysis and build prediction models."""
        try:
            self.logger.info("🤖 Performing machine learning analysis...")
            
            # Prepare features
            features_df = self.processed_data.copy()
            
            # Create lag features
            for lag in [1, 2, 3]:
                features_df[f'sum_lag_{lag}'] = features_df['sum'].shift(lag)
                features_df[f'avg_lag_{lag}'] = features_df['avg'].shift(lag)
                features_df[f'range_lag_{lag}'] = features_df['range'].shift(lag)
                features_df[f'even_count_lag_{lag}'] = features_df['even_count'].shift(lag)
            
            # Create moving averages
            for window in [3, 5, 7]:
                features_df[f'sum_ma_{window}'] = features_df['sum'].rolling(window=window).mean()
                features_df[f'avg_ma_{window}'] = features_df['avg'].rolling(window=window).mean()
            
            # Drop rows with NaN values
            features_df = features_df.dropna()
            
            if len(features_df) < 10:
                self.logger.warning("⚠️ Insufficient data for ML analysis")
                return
            
            # Define features and targets
            feature_columns = [col for col in features_df.columns if 'lag' in col or 'ma' in col or col == 'period']
            X = features_df[feature_columns]
            
            # Multiple prediction targets
            targets = {
                'sum': features_df['sum'],
                'avg': features_df['avg'],
                'range': features_df['range'],
                'even_count': features_df['even_count']
            }
            
            models = {
                'RandomForest': RandomForestRegressor(n_estimators=100, random_state=42),
                'GradientBoosting': GradientBoostingRegressor(n_estimators=100, random_state=42),
                'LinearRegression': LinearRegression()
            }
            
            ml_results = {}
            
            for target_name, y in targets.items():
                ml_results[target_name] = {}
                
                # Split data
                X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.3, random_state=42)
                
                # Scale features
                scaler = StandardScaler()
                X_train_scaled = scaler.fit_transform(X_train)
                X_test_scaled = scaler.transform(X_test)
                
                for model_name, model in models.items():
                    try:
                        # Train model
                        if model_name == 'LinearRegression':
                            model.fit(X_train_scaled, y_train)
                            y_pred = model.predict(X_test_scaled)
                        else:
                            model.fit(X_train, y_train)
                            y_pred = model.predict(X_test)
                        
                        # Evaluate model
                        mse = mean_squared_error(y_test, y_pred)
                        mae = mean_absolute_error(y_test, y_pred)
                        
                        # Cross-validation
                        cv_scores = cross_val_score(model, X_train, y_train, cv=3, scoring='neg_mean_squared_error')
                        
                        ml_results[target_name][model_name] = {
                            'mse': mse,
                            'mae': mae,
                            'rmse': np.sqrt(mse),
                            'cv_score_mean': -cv_scores.mean(),
                            'cv_score_std': cv_scores.std()
                        }
                        
                        # Store best model
                        if target_name not in self.prediction_models:
                            self.prediction_models[target_name] = {}
                        
                        self.prediction_models[target_name][model_name] = {
                            'model': model,
                            'scaler': scaler if model_name == 'LinearRegression' else None,
                            'features': feature_columns
                        }
                        
                    except Exception as e:
                        self.logger.warning(f"⚠️ Model {model_name} failed for target {target_name}: {e}")
                        continue
            
            self.analysis_results['machine_learning'] = ml_results
            
            self.logger.info("✅ Machine learning analysis completed")
            
        except Exception as e:
            self.logger.error(f"❌ Error in machine learning analysis: {e}")
    
    def pattern_analysis(self):
        """Analyze complex patterns in lottery data."""
        try:
            self.logger.info("🔍 Performing pattern analysis...")
            
            patterns = {}
            
            # Consecutive numbers pattern
            consecutive_counts = []
            for _, row in self.processed_data.iterrows():
                numbers = sorted(row['numbers'])
                consecutive = 0
                max_consecutive = 0
                
                for i in range(1, len(numbers)):
                    if numbers[i] == numbers[i-1] + 1:
                        consecutive += 1
                        max_consecutive = max(max_consecutive, consecutive + 1)
                    else:
                        consecutive = 0
                
                consecutive_counts.append(max_consecutive)
            
            patterns['consecutive'] = {
                'average_consecutive': np.mean(consecutive_counts),
                'max_consecutive_seen': max(consecutive_counts),
                'consecutive_distribution': dict(Counter(consecutive_counts))
            }
            
            # Sum ranges analysis
            sum_ranges = {
                'low': (0, 100),
                'medium': (100, 150),
                'high': (150, 300)
            }
            
            sum_distribution = {}
            for range_name, (low, high) in sum_ranges.items():
                count = sum(1 for s in self.processed_data['sum'] if low <= s < high)
                sum_distribution[range_name] = count
            
            patterns['sum_ranges'] = sum_distribution
            
            # Even/Odd patterns
            even_odd_patterns = Counter()
            for _, row in self.processed_data.iterrows():
                pattern = f"{row['even_count']}E{row['odd_count']}O"
                even_odd_patterns[pattern] += 1
            
            patterns['even_odd'] = dict(even_odd_patterns.most_common())
            
            # Number gap analysis
            gap_analysis = []
            for _, row in self.processed_data.iterrows():
                numbers = sorted(row['numbers'])
                gaps = [numbers[i+1] - numbers[i] for i in range(len(numbers)-1)]
                gap_analysis.extend(gaps)
            
            patterns['gaps'] = {
                'average_gap': np.mean(gap_analysis),
                'gap_distribution': dict(Counter(gap_analysis).most_common(10))
            }
            
            self.analysis_results['patterns'] = patterns
            
            self.logger.info("✅ Pattern analysis completed")
            
        except Exception as e:
            self.logger.error(f"❌ Error in pattern analysis: {e}")
    
    def run_comprehensive_analysis(self):
        """Run all analysis methods."""
        try:
            self.logger.info("🚀 Starting comprehensive analysis...")
            
            if self.processed_data is None or len(self.processed_data) == 0:
                self.logger.error("❌ No data available for analysis")
                return False
            
            # Run all analysis methods
            self.frequency_analysis()
            self.markov_chain_analysis()
            self.time_series_analysis()
            self.machine_learning_analysis()
            self.pattern_analysis()
            
            # Generate summary report
            self.generate_analysis_report()
            
            self.logger.info("🎉 Comprehensive analysis completed!")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Error in comprehensive analysis: {e}")
            return False
    
    def generate_analysis_report(self):
        """Generate comprehensive analysis report."""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            
            # Create output directory
            os.makedirs('output/analysis', exist_ok=True)
            
            # Convert tuples to strings for JSON serialization
            def convert_tuples(obj):
                if isinstance(obj, dict):
                    return {str(k) if isinstance(k, tuple) else k: convert_tuples(v) for k, v in obj.items()}
                elif isinstance(obj, list):
                    return [convert_tuples(item) for item in obj]
                elif isinstance(obj, tuple):
                    return str(obj)
                else:
                    return obj

            serializable_results = convert_tuples(self.analysis_results)

            # Save detailed results
            report_file = f"output/analysis/comprehensive_analysis_{timestamp}.json"
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(serializable_results, f, ensure_ascii=False, indent=2, default=str)
            
            # Generate summary
            summary = {
                'analysis_timestamp': timestamp,
                'data_records': len(self.processed_data),
                'analysis_methods': list(self.analysis_results.keys()),
                'key_insights': self.extract_key_insights()
            }
            
            summary_file = f"output/analysis/analysis_summary_{timestamp}.json"
            with open(summary_file, 'w', encoding='utf-8') as f:
                json.dump(summary, f, ensure_ascii=False, indent=2)
            
            self.logger.info(f"✅ Analysis report saved:")
            self.logger.info(f"   📄 Detailed: {report_file}")
            self.logger.info(f"   📋 Summary: {summary_file}")
            
        except Exception as e:
            self.logger.error(f"❌ Error generating report: {e}")
    
    def extract_key_insights(self):
        """Extract key insights from analysis results."""
        insights = {}
        
        try:
            # Frequency insights
            if 'frequency' in self.analysis_results:
                freq_data = self.analysis_results['frequency']
                insights['hot_numbers'] = freq_data.get('hot_numbers', [])
                insights['cold_numbers'] = freq_data.get('cold_numbers', [])
                insights['most_common_pairs'] = list(freq_data.get('pair_frequency', {}).keys())[:5]
            
            # Pattern insights
            if 'patterns' in self.analysis_results:
                pattern_data = self.analysis_results['patterns']
                insights['average_consecutive'] = pattern_data.get('consecutive', {}).get('average_consecutive', 0)
                insights['most_common_even_odd'] = max(pattern_data.get('even_odd', {}).items(), key=lambda x: x[1])[0] if pattern_data.get('even_odd') else None
            
            # Time series insights
            if 'time_series' in self.analysis_results:
                ts_data = self.analysis_results['time_series']
                trend = ts_data.get('trend_analysis', {})
                insights['sum_trend'] = trend.get('trend_direction', 'unknown')
                insights['trend_strength'] = trend.get('sum_trend_r_squared', 0)
            
            # ML insights
            if 'machine_learning' in self.analysis_results:
                ml_data = self.analysis_results['machine_learning']
                best_models = {}
                for target, models in ml_data.items():
                    if models:
                        best_model = min(models.items(), key=lambda x: x[1].get('mse', float('inf')))
                        best_models[target] = best_model[0]
                insights['best_prediction_models'] = best_models
            
        except Exception as e:
            self.logger.warning(f"⚠️ Error extracting insights: {e}")
        
        return insights

def main():
    """Main function to run advanced analysis."""
    # Use existing data file
    data_file = "output/lottery_data_20250704_181955.csv"
    
    if not os.path.exists(data_file):
        print(f"❌ Data file not found: {data_file}")
        return False
    
    # Create analyzer
    analyzer = AdvancedLotteryAnalyzer(data_file)
    
    # Run comprehensive analysis
    success = analyzer.run_comprehensive_analysis()
    
    if success:
        print("✅ Advanced analysis completed successfully!")
        print("📊 Check output/analysis/ directory for detailed results")
    else:
        print("❌ Advanced analysis failed!")
    
    return success

if __name__ == "__main__":
    main()
