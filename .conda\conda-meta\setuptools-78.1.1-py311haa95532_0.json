{"build": "py311haa95532_0", "build_number": 0, "channel": "https://repo.anaconda.com/pkgs/main/win-64", "constrains": [], "depends": ["python >=3.11,<3.12.0a0"], "extracted_package_dir": "C:\\Users\\<USER>\\anaconda3\\pkgs\\setuptools-78.1.1-py311haa95532_0", "files": ["Lib/site-packages/_distutils_hack/__init__.py", "Lib/site-packages/_distutils_hack/__pycache__/__init__.cpython-311.pyc", "Lib/site-packages/_distutils_hack/__pycache__/override.cpython-311.pyc", "Lib/site-packages/_distutils_hack/override.py", "Lib/site-packages/distutils-precedence.pth", "Lib/site-packages/pkg_resources/__init__.py", "Lib/site-packages/pkg_resources/__pycache__/__init__.cpython-311.pyc", "Lib/site-packages/pkg_resources/api_tests.txt", "Lib/site-packages/pkg_resources/py.typed", "Lib/site-packages/pkg_resources/tests/__init__.py", "Lib/site-packages/pkg_resources/tests/__pycache__/__init__.cpython-311.pyc", "Lib/site-packages/pkg_resources/tests/__pycache__/test_find_distributions.cpython-311.pyc", "Lib/site-packages/pkg_resources/tests/__pycache__/test_integration_zope_interface.cpython-311.pyc", "Lib/site-packages/pkg_resources/tests/__pycache__/test_markers.cpython-311.pyc", "Lib/site-packages/pkg_resources/tests/__pycache__/test_pkg_resources.cpython-311.pyc", "Lib/site-packages/pkg_resources/tests/__pycache__/test_resources.cpython-311.pyc", "Lib/site-packages/pkg_resources/tests/__pycache__/test_working_set.cpython-311.pyc", "Lib/site-packages/pkg_resources/tests/data/my-test-package-source/__pycache__/setup.cpython-311.pyc", "Lib/site-packages/pkg_resources/tests/data/my-test-package-source/setup.cfg", "Lib/site-packages/pkg_resources/tests/data/my-test-package-source/setup.py", "Lib/site-packages/pkg_resources/tests/data/my-test-package-zip/my-test-package.zip", "Lib/site-packages/pkg_resources/tests/data/my-test-package_unpacked-egg/my_test_package-1.0-py3.7.egg/EGG-INFO/PKG-INFO", "Lib/site-packages/pkg_resources/tests/data/my-test-package_unpacked-egg/my_test_package-1.0-py3.7.egg/EGG-INFO/SOURCES.txt", "Lib/site-packages/pkg_resources/tests/data/my-test-package_unpacked-egg/my_test_package-1.0-py3.7.egg/EGG-INFO/dependency_links.txt", "Lib/site-packages/pkg_resources/tests/data/my-test-package_unpacked-egg/my_test_package-1.0-py3.7.egg/EGG-INFO/top_level.txt", "Lib/site-packages/pkg_resources/tests/data/my-test-package_unpacked-egg/my_test_package-1.0-py3.7.egg/EGG-INFO/zip-safe", "Lib/site-packages/pkg_resources/tests/data/my-test-package_zipped-egg/my_test_package-1.0-py3.7.egg", "Lib/site-packages/pkg_resources/tests/test_find_distributions.py", "Lib/site-packages/pkg_resources/tests/test_integration_zope_interface.py", "Lib/site-packages/pkg_resources/tests/test_markers.py", "Lib/site-packages/pkg_resources/tests/test_pkg_resources.py", "Lib/site-packages/pkg_resources/tests/test_resources.py", "Lib/site-packages/pkg_resources/tests/test_working_set.py", "Lib/site-packages/setuptools-78.1.1-py3.11.egg-info/PKG-INFO", "Lib/site-packages/setuptools-78.1.1-py3.11.egg-info/SOURCES.txt", "Lib/site-packages/setuptools-78.1.1-py3.11.egg-info/dependency_links.txt", "Lib/site-packages/setuptools-78.1.1-py3.11.egg-info/entry_points.txt", "Lib/site-packages/setuptools-78.1.1-py3.11.egg-info/requires.txt", "Lib/site-packages/setuptools-78.1.1-py3.11.egg-info/top_level.txt", "Lib/site-packages/setuptools/__init__.py", "Lib/site-packages/setuptools/__pycache__/__init__.cpython-311.pyc", "Lib/site-packages/setuptools/__pycache__/_core_metadata.cpython-311.pyc", "Lib/site-packages/setuptools/__pycache__/_entry_points.cpython-311.pyc", "Lib/site-packages/setuptools/__pycache__/_imp.cpython-311.pyc", "Lib/site-packages/setuptools/__pycache__/_importlib.cpython-311.pyc", "Lib/site-packages/setuptools/__pycache__/_itertools.cpython-311.pyc", "Lib/site-packages/setuptools/__pycache__/_normalization.cpython-311.pyc", "Lib/site-packages/setuptools/__pycache__/_path.cpython-311.pyc", "Lib/site-packages/setuptools/__pycache__/_reqs.cpython-311.pyc", "Lib/site-packages/setuptools/__pycache__/_shutil.cpython-311.pyc", "Lib/site-packages/setuptools/__pycache__/_static.cpython-311.pyc", "Lib/site-packages/setuptools/__pycache__/archive_util.cpython-311.pyc", "Lib/site-packages/setuptools/__pycache__/build_meta.cpython-311.pyc", "Lib/site-packages/setuptools/__pycache__/depends.cpython-311.pyc", "Lib/site-packages/setuptools/__pycache__/discovery.cpython-311.pyc", "Lib/site-packages/setuptools/__pycache__/dist.cpython-311.pyc", "Lib/site-packages/setuptools/__pycache__/errors.cpython-311.pyc", "Lib/site-packages/setuptools/__pycache__/extension.cpython-311.pyc", "Lib/site-packages/setuptools/__pycache__/glob.cpython-311.pyc", "Lib/site-packages/setuptools/__pycache__/installer.cpython-311.pyc", "Lib/site-packages/setuptools/__pycache__/launch.cpython-311.pyc", "Lib/site-packages/setuptools/__pycache__/logging.cpython-311.pyc", "Lib/site-packages/setuptools/__pycache__/modified.cpython-311.pyc", "Lib/site-packages/setuptools/__pycache__/monkey.cpython-311.pyc", "Lib/site-packages/setuptools/__pycache__/msvc.cpython-311.pyc", "Lib/site-packages/setuptools/__pycache__/namespaces.cpython-311.pyc", "Lib/site-packages/setuptools/__pycache__/package_index.cpython-311.pyc", "Lib/site-packages/setuptools/__pycache__/sandbox.cpython-311.pyc", "Lib/site-packages/setuptools/__pycache__/unicode_utils.cpython-311.pyc", "Lib/site-packages/setuptools/__pycache__/version.cpython-311.pyc", "Lib/site-packages/setuptools/__pycache__/warnings.cpython-311.pyc", "Lib/site-packages/setuptools/__pycache__/wheel.cpython-311.pyc", "Lib/site-packages/setuptools/__pycache__/windows_support.cpython-311.pyc", "Lib/site-packages/setuptools/_core_metadata.py", "Lib/site-packages/setuptools/_distutils/__init__.py", "Lib/site-packages/setuptools/_distutils/__pycache__/__init__.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/_log.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/_macos_compat.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/_modified.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/_msvccompiler.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/archive_util.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/ccompiler.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/cmd.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/core.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/cygwinccompiler.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/debug.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/dep_util.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/dir_util.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/dist.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/errors.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/extension.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/fancy_getopt.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/file_util.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/filelist.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/log.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/spawn.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/sysconfig.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/text_file.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/unixccompiler.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/util.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/version.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/versionpredicate.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/zosccompiler.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/_log.py", "Lib/site-packages/setuptools/_distutils/_macos_compat.py", "Lib/site-packages/setuptools/_distutils/_modified.py", "Lib/site-packages/setuptools/_distutils/_msvccompiler.py", "Lib/site-packages/setuptools/_distutils/archive_util.py", "Lib/site-packages/setuptools/_distutils/ccompiler.py", "Lib/site-packages/setuptools/_distutils/cmd.py", "Lib/site-packages/setuptools/_distutils/command/__init__.py", "Lib/site-packages/setuptools/_distutils/command/__pycache__/__init__.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/command/__pycache__/_framework_compat.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/command/__pycache__/bdist.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/command/__pycache__/bdist_dumb.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/command/__pycache__/bdist_rpm.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/command/__pycache__/build.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/command/__pycache__/build_clib.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/command/__pycache__/build_ext.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/command/__pycache__/build_py.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/command/__pycache__/build_scripts.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/command/__pycache__/check.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/command/__pycache__/clean.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/command/__pycache__/config.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/command/__pycache__/install.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/command/__pycache__/install_data.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/command/__pycache__/install_egg_info.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/command/__pycache__/install_headers.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/command/__pycache__/install_lib.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/command/__pycache__/install_scripts.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/command/__pycache__/sdist.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/command/_framework_compat.py", "Lib/site-packages/setuptools/_distutils/command/bdist.py", "Lib/site-packages/setuptools/_distutils/command/bdist_dumb.py", "Lib/site-packages/setuptools/_distutils/command/bdist_rpm.py", "Lib/site-packages/setuptools/_distutils/command/build.py", "Lib/site-packages/setuptools/_distutils/command/build_clib.py", "Lib/site-packages/setuptools/_distutils/command/build_ext.py", "Lib/site-packages/setuptools/_distutils/command/build_py.py", "Lib/site-packages/setuptools/_distutils/command/build_scripts.py", "Lib/site-packages/setuptools/_distutils/command/check.py", "Lib/site-packages/setuptools/_distutils/command/clean.py", "Lib/site-packages/setuptools/_distutils/command/config.py", "Lib/site-packages/setuptools/_distutils/command/install.py", "Lib/site-packages/setuptools/_distutils/command/install_data.py", "Lib/site-packages/setuptools/_distutils/command/install_egg_info.py", "Lib/site-packages/setuptools/_distutils/command/install_headers.py", "Lib/site-packages/setuptools/_distutils/command/install_lib.py", "Lib/site-packages/setuptools/_distutils/command/install_scripts.py", "Lib/site-packages/setuptools/_distutils/command/sdist.py", "Lib/site-packages/setuptools/_distutils/compat/__init__.py", "Lib/site-packages/setuptools/_distutils/compat/__pycache__/__init__.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/compat/__pycache__/numpy.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/compat/__pycache__/py39.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/compat/numpy.py", "Lib/site-packages/setuptools/_distutils/compat/py39.py", "Lib/site-packages/setuptools/_distutils/compilers/C/__pycache__/base.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/compilers/C/__pycache__/cygwin.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/compilers/C/__pycache__/errors.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/compilers/C/__pycache__/msvc.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/compilers/C/__pycache__/unix.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/compilers/C/__pycache__/zos.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/compilers/C/base.py", "Lib/site-packages/setuptools/_distutils/compilers/C/cygwin.py", "Lib/site-packages/setuptools/_distutils/compilers/C/errors.py", "Lib/site-packages/setuptools/_distutils/compilers/C/msvc.py", "Lib/site-packages/setuptools/_distutils/compilers/C/tests/__pycache__/test_base.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/compilers/C/tests/__pycache__/test_cygwin.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/compilers/C/tests/__pycache__/test_mingw.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/compilers/C/tests/__pycache__/test_msvc.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/compilers/C/tests/__pycache__/test_unix.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/compilers/C/tests/test_base.py", "Lib/site-packages/setuptools/_distutils/compilers/C/tests/test_cygwin.py", "Lib/site-packages/setuptools/_distutils/compilers/C/tests/test_mingw.py", "Lib/site-packages/setuptools/_distutils/compilers/C/tests/test_msvc.py", "Lib/site-packages/setuptools/_distutils/compilers/C/tests/test_unix.py", "Lib/site-packages/setuptools/_distutils/compilers/C/unix.py", "Lib/site-packages/setuptools/_distutils/compilers/C/zos.py", "Lib/site-packages/setuptools/_distutils/core.py", "Lib/site-packages/setuptools/_distutils/cygwinccompiler.py", "Lib/site-packages/setuptools/_distutils/debug.py", "Lib/site-packages/setuptools/_distutils/dep_util.py", "Lib/site-packages/setuptools/_distutils/dir_util.py", "Lib/site-packages/setuptools/_distutils/dist.py", "Lib/site-packages/setuptools/_distutils/errors.py", "Lib/site-packages/setuptools/_distutils/extension.py", "Lib/site-packages/setuptools/_distutils/fancy_getopt.py", "Lib/site-packages/setuptools/_distutils/file_util.py", "Lib/site-packages/setuptools/_distutils/filelist.py", "Lib/site-packages/setuptools/_distutils/log.py", "Lib/site-packages/setuptools/_distutils/spawn.py", "Lib/site-packages/setuptools/_distutils/sysconfig.py", "Lib/site-packages/setuptools/_distutils/tests/__init__.py", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/__init__.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/support.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_archive_util.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_bdist.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_bdist_dumb.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_bdist_rpm.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_build.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_build_clib.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_build_ext.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_build_py.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_build_scripts.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_check.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_clean.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_cmd.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_config_cmd.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_core.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_dir_util.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_dist.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_extension.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_file_util.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_filelist.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_install.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_install_data.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_install_headers.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_install_lib.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_install_scripts.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_log.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_modified.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_sdist.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_spawn.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_sysconfig.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_text_file.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_util.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_version.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_versionpredicate.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/unix_compat.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/tests/compat/__init__.py", "Lib/site-packages/setuptools/_distutils/tests/compat/__pycache__/__init__.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/tests/compat/__pycache__/py39.cpython-311.pyc", "Lib/site-packages/setuptools/_distutils/tests/compat/py39.py", "Lib/site-packages/setuptools/_distutils/tests/support.py", "Lib/site-packages/setuptools/_distutils/tests/test_archive_util.py", "Lib/site-packages/setuptools/_distutils/tests/test_bdist.py", "Lib/site-packages/setuptools/_distutils/tests/test_bdist_dumb.py", "Lib/site-packages/setuptools/_distutils/tests/test_bdist_rpm.py", "Lib/site-packages/setuptools/_distutils/tests/test_build.py", "Lib/site-packages/setuptools/_distutils/tests/test_build_clib.py", "Lib/site-packages/setuptools/_distutils/tests/test_build_ext.py", "Lib/site-packages/setuptools/_distutils/tests/test_build_py.py", "Lib/site-packages/setuptools/_distutils/tests/test_build_scripts.py", "Lib/site-packages/setuptools/_distutils/tests/test_check.py", "Lib/site-packages/setuptools/_distutils/tests/test_clean.py", "Lib/site-packages/setuptools/_distutils/tests/test_cmd.py", "Lib/site-packages/setuptools/_distutils/tests/test_config_cmd.py", "Lib/site-packages/setuptools/_distutils/tests/test_core.py", "Lib/site-packages/setuptools/_distutils/tests/test_dir_util.py", "Lib/site-packages/setuptools/_distutils/tests/test_dist.py", "Lib/site-packages/setuptools/_distutils/tests/test_extension.py", "Lib/site-packages/setuptools/_distutils/tests/test_file_util.py", "Lib/site-packages/setuptools/_distutils/tests/test_filelist.py", "Lib/site-packages/setuptools/_distutils/tests/test_install.py", "Lib/site-packages/setuptools/_distutils/tests/test_install_data.py", "Lib/site-packages/setuptools/_distutils/tests/test_install_headers.py", "Lib/site-packages/setuptools/_distutils/tests/test_install_lib.py", "Lib/site-packages/setuptools/_distutils/tests/test_install_scripts.py", "Lib/site-packages/setuptools/_distutils/tests/test_log.py", "Lib/site-packages/setuptools/_distutils/tests/test_modified.py", "Lib/site-packages/setuptools/_distutils/tests/test_sdist.py", "Lib/site-packages/setuptools/_distutils/tests/test_spawn.py", "Lib/site-packages/setuptools/_distutils/tests/test_sysconfig.py", "Lib/site-packages/setuptools/_distutils/tests/test_text_file.py", "Lib/site-packages/setuptools/_distutils/tests/test_util.py", "Lib/site-packages/setuptools/_distutils/tests/test_version.py", "Lib/site-packages/setuptools/_distutils/tests/test_versionpredicate.py", "Lib/site-packages/setuptools/_distutils/tests/unix_compat.py", "Lib/site-packages/setuptools/_distutils/text_file.py", "Lib/site-packages/setuptools/_distutils/unixccompiler.py", "Lib/site-packages/setuptools/_distutils/util.py", "Lib/site-packages/setuptools/_distutils/version.py", "Lib/site-packages/setuptools/_distutils/versionpredicate.py", "Lib/site-packages/setuptools/_distutils/zosccompiler.py", "Lib/site-packages/setuptools/_entry_points.py", "Lib/site-packages/setuptools/_imp.py", "Lib/site-packages/setuptools/_importlib.py", "Lib/site-packages/setuptools/_itertools.py", "Lib/site-packages/setuptools/_normalization.py", "Lib/site-packages/setuptools/_path.py", "Lib/site-packages/setuptools/_reqs.py", "Lib/site-packages/setuptools/_shutil.py", "Lib/site-packages/setuptools/_static.py", "Lib/site-packages/setuptools/_vendor/__pycache__/typing_extensions.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/autocommand-2.2.2.dist-info/INSTALLER", "Lib/site-packages/setuptools/_vendor/autocommand-2.2.2.dist-info/LICENSE", "Lib/site-packages/setuptools/_vendor/autocommand-2.2.2.dist-info/METADATA", "Lib/site-packages/setuptools/_vendor/autocommand-2.2.2.dist-info/RECORD", "Lib/site-packages/setuptools/_vendor/autocommand-2.2.2.dist-info/WHEEL", "Lib/site-packages/setuptools/_vendor/autocommand-2.2.2.dist-info/top_level.txt", "Lib/site-packages/setuptools/_vendor/autocommand/__init__.py", "Lib/site-packages/setuptools/_vendor/autocommand/__pycache__/__init__.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/autocommand/__pycache__/autoasync.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/autocommand/__pycache__/autocommand.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/autocommand/__pycache__/automain.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/autocommand/__pycache__/autoparse.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/autocommand/__pycache__/errors.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/autocommand/autoasync.py", "Lib/site-packages/setuptools/_vendor/autocommand/autocommand.py", "Lib/site-packages/setuptools/_vendor/autocommand/automain.py", "Lib/site-packages/setuptools/_vendor/autocommand/autoparse.py", "Lib/site-packages/setuptools/_vendor/autocommand/errors.py", "Lib/site-packages/setuptools/_vendor/backports.tarfile-1.2.0.dist-info/INSTALLER", "Lib/site-packages/setuptools/_vendor/backports.tarfile-1.2.0.dist-info/LICENSE", "Lib/site-packages/setuptools/_vendor/backports.tarfile-1.2.0.dist-info/METADATA", "Lib/site-packages/setuptools/_vendor/backports.tarfile-1.2.0.dist-info/RECORD", "Lib/site-packages/setuptools/_vendor/backports.tarfile-1.2.0.dist-info/REQUESTED", "Lib/site-packages/setuptools/_vendor/backports.tarfile-1.2.0.dist-info/WHEEL", "Lib/site-packages/setuptools/_vendor/backports.tarfile-1.2.0.dist-info/top_level.txt", "Lib/site-packages/setuptools/_vendor/backports/__init__.py", "Lib/site-packages/setuptools/_vendor/backports/__pycache__/__init__.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/backports/tarfile/__init__.py", "Lib/site-packages/setuptools/_vendor/backports/tarfile/__main__.py", "Lib/site-packages/setuptools/_vendor/backports/tarfile/__pycache__/__init__.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/backports/tarfile/__pycache__/__main__.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/backports/tarfile/compat/__init__.py", "Lib/site-packages/setuptools/_vendor/backports/tarfile/compat/__pycache__/__init__.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/backports/tarfile/compat/__pycache__/py38.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/backports/tarfile/compat/py38.py", "Lib/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/INSTALLER", "Lib/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/LICENSE", "Lib/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/METADATA", "Lib/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/RECORD", "Lib/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/REQUESTED", "Lib/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/WHEEL", "Lib/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/top_level.txt", "Lib/site-packages/setuptools/_vendor/importlib_metadata/__init__.py", "Lib/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/__init__.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/_adapters.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/_collections.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/_compat.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/_functools.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/_itertools.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/_meta.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/_text.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/diagnose.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/importlib_metadata/_adapters.py", "Lib/site-packages/setuptools/_vendor/importlib_metadata/_collections.py", "Lib/site-packages/setuptools/_vendor/importlib_metadata/_compat.py", "Lib/site-packages/setuptools/_vendor/importlib_metadata/_functools.py", "Lib/site-packages/setuptools/_vendor/importlib_metadata/_itertools.py", "Lib/site-packages/setuptools/_vendor/importlib_metadata/_meta.py", "Lib/site-packages/setuptools/_vendor/importlib_metadata/_text.py", "Lib/site-packages/setuptools/_vendor/importlib_metadata/compat/__init__.py", "Lib/site-packages/setuptools/_vendor/importlib_metadata/compat/__pycache__/__init__.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/importlib_metadata/compat/__pycache__/py311.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/importlib_metadata/compat/__pycache__/py39.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/importlib_metadata/compat/py311.py", "Lib/site-packages/setuptools/_vendor/importlib_metadata/compat/py39.py", "Lib/site-packages/setuptools/_vendor/importlib_metadata/diagnose.py", "Lib/site-packages/setuptools/_vendor/importlib_metadata/py.typed", "Lib/site-packages/setuptools/_vendor/inflect-7.3.1.dist-info/INSTALLER", "Lib/site-packages/setuptools/_vendor/inflect-7.3.1.dist-info/LICENSE", "Lib/site-packages/setuptools/_vendor/inflect-7.3.1.dist-info/METADATA", "Lib/site-packages/setuptools/_vendor/inflect-7.3.1.dist-info/RECORD", "Lib/site-packages/setuptools/_vendor/inflect-7.3.1.dist-info/WHEEL", "Lib/site-packages/setuptools/_vendor/inflect-7.3.1.dist-info/top_level.txt", "Lib/site-packages/setuptools/_vendor/inflect/__init__.py", "Lib/site-packages/setuptools/_vendor/inflect/__pycache__/__init__.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/inflect/compat/__init__.py", "Lib/site-packages/setuptools/_vendor/inflect/compat/__pycache__/__init__.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/inflect/compat/__pycache__/py38.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/inflect/compat/py38.py", "Lib/site-packages/setuptools/_vendor/inflect/py.typed", "Lib/site-packages/setuptools/_vendor/jaraco.collections-5.1.0.dist-info/INSTALLER", "Lib/site-packages/setuptools/_vendor/jaraco.collections-5.1.0.dist-info/LICENSE", "Lib/site-packages/setuptools/_vendor/jaraco.collections-5.1.0.dist-info/METADATA", "Lib/site-packages/setuptools/_vendor/jaraco.collections-5.1.0.dist-info/RECORD", "Lib/site-packages/setuptools/_vendor/jaraco.collections-5.1.0.dist-info/REQUESTED", "Lib/site-packages/setuptools/_vendor/jaraco.collections-5.1.0.dist-info/WHEEL", "Lib/site-packages/setuptools/_vendor/jaraco.collections-5.1.0.dist-info/top_level.txt", "Lib/site-packages/setuptools/_vendor/jaraco.context-5.3.0.dist-info/INSTALLER", "Lib/site-packages/setuptools/_vendor/jaraco.context-5.3.0.dist-info/LICENSE", "Lib/site-packages/setuptools/_vendor/jaraco.context-5.3.0.dist-info/METADATA", "Lib/site-packages/setuptools/_vendor/jaraco.context-5.3.0.dist-info/RECORD", "Lib/site-packages/setuptools/_vendor/jaraco.context-5.3.0.dist-info/WHEEL", "Lib/site-packages/setuptools/_vendor/jaraco.context-5.3.0.dist-info/top_level.txt", "Lib/site-packages/setuptools/_vendor/jaraco.functools-4.0.1.dist-info/INSTALLER", "Lib/site-packages/setuptools/_vendor/jaraco.functools-4.0.1.dist-info/LICENSE", "Lib/site-packages/setuptools/_vendor/jaraco.functools-4.0.1.dist-info/METADATA", "Lib/site-packages/setuptools/_vendor/jaraco.functools-4.0.1.dist-info/RECORD", "Lib/site-packages/setuptools/_vendor/jaraco.functools-4.0.1.dist-info/WHEEL", "Lib/site-packages/setuptools/_vendor/jaraco.functools-4.0.1.dist-info/top_level.txt", "Lib/site-packages/setuptools/_vendor/jaraco.text-3.12.1.dist-info/INSTALLER", "Lib/site-packages/setuptools/_vendor/jaraco.text-3.12.1.dist-info/LICENSE", "Lib/site-packages/setuptools/_vendor/jaraco.text-3.12.1.dist-info/METADATA", "Lib/site-packages/setuptools/_vendor/jaraco.text-3.12.1.dist-info/RECORD", "Lib/site-packages/setuptools/_vendor/jaraco.text-3.12.1.dist-info/REQUESTED", "Lib/site-packages/setuptools/_vendor/jaraco.text-3.12.1.dist-info/WHEEL", "Lib/site-packages/setuptools/_vendor/jaraco.text-3.12.1.dist-info/top_level.txt", "Lib/site-packages/setuptools/_vendor/jaraco/__pycache__/context.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/jaraco/collections/__init__.py", "Lib/site-packages/setuptools/_vendor/jaraco/collections/__pycache__/__init__.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/jaraco/collections/py.typed", "Lib/site-packages/setuptools/_vendor/jaraco/context.py", "Lib/site-packages/setuptools/_vendor/jaraco/functools/__init__.py", "Lib/site-packages/setuptools/_vendor/jaraco/functools/__init__.pyi", "Lib/site-packages/setuptools/_vendor/jaraco/functools/__pycache__/__init__.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/jaraco/functools/py.typed", "Lib/site-packages/setuptools/_vendor/jaraco/text/Lorem ipsum.txt", "Lib/site-packages/setuptools/_vendor/jaraco/text/__init__.py", "Lib/site-packages/setuptools/_vendor/jaraco/text/__pycache__/__init__.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/jaraco/text/__pycache__/layouts.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/jaraco/text/__pycache__/show-newlines.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/jaraco/text/__pycache__/strip-prefix.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/jaraco/text/__pycache__/to-dvorak.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/jaraco/text/__pycache__/to-qwerty.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/jaraco/text/layouts.py", "Lib/site-packages/setuptools/_vendor/jaraco/text/show-newlines.py", "Lib/site-packages/setuptools/_vendor/jaraco/text/strip-prefix.py", "Lib/site-packages/setuptools/_vendor/jaraco/text/to-dvorak.py", "Lib/site-packages/setuptools/_vendor/jaraco/text/to-qwerty.py", "Lib/site-packages/setuptools/_vendor/more_itertools-10.3.0.dist-info/INSTALLER", "Lib/site-packages/setuptools/_vendor/more_itertools-10.3.0.dist-info/LICENSE", "Lib/site-packages/setuptools/_vendor/more_itertools-10.3.0.dist-info/METADATA", "Lib/site-packages/setuptools/_vendor/more_itertools-10.3.0.dist-info/RECORD", "Lib/site-packages/setuptools/_vendor/more_itertools-10.3.0.dist-info/REQUESTED", "Lib/site-packages/setuptools/_vendor/more_itertools-10.3.0.dist-info/WHEEL", "Lib/site-packages/setuptools/_vendor/more_itertools/__init__.py", "Lib/site-packages/setuptools/_vendor/more_itertools/__init__.pyi", "Lib/site-packages/setuptools/_vendor/more_itertools/__pycache__/__init__.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/more_itertools/__pycache__/more.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/more_itertools/__pycache__/recipes.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/more_itertools/more.py", "Lib/site-packages/setuptools/_vendor/more_itertools/more.pyi", "Lib/site-packages/setuptools/_vendor/more_itertools/py.typed", "Lib/site-packages/setuptools/_vendor/more_itertools/recipes.py", "Lib/site-packages/setuptools/_vendor/more_itertools/recipes.pyi", "Lib/site-packages/setuptools/_vendor/packaging-24.2.dist-info/INSTALLER", "Lib/site-packages/setuptools/_vendor/packaging-24.2.dist-info/LICENSE", "Lib/site-packages/setuptools/_vendor/packaging-24.2.dist-info/LICENSE.APACHE", "Lib/site-packages/setuptools/_vendor/packaging-24.2.dist-info/LICENSE.BSD", "Lib/site-packages/setuptools/_vendor/packaging-24.2.dist-info/METADATA", "Lib/site-packages/setuptools/_vendor/packaging-24.2.dist-info/RECORD", "Lib/site-packages/setuptools/_vendor/packaging-24.2.dist-info/REQUESTED", "Lib/site-packages/setuptools/_vendor/packaging-24.2.dist-info/WHEEL", "Lib/site-packages/setuptools/_vendor/packaging/__init__.py", "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/__init__.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/_elffile.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/_manylinux.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/_musllinux.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/_parser.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/_structures.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/_tokenizer.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/markers.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/metadata.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/requirements.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/specifiers.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/tags.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/utils.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/version.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/packaging/_elffile.py", "Lib/site-packages/setuptools/_vendor/packaging/_manylinux.py", "Lib/site-packages/setuptools/_vendor/packaging/_musllinux.py", "Lib/site-packages/setuptools/_vendor/packaging/_parser.py", "Lib/site-packages/setuptools/_vendor/packaging/_structures.py", "Lib/site-packages/setuptools/_vendor/packaging/_tokenizer.py", "Lib/site-packages/setuptools/_vendor/packaging/licenses/__init__.py", "Lib/site-packages/setuptools/_vendor/packaging/licenses/__pycache__/__init__.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/packaging/licenses/__pycache__/_spdx.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/packaging/licenses/_spdx.py", "Lib/site-packages/setuptools/_vendor/packaging/markers.py", "Lib/site-packages/setuptools/_vendor/packaging/metadata.py", "Lib/site-packages/setuptools/_vendor/packaging/py.typed", "Lib/site-packages/setuptools/_vendor/packaging/requirements.py", "Lib/site-packages/setuptools/_vendor/packaging/specifiers.py", "Lib/site-packages/setuptools/_vendor/packaging/tags.py", "Lib/site-packages/setuptools/_vendor/packaging/utils.py", "Lib/site-packages/setuptools/_vendor/packaging/version.py", "Lib/site-packages/setuptools/_vendor/platformdirs-4.2.2.dist-info/INSTALLER", "Lib/site-packages/setuptools/_vendor/platformdirs-4.2.2.dist-info/METADATA", "Lib/site-packages/setuptools/_vendor/platformdirs-4.2.2.dist-info/RECORD", "Lib/site-packages/setuptools/_vendor/platformdirs-4.2.2.dist-info/REQUESTED", "Lib/site-packages/setuptools/_vendor/platformdirs-4.2.2.dist-info/WHEEL", "Lib/site-packages/setuptools/_vendor/platformdirs-4.2.2.dist-info/licenses/LICENSE", "Lib/site-packages/setuptools/_vendor/platformdirs/__init__.py", "Lib/site-packages/setuptools/_vendor/platformdirs/__main__.py", "Lib/site-packages/setuptools/_vendor/platformdirs/__pycache__/__init__.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/platformdirs/__pycache__/__main__.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/platformdirs/__pycache__/android.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/platformdirs/__pycache__/api.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/platformdirs/__pycache__/macos.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/platformdirs/__pycache__/unix.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/platformdirs/__pycache__/version.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/platformdirs/__pycache__/windows.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/platformdirs/android.py", "Lib/site-packages/setuptools/_vendor/platformdirs/api.py", "Lib/site-packages/setuptools/_vendor/platformdirs/macos.py", "Lib/site-packages/setuptools/_vendor/platformdirs/py.typed", "Lib/site-packages/setuptools/_vendor/platformdirs/unix.py", "Lib/site-packages/setuptools/_vendor/platformdirs/version.py", "Lib/site-packages/setuptools/_vendor/platformdirs/windows.py", "Lib/site-packages/setuptools/_vendor/tomli-2.0.1.dist-info/INSTALLER", "Lib/site-packages/setuptools/_vendor/tomli-2.0.1.dist-info/LICENSE", "Lib/site-packages/setuptools/_vendor/tomli-2.0.1.dist-info/METADATA", "Lib/site-packages/setuptools/_vendor/tomli-2.0.1.dist-info/RECORD", "Lib/site-packages/setuptools/_vendor/tomli-2.0.1.dist-info/REQUESTED", "Lib/site-packages/setuptools/_vendor/tomli-2.0.1.dist-info/WHEEL", "Lib/site-packages/setuptools/_vendor/tomli/__init__.py", "Lib/site-packages/setuptools/_vendor/tomli/__pycache__/__init__.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/tomli/__pycache__/_parser.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/tomli/__pycache__/_re.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/tomli/__pycache__/_types.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/tomli/_parser.py", "Lib/site-packages/setuptools/_vendor/tomli/_re.py", "Lib/site-packages/setuptools/_vendor/tomli/_types.py", "Lib/site-packages/setuptools/_vendor/tomli/py.typed", "Lib/site-packages/setuptools/_vendor/typeguard-4.3.0.dist-info/INSTALLER", "Lib/site-packages/setuptools/_vendor/typeguard-4.3.0.dist-info/LICENSE", "Lib/site-packages/setuptools/_vendor/typeguard-4.3.0.dist-info/METADATA", "Lib/site-packages/setuptools/_vendor/typeguard-4.3.0.dist-info/RECORD", "Lib/site-packages/setuptools/_vendor/typeguard-4.3.0.dist-info/WHEEL", "Lib/site-packages/setuptools/_vendor/typeguard-4.3.0.dist-info/entry_points.txt", "Lib/site-packages/setuptools/_vendor/typeguard-4.3.0.dist-info/top_level.txt", "Lib/site-packages/setuptools/_vendor/typeguard/__init__.py", "Lib/site-packages/setuptools/_vendor/typeguard/__pycache__/__init__.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/typeguard/__pycache__/_checkers.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/typeguard/__pycache__/_config.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/typeguard/__pycache__/_decorators.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/typeguard/__pycache__/_exceptions.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/typeguard/__pycache__/_functions.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/typeguard/__pycache__/_importhook.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/typeguard/__pycache__/_memo.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/typeguard/__pycache__/_pytest_plugin.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/typeguard/__pycache__/_suppression.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/typeguard/__pycache__/_transformer.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/typeguard/__pycache__/_union_transformer.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/typeguard/__pycache__/_utils.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/typeguard/_checkers.py", "Lib/site-packages/setuptools/_vendor/typeguard/_config.py", "Lib/site-packages/setuptools/_vendor/typeguard/_decorators.py", "Lib/site-packages/setuptools/_vendor/typeguard/_exceptions.py", "Lib/site-packages/setuptools/_vendor/typeguard/_functions.py", "Lib/site-packages/setuptools/_vendor/typeguard/_importhook.py", "Lib/site-packages/setuptools/_vendor/typeguard/_memo.py", "Lib/site-packages/setuptools/_vendor/typeguard/_pytest_plugin.py", "Lib/site-packages/setuptools/_vendor/typeguard/_suppression.py", "Lib/site-packages/setuptools/_vendor/typeguard/_transformer.py", "Lib/site-packages/setuptools/_vendor/typeguard/_union_transformer.py", "Lib/site-packages/setuptools/_vendor/typeguard/_utils.py", "Lib/site-packages/setuptools/_vendor/typeguard/py.typed", "Lib/site-packages/setuptools/_vendor/typing_extensions-4.12.2.dist-info/INSTALLER", "Lib/site-packages/setuptools/_vendor/typing_extensions-4.12.2.dist-info/LICENSE", "Lib/site-packages/setuptools/_vendor/typing_extensions-4.12.2.dist-info/METADATA", "Lib/site-packages/setuptools/_vendor/typing_extensions-4.12.2.dist-info/RECORD", "Lib/site-packages/setuptools/_vendor/typing_extensions-4.12.2.dist-info/WHEEL", "Lib/site-packages/setuptools/_vendor/typing_extensions.py", "Lib/site-packages/setuptools/_vendor/wheel-0.45.1.dist-info/INSTALLER", "Lib/site-packages/setuptools/_vendor/wheel-0.45.1.dist-info/LICENSE.txt", "Lib/site-packages/setuptools/_vendor/wheel-0.45.1.dist-info/METADATA", "Lib/site-packages/setuptools/_vendor/wheel-0.45.1.dist-info/RECORD", "Lib/site-packages/setuptools/_vendor/wheel-0.45.1.dist-info/REQUESTED", "Lib/site-packages/setuptools/_vendor/wheel-0.45.1.dist-info/WHEEL", "Lib/site-packages/setuptools/_vendor/wheel-0.45.1.dist-info/entry_points.txt", "Lib/site-packages/setuptools/_vendor/wheel/__init__.py", "Lib/site-packages/setuptools/_vendor/wheel/__main__.py", "Lib/site-packages/setuptools/_vendor/wheel/__pycache__/__init__.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/wheel/__pycache__/__main__.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/wheel/__pycache__/_bdist_wheel.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/wheel/__pycache__/_setuptools_logging.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/wheel/__pycache__/bdist_wheel.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/wheel/__pycache__/macosx_libfile.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/wheel/__pycache__/metadata.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/wheel/__pycache__/util.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/wheel/__pycache__/wheelfile.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/wheel/_bdist_wheel.py", "Lib/site-packages/setuptools/_vendor/wheel/_setuptools_logging.py", "Lib/site-packages/setuptools/_vendor/wheel/bdist_wheel.py", "Lib/site-packages/setuptools/_vendor/wheel/cli/__init__.py", "Lib/site-packages/setuptools/_vendor/wheel/cli/__pycache__/__init__.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/wheel/cli/__pycache__/convert.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/wheel/cli/__pycache__/pack.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/wheel/cli/__pycache__/tags.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/wheel/cli/__pycache__/unpack.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/wheel/cli/convert.py", "Lib/site-packages/setuptools/_vendor/wheel/cli/pack.py", "Lib/site-packages/setuptools/_vendor/wheel/cli/tags.py", "Lib/site-packages/setuptools/_vendor/wheel/cli/unpack.py", "Lib/site-packages/setuptools/_vendor/wheel/macosx_libfile.py", "Lib/site-packages/setuptools/_vendor/wheel/metadata.py", "Lib/site-packages/setuptools/_vendor/wheel/util.py", "Lib/site-packages/setuptools/_vendor/wheel/vendored/__init__.py", "Lib/site-packages/setuptools/_vendor/wheel/vendored/__pycache__/__init__.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/LICENSE", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/LICENSE.APACHE", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/LICENSE.BSD", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__init__.py", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/__init__.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/_elffile.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/_manylinux.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/_musllinux.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/_parser.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/_structures.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/_tokenizer.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/markers.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/requirements.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/specifiers.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/tags.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/utils.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/version.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/_elffile.py", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/_manylinux.py", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/_musllinux.py", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/_parser.py", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/_structures.py", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/_tokenizer.py", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/markers.py", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/requirements.py", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/specifiers.py", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/tags.py", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/utils.py", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/version.py", "Lib/site-packages/setuptools/_vendor/wheel/vendored/vendor.txt", "Lib/site-packages/setuptools/_vendor/wheel/wheelfile.py", "Lib/site-packages/setuptools/_vendor/zipp-3.19.2.dist-info/INSTALLER", "Lib/site-packages/setuptools/_vendor/zipp-3.19.2.dist-info/LICENSE", "Lib/site-packages/setuptools/_vendor/zipp-3.19.2.dist-info/METADATA", "Lib/site-packages/setuptools/_vendor/zipp-3.19.2.dist-info/RECORD", "Lib/site-packages/setuptools/_vendor/zipp-3.19.2.dist-info/REQUESTED", "Lib/site-packages/setuptools/_vendor/zipp-3.19.2.dist-info/WHEEL", "Lib/site-packages/setuptools/_vendor/zipp-3.19.2.dist-info/top_level.txt", "Lib/site-packages/setuptools/_vendor/zipp/__init__.py", "Lib/site-packages/setuptools/_vendor/zipp/__pycache__/__init__.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/zipp/__pycache__/glob.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/zipp/compat/__init__.py", "Lib/site-packages/setuptools/_vendor/zipp/compat/__pycache__/__init__.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/zipp/compat/__pycache__/py310.cpython-311.pyc", "Lib/site-packages/setuptools/_vendor/zipp/compat/py310.py", "Lib/site-packages/setuptools/_vendor/zipp/glob.py", "Lib/site-packages/setuptools/archive_util.py", "Lib/site-packages/setuptools/build_meta.py", "Lib/site-packages/setuptools/cli-32.exe", "Lib/site-packages/setuptools/cli-64.exe", "Lib/site-packages/setuptools/cli-arm64.exe", "Lib/site-packages/setuptools/cli.exe", "Lib/site-packages/setuptools/command/__init__.py", "Lib/site-packages/setuptools/command/__pycache__/__init__.cpython-311.pyc", "Lib/site-packages/setuptools/command/__pycache__/_requirestxt.cpython-311.pyc", "Lib/site-packages/setuptools/command/__pycache__/alias.cpython-311.pyc", "Lib/site-packages/setuptools/command/__pycache__/bdist_egg.cpython-311.pyc", "Lib/site-packages/setuptools/command/__pycache__/bdist_rpm.cpython-311.pyc", "Lib/site-packages/setuptools/command/__pycache__/bdist_wheel.cpython-311.pyc", "Lib/site-packages/setuptools/command/__pycache__/build.cpython-311.pyc", "Lib/site-packages/setuptools/command/__pycache__/build_clib.cpython-311.pyc", "Lib/site-packages/setuptools/command/__pycache__/build_ext.cpython-311.pyc", "Lib/site-packages/setuptools/command/__pycache__/build_py.cpython-311.pyc", "Lib/site-packages/setuptools/command/__pycache__/develop.cpython-311.pyc", "Lib/site-packages/setuptools/command/__pycache__/dist_info.cpython-311.pyc", "Lib/site-packages/setuptools/command/__pycache__/easy_install.cpython-311.pyc", "Lib/site-packages/setuptools/command/__pycache__/editable_wheel.cpython-311.pyc", "Lib/site-packages/setuptools/command/__pycache__/egg_info.cpython-311.pyc", "Lib/site-packages/setuptools/command/__pycache__/install.cpython-311.pyc", "Lib/site-packages/setuptools/command/__pycache__/install_egg_info.cpython-311.pyc", "Lib/site-packages/setuptools/command/__pycache__/install_lib.cpython-311.pyc", "Lib/site-packages/setuptools/command/__pycache__/install_scripts.cpython-311.pyc", "Lib/site-packages/setuptools/command/__pycache__/rotate.cpython-311.pyc", "Lib/site-packages/setuptools/command/__pycache__/saveopts.cpython-311.pyc", "Lib/site-packages/setuptools/command/__pycache__/sdist.cpython-311.pyc", "Lib/site-packages/setuptools/command/__pycache__/setopt.cpython-311.pyc", "Lib/site-packages/setuptools/command/__pycache__/test.cpython-311.pyc", "Lib/site-packages/setuptools/command/_requirestxt.py", "Lib/site-packages/setuptools/command/alias.py", "Lib/site-packages/setuptools/command/bdist_egg.py", "Lib/site-packages/setuptools/command/bdist_rpm.py", "Lib/site-packages/setuptools/command/bdist_wheel.py", "Lib/site-packages/setuptools/command/build.py", "Lib/site-packages/setuptools/command/build_clib.py", "Lib/site-packages/setuptools/command/build_ext.py", "Lib/site-packages/setuptools/command/build_py.py", "Lib/site-packages/setuptools/command/develop.py", "Lib/site-packages/setuptools/command/dist_info.py", "Lib/site-packages/setuptools/command/easy_install.py", "Lib/site-packages/setuptools/command/editable_wheel.py", "Lib/site-packages/setuptools/command/egg_info.py", "Lib/site-packages/setuptools/command/install.py", "Lib/site-packages/setuptools/command/install_egg_info.py", "Lib/site-packages/setuptools/command/install_lib.py", "Lib/site-packages/setuptools/command/install_scripts.py", "Lib/site-packages/setuptools/command/launcher manifest.xml", "Lib/site-packages/setuptools/command/rotate.py", "Lib/site-packages/setuptools/command/saveopts.py", "Lib/site-packages/setuptools/command/sdist.py", "Lib/site-packages/setuptools/command/setopt.py", "Lib/site-packages/setuptools/command/test.py", "Lib/site-packages/setuptools/compat/__init__.py", "Lib/site-packages/setuptools/compat/__pycache__/__init__.cpython-311.pyc", "Lib/site-packages/setuptools/compat/__pycache__/py310.cpython-311.pyc", "Lib/site-packages/setuptools/compat/__pycache__/py311.cpython-311.pyc", "Lib/site-packages/setuptools/compat/__pycache__/py312.cpython-311.pyc", "Lib/site-packages/setuptools/compat/__pycache__/py39.cpython-311.pyc", "Lib/site-packages/setuptools/compat/py310.py", "Lib/site-packages/setuptools/compat/py311.py", "Lib/site-packages/setuptools/compat/py312.py", "Lib/site-packages/setuptools/compat/py39.py", "Lib/site-packages/setuptools/config/NOTICE", "Lib/site-packages/setuptools/config/__init__.py", "Lib/site-packages/setuptools/config/__pycache__/__init__.cpython-311.pyc", "Lib/site-packages/setuptools/config/__pycache__/_apply_pyprojecttoml.cpython-311.pyc", "Lib/site-packages/setuptools/config/__pycache__/expand.cpython-311.pyc", "Lib/site-packages/setuptools/config/__pycache__/pyprojecttoml.cpython-311.pyc", "Lib/site-packages/setuptools/config/__pycache__/setupcfg.cpython-311.pyc", "Lib/site-packages/setuptools/config/_apply_pyprojecttoml.py", "Lib/site-packages/setuptools/config/_validate_pyproject/NOTICE", "Lib/site-packages/setuptools/config/_validate_pyproject/__init__.py", "Lib/site-packages/setuptools/config/_validate_pyproject/__pycache__/__init__.cpython-311.pyc", "Lib/site-packages/setuptools/config/_validate_pyproject/__pycache__/error_reporting.cpython-311.pyc", "Lib/site-packages/setuptools/config/_validate_pyproject/__pycache__/extra_validations.cpython-311.pyc", "Lib/site-packages/setuptools/config/_validate_pyproject/__pycache__/fastjsonschema_exceptions.cpython-311.pyc", "Lib/site-packages/setuptools/config/_validate_pyproject/__pycache__/fastjsonschema_validations.cpython-311.pyc", "Lib/site-packages/setuptools/config/_validate_pyproject/__pycache__/formats.cpython-311.pyc", "Lib/site-packages/setuptools/config/_validate_pyproject/error_reporting.py", "Lib/site-packages/setuptools/config/_validate_pyproject/extra_validations.py", "Lib/site-packages/setuptools/config/_validate_pyproject/fastjsonschema_exceptions.py", "Lib/site-packages/setuptools/config/_validate_pyproject/fastjsonschema_validations.py", "Lib/site-packages/setuptools/config/_validate_pyproject/formats.py", "Lib/site-packages/setuptools/config/distutils.schema.json", "Lib/site-packages/setuptools/config/expand.py", "Lib/site-packages/setuptools/config/pyprojecttoml.py", "Lib/site-packages/setuptools/config/setupcfg.py", "Lib/site-packages/setuptools/config/setuptools.schema.json", "Lib/site-packages/setuptools/depends.py", "Lib/site-packages/setuptools/discovery.py", "Lib/site-packages/setuptools/dist.py", "Lib/site-packages/setuptools/errors.py", "Lib/site-packages/setuptools/extension.py", "Lib/site-packages/setuptools/glob.py", "Lib/site-packages/setuptools/gui-32.exe", "Lib/site-packages/setuptools/gui-64.exe", "Lib/site-packages/setuptools/gui-arm64.exe", "Lib/site-packages/setuptools/gui.exe", "Lib/site-packages/setuptools/installer.py", "Lib/site-packages/setuptools/launch.py", "Lib/site-packages/setuptools/logging.py", "Lib/site-packages/setuptools/modified.py", "Lib/site-packages/setuptools/monkey.py", "Lib/site-packages/setuptools/msvc.py", "Lib/site-packages/setuptools/namespaces.py", "Lib/site-packages/setuptools/package_index.py", "Lib/site-packages/setuptools/sandbox.py", "Lib/site-packages/setuptools/script (dev).tmpl", "Lib/site-packages/setuptools/script.tmpl", "Lib/site-packages/setuptools/tests/__init__.py", "Lib/site-packages/setuptools/tests/__pycache__/__init__.cpython-311.pyc", "Lib/site-packages/setuptools/tests/__pycache__/contexts.cpython-311.pyc", "Lib/site-packages/setuptools/tests/__pycache__/environment.cpython-311.pyc", "Lib/site-packages/setuptools/tests/__pycache__/fixtures.cpython-311.pyc", "Lib/site-packages/setuptools/tests/__pycache__/mod_with_constant.cpython-311.pyc", "Lib/site-packages/setuptools/tests/__pycache__/namespaces.cpython-311.pyc", "Lib/site-packages/setuptools/tests/__pycache__/script-with-bom.cpython-311.pyc", "Lib/site-packages/setuptools/tests/__pycache__/server.cpython-311.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_archive_util.cpython-311.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_bdist_deprecations.cpython-311.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_bdist_egg.cpython-311.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_bdist_wheel.cpython-311.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_build.cpython-311.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_build_clib.cpython-311.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_build_ext.cpython-311.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_build_meta.cpython-311.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_build_py.cpython-311.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_config_discovery.cpython-311.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_core_metadata.cpython-311.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_depends.cpython-311.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_develop.cpython-311.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_dist.cpython-311.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_dist_info.cpython-311.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_distutils_adoption.cpython-311.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_easy_install.cpython-311.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_editable_install.cpython-311.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_egg_info.cpython-311.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_extern.cpython-311.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_find_packages.cpython-311.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_find_py_modules.cpython-311.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_glob.cpython-311.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_install_scripts.cpython-311.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_logging.cpython-311.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_manifest.cpython-311.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_namespaces.cpython-311.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_packageindex.cpython-311.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_sandbox.cpython-311.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_sdist.cpython-311.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_setopt.cpython-311.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_setuptools.cpython-311.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_shutil_wrapper.cpython-311.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_unicode_utils.cpython-311.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_virtualenv.cpython-311.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_warnings.cpython-311.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_wheel.cpython-311.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_windows_wrappers.cpython-311.pyc", "Lib/site-packages/setuptools/tests/__pycache__/text.cpython-311.pyc", "Lib/site-packages/setuptools/tests/__pycache__/textwrap.cpython-311.pyc", "Lib/site-packages/setuptools/tests/compat/__init__.py", "Lib/site-packages/setuptools/tests/compat/__pycache__/__init__.cpython-311.pyc", "Lib/site-packages/setuptools/tests/compat/__pycache__/py39.cpython-311.pyc", "Lib/site-packages/setuptools/tests/compat/py39.py", "Lib/site-packages/setuptools/tests/config/__init__.py", "Lib/site-packages/setuptools/tests/config/__pycache__/__init__.cpython-311.pyc", "Lib/site-packages/setuptools/tests/config/__pycache__/test_apply_pyprojecttoml.cpython-311.pyc", "Lib/site-packages/setuptools/tests/config/__pycache__/test_expand.cpython-311.pyc", "Lib/site-packages/setuptools/tests/config/__pycache__/test_pyprojecttoml.cpython-311.pyc", "Lib/site-packages/setuptools/tests/config/__pycache__/test_pyprojecttoml_dynamic_deps.cpython-311.pyc", "Lib/site-packages/setuptools/tests/config/__pycache__/test_setupcfg.cpython-311.pyc", "Lib/site-packages/setuptools/tests/config/downloads/__init__.py", "Lib/site-packages/setuptools/tests/config/downloads/__pycache__/__init__.cpython-311.pyc", "Lib/site-packages/setuptools/tests/config/downloads/__pycache__/preload.cpython-311.pyc", "Lib/site-packages/setuptools/tests/config/downloads/preload.py", "Lib/site-packages/setuptools/tests/config/setupcfg_examples.txt", "Lib/site-packages/setuptools/tests/config/test_apply_pyprojecttoml.py", "Lib/site-packages/setuptools/tests/config/test_expand.py", "Lib/site-packages/setuptools/tests/config/test_pyprojecttoml.py", "Lib/site-packages/setuptools/tests/config/test_pyprojecttoml_dynamic_deps.py", "Lib/site-packages/setuptools/tests/config/test_setupcfg.py", "Lib/site-packages/setuptools/tests/contexts.py", "Lib/site-packages/setuptools/tests/environment.py", "Lib/site-packages/setuptools/tests/fixtures.py", "Lib/site-packages/setuptools/tests/indexes/test_links_priority/external.html", "Lib/site-packages/setuptools/tests/indexes/test_links_priority/simple/foobar/index.html", "Lib/site-packages/setuptools/tests/integration/__init__.py", "Lib/site-packages/setuptools/tests/integration/__pycache__/__init__.cpython-311.pyc", "Lib/site-packages/setuptools/tests/integration/__pycache__/helpers.cpython-311.pyc", "Lib/site-packages/setuptools/tests/integration/__pycache__/test_pip_install_sdist.cpython-311.pyc", "Lib/site-packages/setuptools/tests/integration/helpers.py", "Lib/site-packages/setuptools/tests/integration/test_pip_install_sdist.py", "Lib/site-packages/setuptools/tests/mod_with_constant.py", "Lib/site-packages/setuptools/tests/namespaces.py", "Lib/site-packages/setuptools/tests/script-with-bom.py", "Lib/site-packages/setuptools/tests/server.py", "Lib/site-packages/setuptools/tests/test_archive_util.py", "Lib/site-packages/setuptools/tests/test_bdist_deprecations.py", "Lib/site-packages/setuptools/tests/test_bdist_egg.py", "Lib/site-packages/setuptools/tests/test_bdist_wheel.py", "Lib/site-packages/setuptools/tests/test_build.py", "Lib/site-packages/setuptools/tests/test_build_clib.py", "Lib/site-packages/setuptools/tests/test_build_ext.py", "Lib/site-packages/setuptools/tests/test_build_meta.py", "Lib/site-packages/setuptools/tests/test_build_py.py", "Lib/site-packages/setuptools/tests/test_config_discovery.py", "Lib/site-packages/setuptools/tests/test_core_metadata.py", "Lib/site-packages/setuptools/tests/test_depends.py", "Lib/site-packages/setuptools/tests/test_develop.py", "Lib/site-packages/setuptools/tests/test_dist.py", "Lib/site-packages/setuptools/tests/test_dist_info.py", "Lib/site-packages/setuptools/tests/test_distutils_adoption.py", "Lib/site-packages/setuptools/tests/test_easy_install.py", "Lib/site-packages/setuptools/tests/test_editable_install.py", "Lib/site-packages/setuptools/tests/test_egg_info.py", "Lib/site-packages/setuptools/tests/test_extern.py", "Lib/site-packages/setuptools/tests/test_find_packages.py", "Lib/site-packages/setuptools/tests/test_find_py_modules.py", "Lib/site-packages/setuptools/tests/test_glob.py", "Lib/site-packages/setuptools/tests/test_install_scripts.py", "Lib/site-packages/setuptools/tests/test_logging.py", "Lib/site-packages/setuptools/tests/test_manifest.py", "Lib/site-packages/setuptools/tests/test_namespaces.py", "Lib/site-packages/setuptools/tests/test_packageindex.py", "Lib/site-packages/setuptools/tests/test_sandbox.py", "Lib/site-packages/setuptools/tests/test_sdist.py", "Lib/site-packages/setuptools/tests/test_setopt.py", "Lib/site-packages/setuptools/tests/test_setuptools.py", "Lib/site-packages/setuptools/tests/test_shutil_wrapper.py", "Lib/site-packages/setuptools/tests/test_unicode_utils.py", "Lib/site-packages/setuptools/tests/test_virtualenv.py", "Lib/site-packages/setuptools/tests/test_warnings.py", "Lib/site-packages/setuptools/tests/test_wheel.py", "Lib/site-packages/setuptools/tests/test_windows_wrappers.py", "Lib/site-packages/setuptools/tests/text.py", "Lib/site-packages/setuptools/tests/textwrap.py", "Lib/site-packages/setuptools/unicode_utils.py", "Lib/site-packages/setuptools/version.py", "Lib/site-packages/setuptools/warnings.py", "Lib/site-packages/setuptools/wheel.py", "Lib/site-packages/setuptools/windows_support.py"], "fn": "setuptools-78.1.1-py311haa95532_0.conda", "license": "MIT", "link": {"source": "C:\\Users\\<USER>\\anaconda3\\pkgs\\setuptools-78.1.1-py311haa95532_0", "type": 1}, "md5": "4b2a66eb7d1f0bc20853d09511c36e22", "name": "setuptools", "package_tarball_full_path": "C:\\Users\\<USER>\\anaconda3\\pkgs\\setuptools-78.1.1-py311haa95532_0.conda", "paths_data": {"paths": [{"_path": "Lib/site-packages/_distutils_hack/__init__.py", "path_type": "hardlink", "sha256": "df81e6bcba34ee3e3952f776551fb669143b9490fdd6c4caeb32609f97e985b4", "sha256_in_prefix": "df81e6bcba34ee3e3952f776551fb669143b9490fdd6c4caeb32609f97e985b4", "size_in_bytes": 6755}, {"_path": "Lib/site-packages/_distutils_hack/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "6622ce589fad52291415e034d22dd3befd667df44d4e97f69ea985e33bd9f74e", "sha256_in_prefix": "6622ce589fad52291415e034d22dd3befd667df44d4e97f69ea985e33bd9f74e", "size_in_bytes": 11916}, {"_path": "Lib/site-packages/_distutils_hack/__pycache__/override.cpython-311.pyc", "path_type": "hardlink", "sha256": "1d61a655508de445d05919704aba0f37a9b06a55b0d9457589e8e029e23dd927", "sha256_in_prefix": "1d61a655508de445d05919704aba0f37a9b06a55b0d9457589e8e029e23dd927", "size_in_bytes": 276}, {"_path": "Lib/site-packages/_distutils_hack/override.py", "path_type": "hardlink", "sha256": "12efecf8d17a5486780aa774b5b6c0e70b56932d8864f35df1eb7a18bb759b3a", "sha256_in_prefix": "12efecf8d17a5486780aa774b5b6c0e70b56932d8864f35df1eb7a18bb759b3a", "size_in_bytes": 44}, {"_path": "Lib/site-packages/distutils-precedence.pth", "path_type": "hardlink", "sha256": "ab406aa05439fe87070cde36180433193568432f11d04f0f762f374b8a9302f5", "sha256_in_prefix": "ab406aa05439fe87070cde36180433193568432f11d04f0f762f374b8a9302f5", "size_in_bytes": 152}, {"_path": "Lib/site-packages/pkg_resources/__init__.py", "path_type": "hardlink", "sha256": "fab87b5ce9d3c5d1ae0beffd140caee43eacf012f552c05e87152d8fb6be215a", "sha256_in_prefix": "fab87b5ce9d3c5d1ae0beffd140caee43eacf012f552c05e87152d8fb6be215a", "size_in_bytes": 126203}, {"_path": "Lib/site-packages/pkg_resources/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "cfcd4f4b1fcafdec09d19ba56596cdaef5e9fc753b66785eb3c1e3559558797f", "sha256_in_prefix": "cfcd4f4b1fcafdec09d19ba56596cdaef5e9fc753b66785eb3c1e3559558797f", "size_in_bytes": 179439}, {"_path": "Lib/site-packages/pkg_resources/api_tests.txt", "path_type": "hardlink", "sha256": "5c476fcb88a01c7aeadaa34734c1e795f3ba5d240a36a3b22c76e5e907297c02", "sha256_in_prefix": "5c476fcb88a01c7aeadaa34734c1e795f3ba5d240a36a3b22c76e5e907297c02", "size_in_bytes": 12595}, {"_path": "Lib/site-packages/pkg_resources/py.typed", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/pkg_resources/tests/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/pkg_resources/tests/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "83878f821b25bd995397d162f16bf0c765aa5b3d4c2b67239365bf432d514260", "sha256_in_prefix": "83878f821b25bd995397d162f16bf0c765aa5b3d4c2b67239365bf432d514260", "size_in_bytes": 158}, {"_path": "Lib/site-packages/pkg_resources/tests/__pycache__/test_find_distributions.cpython-311.pyc", "path_type": "hardlink", "sha256": "2a473168618657edbc7256508d57b217801ff2e3a6a4bf835f5c06153b040ae3", "sha256_in_prefix": "2a473168618657edbc7256508d57b217801ff2e3a6a4bf835f5c06153b040ae3", "size_in_bytes": 3995}, {"_path": "Lib/site-packages/pkg_resources/tests/__pycache__/test_integration_zope_interface.cpython-311.pyc", "path_type": "hardlink", "sha256": "abbef7ce6243e303c92effca449dc8b406cbc087570c037ef12128236a70d8af", "sha256_in_prefix": "abbef7ce6243e303c92effca449dc8b406cbc087570c037ef12128236a70d8af", "size_in_bytes": 2072}, {"_path": "Lib/site-packages/pkg_resources/tests/__pycache__/test_markers.cpython-311.pyc", "path_type": "hardlink", "sha256": "88200f8c5ca7dec7d8ec4b5f74977dcc362f508363e51a4308e93943f520d64a", "sha256_in_prefix": "88200f8c5ca7dec7d8ec4b5f74977dcc362f508363e51a4308e93943f520d64a", "size_in_bytes": 664}, {"_path": "Lib/site-packages/pkg_resources/tests/__pycache__/test_pkg_resources.cpython-311.pyc", "path_type": "hardlink", "sha256": "7440ce503406b096f56c5e1689ed00b11f7edc5e0db379fa46cfa9fedb992660", "sha256_in_prefix": "7440ce503406b096f56c5e1689ed00b11f7edc5e0db379fa46cfa9fedb992660", "size_in_bytes": 27807}, {"_path": "Lib/site-packages/pkg_resources/tests/__pycache__/test_resources.cpython-311.pyc", "path_type": "hardlink", "sha256": "8b9814c0adf8e933f4059631ff0942fb8ccb67317ab8274fa08f7d38cdd8bb83", "sha256_in_prefix": "8b9814c0adf8e933f4059631ff0942fb8ccb67317ab8274fa08f7d38cdd8bb83", "size_in_bytes": 54789}, {"_path": "Lib/site-packages/pkg_resources/tests/__pycache__/test_working_set.cpython-311.pyc", "path_type": "hardlink", "sha256": "53319b6fc2d082bbdfaf5ae3d77d21eace74928b0a53ef49d905e2b0e901da17", "sha256_in_prefix": "53319b6fc2d082bbdfaf5ae3d77d21eace74928b0a53ef49d905e2b0e901da17", "size_in_bytes": 11373}, {"_path": "Lib/site-packages/pkg_resources/tests/data/my-test-package-source/__pycache__/setup.cpython-311.pyc", "path_type": "hardlink", "sha256": "0d34d27c030dffece61d76eceef5d079a4c4fcc074b7be2b1e8b4f5aa6728a1c", "sha256_in_prefix": "0d34d27c030dffece61d76eceef5d079a4c4fcc074b7be2b1e8b4f5aa6728a1c", "size_in_bytes": 349}, {"_path": "Lib/site-packages/pkg_resources/tests/data/my-test-package-source/setup.cfg", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/pkg_resources/tests/data/my-test-package-source/setup.py", "path_type": "hardlink", "sha256": "d55a1b84065b31beccf667e16ff344f0fc03b2fba4a162ecf5a5004b4a5885ef", "sha256_in_prefix": "d55a1b84065b31beccf667e16ff344f0fc03b2fba4a162ecf5a5004b4a5885ef", "size_in_bytes": 105}, {"_path": "Lib/site-packages/pkg_resources/tests/data/my-test-package-zip/my-test-package.zip", "path_type": "hardlink", "sha256": "01845c437f4655e3cf9cc4fc4e49cfd607431f22675e1b611129a90239f34822", "sha256_in_prefix": "01845c437f4655e3cf9cc4fc4e49cfd607431f22675e1b611129a90239f34822", "size_in_bytes": 1809}, {"_path": "Lib/site-packages/pkg_resources/tests/data/my-test-package_unpacked-egg/my_test_package-1.0-py3.7.egg/EGG-INFO/PKG-INFO", "path_type": "hardlink", "sha256": "26f5aff48a363c0b98c04130d9f056e1073962f75b92c729297d6498bceca079", "sha256_in_prefix": "26f5aff48a363c0b98c04130d9f056e1073962f75b92c729297d6498bceca079", "size_in_bytes": 187}, {"_path": "Lib/site-packages/pkg_resources/tests/data/my-test-package_unpacked-egg/my_test_package-1.0-py3.7.egg/EGG-INFO/SOURCES.txt", "path_type": "hardlink", "sha256": "e029641fc793a2f66b755ac916c56ec5d6cc105fbe941552b8aa270c03c4e497", "sha256_in_prefix": "e029641fc793a2f66b755ac916c56ec5d6cc105fbe941552b8aa270c03c4e497", "size_in_bytes": 208}, {"_path": "Lib/site-packages/pkg_resources/tests/data/my-test-package_unpacked-egg/my_test_package-1.0-py3.7.egg/EGG-INFO/dependency_links.txt", "path_type": "hardlink", "sha256": "01ba4719c80b6fe911b091a7c05124b64eeece964e09c058ef8f9805daca546b", "sha256_in_prefix": "01ba4719c80b6fe911b091a7c05124b64eeece964e09c058ef8f9805daca546b", "size_in_bytes": 1}, {"_path": "Lib/site-packages/pkg_resources/tests/data/my-test-package_unpacked-egg/my_test_package-1.0-py3.7.egg/EGG-INFO/top_level.txt", "path_type": "hardlink", "sha256": "01ba4719c80b6fe911b091a7c05124b64eeece964e09c058ef8f9805daca546b", "sha256_in_prefix": "01ba4719c80b6fe911b091a7c05124b64eeece964e09c058ef8f9805daca546b", "size_in_bytes": 1}, {"_path": "Lib/site-packages/pkg_resources/tests/data/my-test-package_unpacked-egg/my_test_package-1.0-py3.7.egg/EGG-INFO/zip-safe", "path_type": "hardlink", "sha256": "01ba4719c80b6fe911b091a7c05124b64eeece964e09c058ef8f9805daca546b", "sha256_in_prefix": "01ba4719c80b6fe911b091a7c05124b64eeece964e09c058ef8f9805daca546b", "size_in_bytes": 1}, {"_path": "Lib/site-packages/pkg_resources/tests/data/my-test-package_zipped-egg/my_test_package-1.0-py3.7.egg", "path_type": "hardlink", "sha256": "65394c1b18d11a2283364880d9cef98db407d93588b5e3f4d22ac5f60bdccdba", "sha256_in_prefix": "65394c1b18d11a2283364880d9cef98db407d93588b5e3f4d22ac5f60bdccdba", "size_in_bytes": 843}, {"_path": "Lib/site-packages/pkg_resources/tests/test_find_distributions.py", "path_type": "hardlink", "sha256": "53dd5ca2fe4bd423802162cdab75f2e29954eff327384d56b5732eea2576c1a3", "sha256_in_prefix": "53dd5ca2fe4bd423802162cdab75f2e29954eff327384d56b5732eea2576c1a3", "size_in_bytes": 1972}, {"_path": "Lib/site-packages/pkg_resources/tests/test_integration_zope_interface.py", "path_type": "hardlink", "sha256": "9f35682b9e7b29940dd15dc3210d6c55e6823a0b782a997e08e0c05ac3bba667", "sha256_in_prefix": "9f35682b9e7b29940dd15dc3210d6c55e6823a0b782a997e08e0c05ac3bba667", "size_in_bytes": 1652}, {"_path": "Lib/site-packages/pkg_resources/tests/test_markers.py", "path_type": "hardlink", "sha256": "d28aca83b50c0dfedf9ee350bd130e73e105f4400ffc94d09e4e26b4681b5b9d", "sha256_in_prefix": "d28aca83b50c0dfedf9ee350bd130e73e105f4400ffc94d09e4e26b4681b5b9d", "size_in_bytes": 241}, {"_path": "Lib/site-packages/pkg_resources/tests/test_pkg_resources.py", "path_type": "hardlink", "sha256": "e4cb786c94212c22fc8fc702e3a52fdf6369d977354d3c4b19ac087c44f9e459", "sha256_in_prefix": "e4cb786c94212c22fc8fc702e3a52fdf6369d977354d3c4b19ac087c44f9e459", "size_in_bytes": 17111}, {"_path": "Lib/site-packages/pkg_resources/tests/test_resources.py", "path_type": "hardlink", "sha256": "2b42ea300506a5143da546fd2b4bf223b19eb2fb6542f4c7d3be26f84d95425a", "sha256_in_prefix": "2b42ea300506a5143da546fd2b4bf223b19eb2fb6542f4c7d3be26f84d95425a", "size_in_bytes": 31252}, {"_path": "Lib/site-packages/pkg_resources/tests/test_working_set.py", "path_type": "hardlink", "sha256": "951b46256222c52c123126e31e047178911088b3115dccf7c7324bdaa2fb7976", "sha256_in_prefix": "951b46256222c52c123126e31e047178911088b3115dccf7c7324bdaa2fb7976", "size_in_bytes": 8602}, {"_path": "Lib/site-packages/setuptools-78.1.1-py3.11.egg-info/PKG-INFO", "path_type": "hardlink", "sha256": "b8abe3cc375d9768729904d2c5da6985061c34977a9507dfda1cd32bba8190c2", "sha256_in_prefix": "b8abe3cc375d9768729904d2c5da6985061c34977a9507dfda1cd32bba8190c2", "size_in_bytes": 6688}, {"_path": "Lib/site-packages/setuptools-78.1.1-py3.11.egg-info/SOURCES.txt", "path_type": "hardlink", "sha256": "cfded7934597a4a900d7a0f110a44dff6486e4f7b4c8450b08ce803d31a09433", "sha256_in_prefix": "cfded7934597a4a900d7a0f110a44dff6486e4f7b4c8450b08ce803d31a09433", "size_in_bytes": 24294}, {"_path": "Lib/site-packages/setuptools-78.1.1-py3.11.egg-info/dependency_links.txt", "path_type": "hardlink", "sha256": "01ba4719c80b6fe911b091a7c05124b64eeece964e09c058ef8f9805daca546b", "sha256_in_prefix": "01ba4719c80b6fe911b091a7c05124b64eeece964e09c058ef8f9805daca546b", "size_in_bytes": 1}, {"_path": "Lib/site-packages/setuptools-78.1.1-py3.11.egg-info/entry_points.txt", "path_type": "hardlink", "sha256": "ce482d8697ff15af4d544f69e85293dd793d0d1d5f680711538728820b15ee30", "sha256_in_prefix": "ce482d8697ff15af4d544f69e85293dd793d0d1d5f680711538728820b15ee30", "size_in_bytes": 2449}, {"_path": "Lib/site-packages/setuptools-78.1.1-py3.11.egg-info/requires.txt", "path_type": "hardlink", "sha256": "cd186b9559d56bb22f53a7730a6ffdd8b0c2a342bbf97705c98b547553d584e3", "sha256_in_prefix": "cd186b9559d56bb22f53a7730a6ffdd8b0c2a342bbf97705c98b547553d584e3", "size_in_bytes": 1231}, {"_path": "Lib/site-packages/setuptools-78.1.1-py3.11.egg-info/top_level.txt", "path_type": "hardlink", "sha256": "77dc8bdfdbff5bbaa62830d21fab13e1b1348ff2ecd4cdcfd7ad4e1a076c9b88", "sha256_in_prefix": "77dc8bdfdbff5bbaa62830d21fab13e1b1348ff2ecd4cdcfd7ad4e1a076c9b88", "size_in_bytes": 41}, {"_path": "Lib/site-packages/setuptools/__init__.py", "path_type": "hardlink", "sha256": "010b0c791156cfd090f5a06d71291b0780e7f2ddb0f3af863eb8a4969a008dec", "sha256_in_prefix": "010b0c791156cfd090f5a06d71291b0780e7f2ddb0f3af863eb8a4969a008dec", "size_in_bytes": 10406}, {"_path": "Lib/site-packages/setuptools/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "3b9677d690f3a44bb8a81c98c8cea72affa996ad6c475d40da5e69b319770174", "sha256_in_prefix": "3b9677d690f3a44bb8a81c98c8cea72affa996ad6c475d40da5e69b319770174", "size_in_bytes": 15329}, {"_path": "Lib/site-packages/setuptools/__pycache__/_core_metadata.cpython-311.pyc", "path_type": "hardlink", "sha256": "c18429aee78eebc34d42791799c8eb8e6e4a969baa87eb933f17a13fe5e6fa41", "sha256_in_prefix": "c18429aee78eebc34d42791799c8eb8e6e4a969baa87eb933f17a13fe5e6fa41", "size_in_bytes": 16880}, {"_path": "Lib/site-packages/setuptools/__pycache__/_entry_points.cpython-311.pyc", "path_type": "hardlink", "sha256": "cfb7390de075ce49955eea7fc0c84cebb332a2f2f4f977d5bb5670a72dc2ed26", "sha256_in_prefix": "cfb7390de075ce49955eea7fc0c84cebb332a2f2f4f977d5bb5670a72dc2ed26", "size_in_bytes": 5109}, {"_path": "Lib/site-packages/setuptools/__pycache__/_imp.cpython-311.pyc", "path_type": "hardlink", "sha256": "c8838beb77326a88e5f7c8457e7adc2f9320333479f1e5e3f2de53f3ead7f406", "sha256_in_prefix": "c8838beb77326a88e5f7c8457e7adc2f9320333479f1e5e3f2de53f3ead7f406", "size_in_bytes": 3660}, {"_path": "Lib/site-packages/setuptools/__pycache__/_importlib.cpython-311.pyc", "path_type": "hardlink", "sha256": "7c32bc36aef87ec5a2d49ae8a3e715ccf6bb10b29c5c124fc5d22aa11106561b", "sha256_in_prefix": "7c32bc36aef87ec5a2d49ae8a3e715ccf6bb10b29c5c124fc5d22aa11106561b", "size_in_bytes": 402}, {"_path": "Lib/site-packages/setuptools/__pycache__/_itertools.cpython-311.pyc", "path_type": "hardlink", "sha256": "4e3b49d0b6600eace523bfca316c7cf90ebb520d4ad8b76b6a7c9a1e208771f1", "sha256_in_prefix": "4e3b49d0b6600eace523bfca316c7cf90ebb520d4ad8b76b6a7c9a1e208771f1", "size_in_bytes": 1098}, {"_path": "Lib/site-packages/setuptools/__pycache__/_normalization.cpython-311.pyc", "path_type": "hardlink", "sha256": "a12650ca87bccc6709b9a9848a4da0ca513bceb5418be7b7ecc79eab7fef6bd9", "sha256_in_prefix": "a12650ca87bccc6709b9a9848a4da0ca513bceb5418be7b7ecc79eab7fef6bd9", "size_in_bytes": 7898}, {"_path": "Lib/site-packages/setuptools/__pycache__/_path.cpython-311.pyc", "path_type": "hardlink", "sha256": "c78b3a742739b13a89337d691b448f4076a520660d0dec9a81706abba8bccefe", "sha256_in_prefix": "c78b3a742739b13a89337d691b448f4076a520660d0dec9a81706abba8bccefe", "size_in_bytes": 4434}, {"_path": "Lib/site-packages/setuptools/__pycache__/_reqs.cpython-311.pyc", "path_type": "hardlink", "sha256": "019231ab87245d65ef5659386b592ef8156f383c55dcf44934b01278e406a5e6", "sha256_in_prefix": "019231ab87245d65ef5659386b592ef8156f383c55dcf44934b01278e406a5e6", "size_in_bytes": 2253}, {"_path": "Lib/site-packages/setuptools/__pycache__/_shutil.cpython-311.pyc", "path_type": "hardlink", "sha256": "b89ba745e66858793f7555ab856d1048e7012f7e8311a6b4eed471f7e6e7da02", "sha256_in_prefix": "b89ba745e66858793f7555ab856d1048e7012f7e8311a6b4eed471f7e6e7da02", "size_in_bytes": 2583}, {"_path": "Lib/site-packages/setuptools/__pycache__/_static.cpython-311.pyc", "path_type": "hardlink", "sha256": "c33a6987d7ee736623c5dea15411b8efca5da6ce53c2cb706fa4b305199ce542", "sha256_in_prefix": "c33a6987d7ee736623c5dea15411b8efca5da6ce53c2cb706fa4b305199ce542", "size_in_bytes": 6638}, {"_path": "Lib/site-packages/setuptools/__pycache__/archive_util.cpython-311.pyc", "path_type": "hardlink", "sha256": "94c3236d22972533acfd57aaeee91bf43db3685fc6a2506a7df1e417f13313f6", "sha256_in_prefix": "94c3236d22972533acfd57aaeee91bf43db3685fc6a2506a7df1e417f13313f6", "size_in_bytes": 10203}, {"_path": "Lib/site-packages/setuptools/__pycache__/build_meta.cpython-311.pyc", "path_type": "hardlink", "sha256": "51ac567b5952b62ba2958bd940f1b9a3ddbc2abed61c9b7272da85ba99aff8d5", "sha256_in_prefix": "51ac567b5952b62ba2958bd940f1b9a3ddbc2abed61c9b7272da85ba99aff8d5", "size_in_bytes": 28158}, {"_path": "Lib/site-packages/setuptools/__pycache__/depends.cpython-311.pyc", "path_type": "hardlink", "sha256": "436d004b281b20019511040200113f04286c29b396f3d99674908cff4377012f", "sha256_in_prefix": "436d004b281b20019511040200113f04286c29b396f3d99674908cff4377012f", "size_in_bytes": 8172}, {"_path": "Lib/site-packages/setuptools/__pycache__/discovery.cpython-311.pyc", "path_type": "hardlink", "sha256": "17819523e044fb23bcf2f9af78dc6e15a2c266bc366c86bb2e33ee0164c0a91f", "sha256_in_prefix": "17819523e044fb23bcf2f9af78dc6e15a2c266bc366c86bb2e33ee0164c0a91f", "size_in_bytes": 31099}, {"_path": "Lib/site-packages/setuptools/__pycache__/dist.cpython-311.pyc", "path_type": "hardlink", "sha256": "9a5ff4d2265b0d9bbeab292b8f0773bd1828631529ab584a5556e6d07ccf0a00", "sha256_in_prefix": "9a5ff4d2265b0d9bbeab292b8f0773bd1828631529ab584a5556e6d07ccf0a00", "size_in_bytes": 58398}, {"_path": "Lib/site-packages/setuptools/__pycache__/errors.cpython-311.pyc", "path_type": "hardlink", "sha256": "d8202cb2784a981b48effdb6afac7bbe31d5c93c18316764034b6569cca25c1d", "sha256_in_prefix": "d8202cb2784a981b48effdb6afac7bbe31d5c93c18316764034b6569cca25c1d", "size_in_bytes": 3514}, {"_path": "Lib/site-packages/setuptools/__pycache__/extension.cpython-311.pyc", "path_type": "hardlink", "sha256": "0b1139ec2a20d51f383fb3eeffb9f240fc02a651d6d27e04db863cfd7f24469c", "sha256_in_prefix": "0b1139ec2a20d51f383fb3eeffb9f240fc02a651d6d27e04db863cfd7f24469c", "size_in_bytes": 7288}, {"_path": "Lib/site-packages/setuptools/__pycache__/glob.cpython-311.pyc", "path_type": "hardlink", "sha256": "49a3f66a679587e61e29b0a66b147c3280dfb7fe322280383b50b09b6316773a", "sha256_in_prefix": "49a3f66a679587e61e29b0a66b147c3280dfb7fe322280383b50b09b6316773a", "size_in_bytes": 8331}, {"_path": "Lib/site-packages/setuptools/__pycache__/installer.cpython-311.pyc", "path_type": "hardlink", "sha256": "e28457ea8970d2b5af2d18dd351d33cdc644d40fd500835183367f71916ed96e", "sha256_in_prefix": "e28457ea8970d2b5af2d18dd351d33cdc644d40fd500835183367f71916ed96e", "size_in_bytes": 7404}, {"_path": "Lib/site-packages/setuptools/__pycache__/launch.cpython-311.pyc", "path_type": "hardlink", "sha256": "7d881fb3153d0190d5368be3acc94915e50a395142787ccc43f8277e06ef7838", "sha256_in_prefix": "7d881fb3153d0190d5368be3acc94915e50a395142787ccc43f8277e06ef7838", "size_in_bytes": 1515}, {"_path": "Lib/site-packages/setuptools/__pycache__/logging.cpython-311.pyc", "path_type": "hardlink", "sha256": "b01d97cd14a437b7063909f8e050c439bf9057134e9b1f1db5a10ae2a50c5e53", "sha256_in_prefix": "b01d97cd14a437b7063909f8e050c439bf9057134e9b1f1db5a10ae2a50c5e53", "size_in_bytes": 2119}, {"_path": "Lib/site-packages/setuptools/__pycache__/modified.cpython-311.pyc", "path_type": "hardlink", "sha256": "114d5213c010756cae897192529a8db34fb9d3c4a6e71499958f1ea0f5561a19", "sha256_in_prefix": "114d5213c010756cae897192529a8db34fb9d3c4a6e71499958f1ea0f5561a19", "size_in_bytes": 594}, {"_path": "Lib/site-packages/setuptools/__pycache__/monkey.cpython-311.pyc", "path_type": "hardlink", "sha256": "95657e097809cb1f9a5e5abac8e949e4f355ac8dddb3ba28d6b0e5530a2f5a9b", "sha256_in_prefix": "95657e097809cb1f9a5e5abac8e949e4f355ac8dddb3ba28d6b0e5530a2f5a9b", "size_in_bytes": 5450}, {"_path": "Lib/site-packages/setuptools/__pycache__/msvc.cpython-311.pyc", "path_type": "hardlink", "sha256": "bf1c55fcdbdb1df98e1c4378462632ccd83a1209b0c19e1f87a36833b802c9c2", "sha256_in_prefix": "bf1c55fcdbdb1df98e1c4378462632ccd83a1209b0c19e1f87a36833b802c9c2", "size_in_bytes": 59792}, {"_path": "Lib/site-packages/setuptools/__pycache__/namespaces.cpython-311.pyc", "path_type": "hardlink", "sha256": "742b73928502d536c82afdeed7b093e7cb67276578c925bdeb6c6a1335285abe", "sha256_in_prefix": "742b73928502d536c82afdeed7b093e7cb67276578c925bdeb6c6a1335285abe", "size_in_bytes": 5813}, {"_path": "Lib/site-packages/setuptools/__pycache__/package_index.cpython-311.pyc", "path_type": "hardlink", "sha256": "ed735ba04a7e373be2af105c7ca224b0375d69fc0249ddd9bc766ab8e3ec4a7b", "sha256_in_prefix": "ed735ba04a7e373be2af105c7ca224b0375d69fc0249ddd9bc766ab8e3ec4a7b", "size_in_bytes": 61793}, {"_path": "Lib/site-packages/setuptools/__pycache__/sandbox.cpython-311.pyc", "path_type": "hardlink", "sha256": "9b55d47d51b75fd2259f457f096b29b95a37597933120eea5ac0008910486818", "sha256_in_prefix": "9b55d47d51b75fd2259f457f096b29b95a37597933120eea5ac0008910486818", "size_in_bytes": 27724}, {"_path": "Lib/site-packages/setuptools/__pycache__/unicode_utils.cpython-311.pyc", "path_type": "hardlink", "sha256": "a4b489572435dea6191129054feb59d54bec0895538e90c37e89d6488a000a10", "sha256_in_prefix": "a4b489572435dea6191129054feb59d54bec0895538e90c37e89d6488a000a10", "size_in_bytes": 4945}, {"_path": "Lib/site-packages/setuptools/__pycache__/version.cpython-311.pyc", "path_type": "hardlink", "sha256": "3ab088cdabd6d4f18abcd28c14b675f1a854a67027c9992476b6507681ee3b4f", "sha256_in_prefix": "3ab088cdabd6d4f18abcd28c14b675f1a854a67027c9992476b6507681ee3b4f", "size_in_bytes": 409}, {"_path": "Lib/site-packages/setuptools/__pycache__/warnings.cpython-311.pyc", "path_type": "hardlink", "sha256": "7a27da5f518d2d62cc173c12a205653178ce258ec7b10b902ac6b25e8ab16196", "sha256_in_prefix": "7a27da5f518d2d62cc173c12a205653178ce258ec7b10b902ac6b25e8ab16196", "size_in_bytes": 5746}, {"_path": "Lib/site-packages/setuptools/__pycache__/wheel.cpython-311.pyc", "path_type": "hardlink", "sha256": "a3b711c49ce9b3747757bd3711b244f9dcdd60f74683c83c3321a1a9669dea49", "sha256_in_prefix": "a3b711c49ce9b3747757bd3711b244f9dcdd60f74683c83c3321a1a9669dea49", "size_in_bytes": 15417}, {"_path": "Lib/site-packages/setuptools/__pycache__/windows_support.cpython-311.pyc", "path_type": "hardlink", "sha256": "856f138de78da3dab54f84bb2151ea679788396fc5540a45a1e7b2ff707a4ea8", "sha256_in_prefix": "856f138de78da3dab54f84bb2151ea679788396fc5540a45a1e7b2ff707a4ea8", "size_in_bytes": 1407}, {"_path": "Lib/site-packages/setuptools/_core_metadata.py", "path_type": "hardlink", "sha256": "4fb4e3a7e592a0df3cd5a75ebf7475c335c23e79031ea6c2d8c83294dd728d2f", "sha256_in_prefix": "4fb4e3a7e592a0df3cd5a75ebf7475c335c23e79031ea6c2d8c83294dd728d2f", "size_in_bytes": 11978}, {"_path": "Lib/site-packages/setuptools/_distutils/__init__.py", "path_type": "hardlink", "sha256": "c4662e856c0b1b4ec9d10e3d0559c48cfcbac320dc77abde24c0c95fb9639723", "sha256_in_prefix": "c4662e856c0b1b4ec9d10e3d0559c48cfcbac320dc77abde24c0c95fb9639723", "size_in_bytes": 359}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "b3ae5888da0f237262459a5c94386637df0080e58854806b4a5bac54508094b8", "sha256_in_prefix": "b3ae5888da0f237262459a5c94386637df0080e58854806b4a5bac54508094b8", "size_in_bytes": 523}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/_log.cpython-311.pyc", "path_type": "hardlink", "sha256": "cbe489b418652f18f7fa5d8fab705a86125db7b583ab46c1a915ece31e60a230", "sha256_in_prefix": "cbe489b418652f18f7fa5d8fab705a86125db7b583ab46c1a915ece31e60a230", "size_in_bytes": 240}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/_macos_compat.cpython-311.pyc", "path_type": "hardlink", "sha256": "90eff9e4b8f743f9015a056047cca7ae52f3c6d464694e95af6364d4728366db", "sha256_in_prefix": "90eff9e4b8f743f9015a056047cca7ae52f3c6d464694e95af6364d4728366db", "size_in_bytes": 534}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/_modified.cpython-311.pyc", "path_type": "hardlink", "sha256": "66bb065f176510111a94e0b532895a50a623d1e1fdd3368ec7ee2202613e4b89", "sha256_in_prefix": "66bb065f176510111a94e0b532895a50a623d1e1fdd3368ec7ee2202613e4b89", "size_in_bytes": 5101}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/_msvccompiler.cpython-311.pyc", "path_type": "hardlink", "sha256": "ddb353c8c6d84db9155f884740604f7d019b2ea74f08cec4e3953afc042a21ed", "sha256_in_prefix": "ddb353c8c6d84db9155f884740604f7d019b2ea74f08cec4e3953afc042a21ed", "size_in_bytes": 721}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/archive_util.cpython-311.pyc", "path_type": "hardlink", "sha256": "8aaa69c7a2e724b9b4a26503277c2649fd0a1eb3d7259acf6d88de578ce33634", "sha256_in_prefix": "8aaa69c7a2e724b9b4a26503277c2649fd0a1eb3d7259acf6d88de578ce33634", "size_in_bytes": 11261}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/ccompiler.cpython-311.pyc", "path_type": "hardlink", "sha256": "b35b18d1a582f4a05e55d7d0c6e988c1d91860c56d41fa6d904c4e1ffd1cd767", "sha256_in_prefix": "b35b18d1a582f4a05e55d7d0c6e988c1d91860c56d41fa6d904c4e1ffd1cd767", "size_in_bytes": 783}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/cmd.cpython-311.pyc", "path_type": "hardlink", "sha256": "c08eb51eeeeee7d32e3e0fc978634fca09864bd42fe9a74cc24b7e6013811ccb", "sha256_in_prefix": "c08eb51eeeeee7d32e3e0fc978634fca09864bd42fe9a74cc24b7e6013811ccb", "size_in_bytes": 23510}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/core.cpython-311.pyc", "path_type": "hardlink", "sha256": "10af01f73eb607ad031f8f4e38fb2102a623bf4ce1b6cb288fcb137be4fb20a7", "sha256_in_prefix": "10af01f73eb607ad031f8f4e38fb2102a623bf4ce1b6cb288fcb137be4fb20a7", "size_in_bytes": 9925}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/cygwinccompiler.cpython-311.pyc", "path_type": "hardlink", "sha256": "a5877d0f6213967b3bf8c1e560003926ad2831d9ad8ae9725aaea5bb4bffde3e", "sha256_in_prefix": "a5877d0f6213967b3bf8c1e560003926ad2831d9ad8ae9725aaea5bb4bffde3e", "size_in_bytes": 689}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/debug.cpython-311.pyc", "path_type": "hardlink", "sha256": "a36b5e31bd92bcc124657741bba5a539a5d523e4d772800e88f72ba58e421bad", "sha256_in_prefix": "a36b5e31bd92bcc124657741bba5a539a5d523e4d772800e88f72ba58e421bad", "size_in_bytes": 286}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/dep_util.cpython-311.pyc", "path_type": "hardlink", "sha256": "bf41f4cff6f6d50f9c572cb439d35027249767712380f013fb3665c24538f2c9", "sha256_in_prefix": "bf41f4cff6f6d50f9c572cb439d35027249767712380f013fb3665c24538f2c9", "size_in_bytes": 752}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/dir_util.cpython-311.pyc", "path_type": "hardlink", "sha256": "21cce30228015adcd7544c52883825c545f29c6c8756ca8d3775a3729113d3b7", "sha256_in_prefix": "21cce30228015adcd7544c52883825c545f29c6c8756ca8d3775a3729113d3b7", "size_in_bytes": 11346}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/dist.cpython-311.pyc", "path_type": "hardlink", "sha256": "336330a89e127a3fe01ad409ed4eadf535946ef66e343476ced68f3bafeccd17", "sha256_in_prefix": "336330a89e127a3fe01ad409ed4eadf535946ef66e343476ced68f3bafeccd17", "size_in_bytes": 62142}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/errors.cpython-311.pyc", "path_type": "hardlink", "sha256": "c7b3de55f5b2632af554943c4768554b00f4af7e1720d567194ace360347c1bc", "sha256_in_prefix": "c7b3de55f5b2632af554943c4768554b00f4af7e1720d567194ace360347c1bc", "size_in_bytes": 5283}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/extension.cpython-311.pyc", "path_type": "hardlink", "sha256": "8631fbcf97986b4ee0ced174c31b339820c796e1eb78c8d3f375b5a61ed9f36e", "sha256_in_prefix": "8631fbcf97986b4ee0ced174c31b339820c796e1eb78c8d3f375b5a61ed9f36e", "size_in_bytes": 10881}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/fancy_getopt.cpython-311.pyc", "path_type": "hardlink", "sha256": "3dc2997664a9e3a76eaef3054e253dbb3c7c48b02ba618e151c0afde86f4ff6d", "sha256_in_prefix": "3dc2997664a9e3a76eaef3054e253dbb3c7c48b02ba618e151c0afde86f4ff6d", "size_in_bytes": 17509}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/file_util.cpython-311.pyc", "path_type": "hardlink", "sha256": "2fd718ed915da9f6a20519d0bb133d655cc8000fbcbb52680e76a2df88171585", "sha256_in_prefix": "2fd718ed915da9f6a20519d0bb133d655cc8000fbcbb52680e76a2df88171585", "size_in_bytes": 10558}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/filelist.cpython-311.pyc", "path_type": "hardlink", "sha256": "ec250105d3d10490c7e5ac9c6f225ff9b7b123e84d48e3f4b745700a67dcb2ee", "sha256_in_prefix": "ec250105d3d10490c7e5ac9c6f225ff9b7b123e84d48e3f4b745700a67dcb2ee", "size_in_bytes": 19542}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/log.cpython-311.pyc", "path_type": "hardlink", "sha256": "f3a5cbfb690d0b38a70b76fa2e0b979cd245b91d3856b388bd8ebbd8a4694f60", "sha256_in_prefix": "f3a5cbfb690d0b38a70b76fa2e0b979cd245b91d3856b388bd8ebbd8a4694f60", "size_in_bytes": 2655}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/spawn.cpython-311.pyc", "path_type": "hardlink", "sha256": "7976e0d8ef12c368a5eabc28abe64500d0432aefd6c0b14cb1188e5cdd9a75fb", "sha256_in_prefix": "7976e0d8ef12c368a5eabc28abe64500d0432aefd6c0b14cb1188e5cdd9a75fb", "size_in_bytes": 6128}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/sysconfig.cpython-311.pyc", "path_type": "hardlink", "sha256": "d862fb8b40130143339c58a922d4542c200bc6acae6ebe8c9025a80d96788b7b", "sha256_in_prefix": "d862fb8b40130143339c58a922d4542c200bc6acae6ebe8c9025a80d96788b7b", "size_in_bytes": 25068}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/text_file.cpython-311.pyc", "path_type": "hardlink", "sha256": "21fb27393dd1e79c3b7f9eaec3e4edbec9feab837af96cad05551c312d53bc17", "sha256_in_prefix": "21fb27393dd1e79c3b7f9eaec3e4edbec9feab837af96cad05551c312d53bc17", "size_in_bytes": 11293}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/unixccompiler.cpython-311.pyc", "path_type": "hardlink", "sha256": "e31c1215e25fb186cc514dcbc8516f0e918eb1300628e26d96bc578643f0ece5", "sha256_in_prefix": "e31c1215e25fb186cc514dcbc8516f0e918eb1300628e26d96bc578643f0ece5", "size_in_bytes": 391}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/util.cpython-311.pyc", "path_type": "hardlink", "sha256": "d9554a106fb9b97855ae5a9fc090886dbf7f736dc5dd36e2ffd4e7085c2bfee9", "sha256_in_prefix": "d9554a106fb9b97855ae5a9fc090886dbf7f736dc5dd36e2ffd4e7085c2bfee9", "size_in_bytes": 21338}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/version.cpython-311.pyc", "path_type": "hardlink", "sha256": "3df0ab1bf85feb2d99a7dd66019a563c43b7df6765949217363f331d4466c582", "sha256_in_prefix": "3df0ab1bf85feb2d99a7dd66019a563c43b7df6765949217363f331d4466c582", "size_in_bytes": 11488}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/versionpredicate.cpython-311.pyc", "path_type": "hardlink", "sha256": "5f9c9d1caeef9fd67ba5299bf4b388188bde29c725f41bac198ee836d53f7f12", "sha256_in_prefix": "5f9c9d1caeef9fd67ba5299bf4b388188bde29c725f41bac198ee836d53f7f12", "size_in_bytes": 7586}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/zosccompiler.cpython-311.pyc", "path_type": "hardlink", "sha256": "991b28aa86ae1eaed00f7aa828fa455f58b91078761554e5b0c58990ab75391a", "sha256_in_prefix": "991b28aa86ae1eaed00f7aa828fa455f58b91078761554e5b0c58990ab75391a", "size_in_bytes": 260}, {"_path": "Lib/site-packages/setuptools/_distutils/_log.py", "path_type": "hardlink", "sha256": "8be94d4d37174bc4e65884c9e833831afb56e73e6d31ab6d250efa87cad9c505", "sha256_in_prefix": "8be94d4d37174bc4e65884c9e833831afb56e73e6d31ab6d250efa87cad9c505", "size_in_bytes": 42}, {"_path": "Lib/site-packages/setuptools/_distutils/_macos_compat.py", "path_type": "hardlink", "sha256": "273506845e04e722084c76d468fa1b6445a318776badc355eb7cfce92e118c17", "sha256_in_prefix": "273506845e04e722084c76d468fa1b6445a318776badc355eb7cfce92e118c17", "size_in_bytes": 239}, {"_path": "Lib/site-packages/setuptools/_distutils/_modified.py", "path_type": "hardlink", "sha256": "445d67d427b1c83615de5bc66de5d2d2cf9708955ba0338851b03cc0442a6136", "sha256_in_prefix": "445d67d427b1c83615de5bc66de5d2d2cf9708955ba0338851b03cc0442a6136", "size_in_bytes": 3211}, {"_path": "Lib/site-packages/setuptools/_distutils/_msvccompiler.py", "path_type": "hardlink", "sha256": "f4f49f487c6f2671e740be92ab3e17733ee2681213eb6a7a061790cc6b12970a", "sha256_in_prefix": "f4f49f487c6f2671e740be92ab3e17733ee2681213eb6a7a061790cc6b12970a", "size_in_bytes": 335}, {"_path": "Lib/site-packages/setuptools/_distutils/archive_util.py", "path_type": "hardlink", "sha256": "430db3f8fb7e355f2535442bce3b375c31960961cc3e7a872f2b7c4e20f65c40", "sha256_in_prefix": "430db3f8fb7e355f2535442bce3b375c31960961cc3e7a872f2b7c4e20f65c40", "size_in_bytes": 8884}, {"_path": "Lib/site-packages/setuptools/_distutils/ccompiler.py", "path_type": "hardlink", "sha256": "14a563ab3189edcf85b68b8d8e12e268c3e6e4b28c6471c0aee5dff0b536d7a7", "sha256_in_prefix": "14a563ab3189edcf85b68b8d8e12e268c3e6e4b28c6471c0aee5dff0b536d7a7", "size_in_bytes": 524}, {"_path": "Lib/site-packages/setuptools/_distutils/cmd.py", "path_type": "hardlink", "sha256": "857b5a45a1fb4019df34e22a12f0ade3b8b06730fd315bc176185d41cb47b313", "sha256_in_prefix": "857b5a45a1fb4019df34e22a12f0ade3b8b06730fd315bc176185d41cb47b313", "size_in_bytes": 22186}, {"_path": "Lib/site-packages/setuptools/_distutils/command/__init__.py", "path_type": "hardlink", "sha256": "19f140cdb06a935ab1487e0175a2a2a0a4b88514670f8e01026c0437ce42e2ef", "sha256_in_prefix": "19f140cdb06a935ab1487e0175a2a2a0a4b88514670f8e01026c0437ce42e2ef", "size_in_bytes": 386}, {"_path": "Lib/site-packages/setuptools/_distutils/command/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "6b8ef92f6077bb69df07ac9a21a5fd5cc77676382286fdd45cf0748e86185ae4", "sha256_in_prefix": "6b8ef92f6077bb69df07ac9a21a5fd5cc77676382286fdd45cf0748e86185ae4", "size_in_bytes": 488}, {"_path": "Lib/site-packages/setuptools/_distutils/command/__pycache__/_framework_compat.cpython-311.pyc", "path_type": "hardlink", "sha256": "5afac5817e0ea91e27780ee23bd15ad32f3aaa0836f5033c047382a628dae27f", "sha256_in_prefix": "5afac5817e0ea91e27780ee23bd15ad32f3aaa0836f5033c047382a628dae27f", "size_in_bytes": 2699}, {"_path": "Lib/site-packages/setuptools/_distutils/command/__pycache__/bdist.cpython-311.pyc", "path_type": "hardlink", "sha256": "8b5dcd34124ac3a3fc02bd046a81f4978bde15a43502f6dec45a4b1161fbbc33", "sha256_in_prefix": "8b5dcd34124ac3a3fc02bd046a81f4978bde15a43502f6dec45a4b1161fbbc33", "size_in_bytes": 7146}, {"_path": "Lib/site-packages/setuptools/_distutils/command/__pycache__/bdist_dumb.cpython-311.pyc", "path_type": "hardlink", "sha256": "713b45945705eef84c079055c8f9825203f0d354ad353f230f774e3e2e91e532", "sha256_in_prefix": "713b45945705eef84c079055c8f9825203f0d354ad353f230f774e3e2e91e532", "size_in_bytes": 5739}, {"_path": "Lib/site-packages/setuptools/_distutils/command/__pycache__/bdist_rpm.cpython-311.pyc", "path_type": "hardlink", "sha256": "59a746ede93b8597266323db4fdb82e2801b763cd1ae7df34464663322ff1c7a", "sha256_in_prefix": "59a746ede93b8597266323db4fdb82e2801b763cd1ae7df34464663322ff1c7a", "size_in_bytes": 23411}, {"_path": "Lib/site-packages/setuptools/_distutils/command/__pycache__/build.cpython-311.pyc", "path_type": "hardlink", "sha256": "7144778c1344ecb6b23887c1b92c0f1858f80f9498ff044c3c4a6c640eadd393", "sha256_in_prefix": "7144778c1344ecb6b23887c1b92c0f1858f80f9498ff044c3c4a6c640eadd393", "size_in_bytes": 6430}, {"_path": "Lib/site-packages/setuptools/_distutils/command/__pycache__/build_clib.cpython-311.pyc", "path_type": "hardlink", "sha256": "876e97d75301029b3c82a694a4bedaa7dccfc2b3376476eb0172704de68681f0", "sha256_in_prefix": "876e97d75301029b3c82a694a4bedaa7dccfc2b3376476eb0172704de68681f0", "size_in_bytes": 8063}, {"_path": "Lib/site-packages/setuptools/_distutils/command/__pycache__/build_ext.cpython-311.pyc", "path_type": "hardlink", "sha256": "7347117dc4a8f8bcc71d7d3a316e9af4b35b92da0cf9546f9ad1d41b5ee36a5b", "sha256_in_prefix": "7347117dc4a8f8bcc71d7d3a316e9af4b35b92da0cf9546f9ad1d41b5ee36a5b", "size_in_bytes": 32042}, {"_path": "Lib/site-packages/setuptools/_distutils/command/__pycache__/build_py.cpython-311.pyc", "path_type": "hardlink", "sha256": "39897727d788530149ba5f220bf4ee9a607868a4569631d390ea913727e177f9", "sha256_in_prefix": "39897727d788530149ba5f220bf4ee9a607868a4569631d390ea913727e177f9", "size_in_bytes": 17945}, {"_path": "Lib/site-packages/setuptools/_distutils/command/__pycache__/build_scripts.cpython-311.pyc", "path_type": "hardlink", "sha256": "0ac6a4acf6ca436e2479e5bb10eaacb1bb3e5edfba1bc2506bac72f26509f12d", "sha256_in_prefix": "0ac6a4acf6ca436e2479e5bb10eaacb1bb3e5edfba1bc2506bac72f26509f12d", "size_in_bytes": 7529}, {"_path": "Lib/site-packages/setuptools/_distutils/command/__pycache__/check.cpython-311.pyc", "path_type": "hardlink", "sha256": "0d521eee541b941120333e5bacbe7b7274d99b5d389732d8a51144fc125001f5", "sha256_in_prefix": "0d521eee541b941120333e5bacbe7b7274d99b5d389732d8a51144fc125001f5", "size_in_bytes": 7924}, {"_path": "Lib/site-packages/setuptools/_distutils/command/__pycache__/clean.cpython-311.pyc", "path_type": "hardlink", "sha256": "8596f8d4acb17c6abdbcd75902d8fff0ed88e315f38c4e170235edd05c20ec42", "sha256_in_prefix": "8596f8d4acb17c6abdbcd75902d8fff0ed88e315f38c4e170235edd05c20ec42", "size_in_bytes": 3301}, {"_path": "Lib/site-packages/setuptools/_distutils/command/__pycache__/config.cpython-311.pyc", "path_type": "hardlink", "sha256": "feb20a3a9fefc32d2636a9fe734b5203522bb57c440bd9f33303845db3c1c6d5", "sha256_in_prefix": "feb20a3a9fefc32d2636a9fe734b5203522bb57c440bd9f33303845db3c1c6d5", "size_in_bytes": 16379}, {"_path": "Lib/site-packages/setuptools/_distutils/command/__pycache__/install.cpython-311.pyc", "path_type": "hardlink", "sha256": "cc55def7e501a6def0b5f1488169319187de047d838e067f1b7549d0baf591c1", "sha256_in_prefix": "cc55def7e501a6def0b5f1488169319187de047d838e067f1b7549d0baf591c1", "size_in_bytes": 29091}, {"_path": "Lib/site-packages/setuptools/_distutils/command/__pycache__/install_data.cpython-311.pyc", "path_type": "hardlink", "sha256": "6363f94ee557fc5789440c7d6201c68146343bf411295f49a5db8b40234da950", "sha256_in_prefix": "6363f94ee557fc5789440c7d6201c68146343bf411295f49a5db8b40234da950", "size_in_bytes": 4739}, {"_path": "Lib/site-packages/setuptools/_distutils/command/__pycache__/install_egg_info.cpython-311.pyc", "path_type": "hardlink", "sha256": "b475b52f6383b0e639b1036b107a255d7c6af93bdcf9dbff9aaad8691f72ff99", "sha256_in_prefix": "b475b52f6383b0e639b1036b107a255d7c6af93bdcf9dbff9aaad8691f72ff99", "size_in_bytes": 5453}, {"_path": "Lib/site-packages/setuptools/_distutils/command/__pycache__/install_headers.cpython-311.pyc", "path_type": "hardlink", "sha256": "ac9c59d22c7ead75357ff761e15b9eebe88c663932930748038568edaeccabc7", "sha256_in_prefix": "ac9c59d22c7ead75357ff761e15b9eebe88c663932930748038568edaeccabc7", "size_in_bytes": 2569}, {"_path": "Lib/site-packages/setuptools/_distutils/command/__pycache__/install_lib.cpython-311.pyc", "path_type": "hardlink", "sha256": "1ba3a187d249e152e10c1b316164a01b5e4d79e145b7009b543306060eb93356", "sha256_in_prefix": "1ba3a187d249e152e10c1b316164a01b5e4d79e145b7009b543306060eb93356", "size_in_bytes": 9095}, {"_path": "Lib/site-packages/setuptools/_distutils/command/__pycache__/install_scripts.cpython-311.pyc", "path_type": "hardlink", "sha256": "5950838e2dc55dcb2491a38a53853894fc6a45bd5c51f4fecc4407ad4fc91c9c", "sha256_in_prefix": "5950838e2dc55dcb2491a38a53853894fc6a45bd5c51f4fecc4407ad4fc91c9c", "size_in_bytes": 3299}, {"_path": "Lib/site-packages/setuptools/_distutils/command/__pycache__/sdist.cpython-311.pyc", "path_type": "hardlink", "sha256": "e49ebf28a378d99198ef4222747e16e94313e24ebfb92b246eac1fa365971b98", "sha256_in_prefix": "e49ebf28a378d99198ef4222747e16e94313e24ebfb92b246eac1fa365971b98", "size_in_bytes": 24290}, {"_path": "Lib/site-packages/setuptools/_distutils/command/_framework_compat.py", "path_type": "hardlink", "sha256": "d2265d4896331915820afcd10ca13e474fbfc9a018bc531dd729576f67985ee8", "sha256_in_prefix": "d2265d4896331915820afcd10ca13e474fbfc9a018bc531dd729576f67985ee8", "size_in_bytes": 1609}, {"_path": "Lib/site-packages/setuptools/_distutils/command/bdist.py", "path_type": "hardlink", "sha256": "8d6b64eb547b7d635450dc49574b614d9cd4e67f342f7032d7069288ff6488b0", "sha256_in_prefix": "8d6b64eb547b7d635450dc49574b614d9cd4e67f342f7032d7069288ff6488b0", "size_in_bytes": 5854}, {"_path": "Lib/site-packages/setuptools/_distutils/command/bdist_dumb.py", "path_type": "hardlink", "sha256": "1f1d6302aa19371608cb83794cbcd4a7a2797e2f0bb35f29cbb5252cd1613b61", "sha256_in_prefix": "1f1d6302aa19371608cb83794cbcd4a7a2797e2f0bb35f29cbb5252cd1613b61", "size_in_bytes": 4631}, {"_path": "Lib/site-packages/setuptools/_distutils/command/bdist_rpm.py", "path_type": "hardlink", "sha256": "9f17175efe5aec1fb59ed5aee036c6982b444b810120dac968141c44d0180892", "sha256_in_prefix": "9f17175efe5aec1fb59ed5aee036c6982b444b810120dac968141c44d0180892", "size_in_bytes": 21785}, {"_path": "Lib/site-packages/setuptools/_distutils/command/build.py", "path_type": "hardlink", "sha256": "4a91e56a07f488d68a572221c437e13c567c5f5f8b0163824000b2fb2b762b4c", "sha256_in_prefix": "4a91e56a07f488d68a572221c437e13c567c5f5f8b0163824000b2fb2b762b4c", "size_in_bytes": 5923}, {"_path": "Lib/site-packages/setuptools/_distutils/command/build_clib.py", "path_type": "hardlink", "sha256": "68ca997147c26ce02eff1afe03d896f90f58647ce90c62d14decce80c4099924", "sha256_in_prefix": "68ca997147c26ce02eff1afe03d896f90f58647ce90c62d14decce80c4099924", "size_in_bytes": 7777}, {"_path": "Lib/site-packages/setuptools/_distutils/command/build_ext.py", "path_type": "hardlink", "sha256": "cebaecbbd1d79f357a6d761b26e6422b84b05593232a7978a46d68ddb35cc6d7", "sha256_in_prefix": "cebaecbbd1d79f357a6d761b26e6422b84b05593232a7978a46d68ddb35cc6d7", "size_in_bytes": 32710}, {"_path": "Lib/site-packages/setuptools/_distutils/command/build_py.py", "path_type": "hardlink", "sha256": "55fabe20d7a6a0c6e0e9fd34dc14f2fd47e9f1b8ce661985221a4a31c7d72e0b", "sha256_in_prefix": "55fabe20d7a6a0c6e0e9fd34dc14f2fd47e9f1b8ce661985221a4a31c7d72e0b", "size_in_bytes": 16696}, {"_path": "Lib/site-packages/setuptools/_distutils/command/build_scripts.py", "path_type": "hardlink", "sha256": "b54a44cf04ec9eb3fcaab368af2de574f076e3440308590ca7ea5d60fb36c139", "sha256_in_prefix": "b54a44cf04ec9eb3fcaab368af2de574f076e3440308590ca7ea5d60fb36c139", "size_in_bytes": 5118}, {"_path": "Lib/site-packages/setuptools/_distutils/command/check.py", "path_type": "hardlink", "sha256": "ca835ed8c3d8e0971333baf0a0841d7d9ef9ab9462d39f08d9ca22f86abd0a33", "sha256_in_prefix": "ca835ed8c3d8e0971333baf0a0841d7d9ef9ab9462d39f08d9ca22f86abd0a33", "size_in_bytes": 4946}, {"_path": "Lib/site-packages/setuptools/_distutils/command/clean.py", "path_type": "hardlink", "sha256": "75001a70e69bc015d4f49a19fb5185bacab778596d0da7972454989dca866ef1", "sha256_in_prefix": "75001a70e69bc015d4f49a19fb5185bacab778596d0da7972454989dca866ef1", "size_in_bytes": 2644}, {"_path": "Lib/site-packages/setuptools/_distutils/command/config.py", "path_type": "hardlink", "sha256": "06e51d3eef75568f70e38c730f54507e2c977d27d570da5e5f769ea0a70600ec", "sha256_in_prefix": "06e51d3eef75568f70e38c730f54507e2c977d27d570da5e5f769ea0a70600ec", "size_in_bytes": 12818}, {"_path": "Lib/site-packages/setuptools/_distutils/command/install.py", "path_type": "hardlink", "sha256": "f897a707e9ae6b885cd9123ff96f05f4f9cffc9f8e6853bb1343c918ac4ba35a", "sha256_in_prefix": "f897a707e9ae6b885cd9123ff96f05f4f9cffc9f8e6853bb1343c918ac4ba35a", "size_in_bytes": 30072}, {"_path": "Lib/site-packages/setuptools/_distutils/command/install_data.py", "path_type": "hardlink", "sha256": "1b306551658ab9b4d82653fe2f46ae52b8aaf5c2fee5128e728c874edb4a8f44", "sha256_in_prefix": "1b306551658ab9b4d82653fe2f46ae52b8aaf5c2fee5128e728c874edb4a8f44", "size_in_bytes": 2875}, {"_path": "Lib/site-packages/setuptools/_distutils/command/install_egg_info.py", "path_type": "hardlink", "sha256": "7df88ba14d62bd027cab6fd62fb6728196d470eb207452ca2fba2d1082565a42", "sha256_in_prefix": "7df88ba14d62bd027cab6fd62fb6728196d470eb207452ca2fba2d1082565a42", "size_in_bytes": 2868}, {"_path": "Lib/site-packages/setuptools/_distutils/command/install_headers.py", "path_type": "hardlink", "sha256": "e5c88a0a3f1cdd72ac60d29d91d32f9f2a5a50229ca1608379e6628f77c3f99e", "sha256_in_prefix": "e5c88a0a3f1cdd72ac60d29d91d32f9f2a5a50229ca1608379e6628f77c3f99e", "size_in_bytes": 1272}, {"_path": "Lib/site-packages/setuptools/_distutils/command/install_lib.py", "path_type": "hardlink", "sha256": "dacf7e9b9f9bd6a2a6e75176f250792f7f59eafbff187325bfd74d052ba9a24d", "sha256_in_prefix": "dacf7e9b9f9bd6a2a6e75176f250792f7f59eafbff187325bfd74d052ba9a24d", "size_in_bytes": 8588}, {"_path": "Lib/site-packages/setuptools/_distutils/command/install_scripts.py", "path_type": "hardlink", "sha256": "334a4f7626aa07b4c69aa4ccba3a4619e88bd08abf0937868cc16dae60e6c333", "sha256_in_prefix": "334a4f7626aa07b4c69aa4ccba3a4619e88bd08abf0937868cc16dae60e6c333", "size_in_bytes": 2002}, {"_path": "Lib/site-packages/setuptools/_distutils/command/sdist.py", "path_type": "hardlink", "sha256": "711205e87b75849e9ac8e38557270c14150dc63a3de1efeb1583f1e078bc0217", "sha256_in_prefix": "711205e87b75849e9ac8e38557270c14150dc63a3de1efeb1583f1e078bc0217", "size_in_bytes": 19151}, {"_path": "Lib/site-packages/setuptools/_distutils/compat/__init__.py", "path_type": "hardlink", "sha256": "276d1a5c68c9f3a460e35c452c85a57160a067d79d31d27dbef74d110f3bbcf4", "sha256_in_prefix": "276d1a5c68c9f3a460e35c452c85a57160a067d79d31d27dbef74d110f3bbcf4", "size_in_bytes": 522}, {"_path": "Lib/site-packages/setuptools/_distutils/compat/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "2c4ed1522936483e5a539c346c469bb9b157b875f7d1c0917c283bcd99ecee12", "sha256_in_prefix": "2c4ed1522936483e5a539c346c469bb9b157b875f7d1c0917c283bcd99ecee12", "size_in_bytes": 1469}, {"_path": "Lib/site-packages/setuptools/_distutils/compat/__pycache__/numpy.cpython-311.pyc", "path_type": "hardlink", "sha256": "af3f7aa7a81d1db03dbe5147b3bc781c2a41fde30bf664efdd3522a6c997de5e", "sha256_in_prefix": "af3f7aa7a81d1db03dbe5147b3bc781c2a41fde30bf664efdd3522a6c997de5e", "size_in_bytes": 268}, {"_path": "Lib/site-packages/setuptools/_distutils/compat/__pycache__/py39.cpython-311.pyc", "path_type": "hardlink", "sha256": "48413af2fe33feb2779303cfc88e555a93235368833ac6e11fdf16b19ced81b6", "sha256_in_prefix": "48413af2fe33feb2779303cfc88e555a93235368833ac6e11fdf16b19ced81b6", "size_in_bytes": 2997}, {"_path": "Lib/site-packages/setuptools/_distutils/compat/numpy.py", "path_type": "hardlink", "sha256": "505827799c3dc3dee0e1cfb21a80083b22f150e590f9f3d122185f32ceff3ae7", "sha256_in_prefix": "505827799c3dc3dee0e1cfb21a80083b22f150e590f9f3d122185f32ceff3ae7", "size_in_bytes": 167}, {"_path": "Lib/site-packages/setuptools/_distutils/compat/py39.py", "path_type": "hardlink", "sha256": "84eb03ea5c192ea66832769c349dcfea7500f8b250844a55b584f3547d28f7a3", "sha256_in_prefix": "84eb03ea5c192ea66832769c349dcfea7500f8b250844a55b584f3547d28f7a3", "size_in_bytes": 1964}, {"_path": "Lib/site-packages/setuptools/_distutils/compilers/C/__pycache__/base.cpython-311.pyc", "path_type": "hardlink", "sha256": "4587b1dddd334dd7fea17f99735b53500267cddfc648b4ac829c2baf57a0430a", "sha256_in_prefix": "4587b1dddd334dd7fea17f99735b53500267cddfc648b4ac829c2baf57a0430a", "size_in_bytes": 54627}, {"_path": "Lib/site-packages/setuptools/_distutils/compilers/C/__pycache__/cygwin.cpython-311.pyc", "path_type": "hardlink", "sha256": "8da05f7a9d2f3044f25c6dc15fc3df4374ab5d2f7ca4b39322896caf7a7597e4", "sha256_in_prefix": "8da05f7a9d2f3044f25c6dc15fc3df4374ab5d2f7ca4b39322896caf7a7597e4", "size_in_bytes": 13052}, {"_path": "Lib/site-packages/setuptools/_distutils/compilers/C/__pycache__/errors.cpython-311.pyc", "path_type": "hardlink", "sha256": "53a74d572e4c950bc63dd4835c0adba222f2289eb1d5d3a8f3c522d97846e0e8", "sha256_in_prefix": "53a74d572e4c950bc63dd4835c0adba222f2289eb1d5d3a8f3c522d97846e0e8", "size_in_bytes": 1821}, {"_path": "Lib/site-packages/setuptools/_distutils/compilers/C/__pycache__/msvc.cpython-311.pyc", "path_type": "hardlink", "sha256": "16034a8a0e2c73e9e6437094e27191a77bece5e1f72db90002a31300eba69eb8", "sha256_in_prefix": "16034a8a0e2c73e9e6437094e27191a77bece5e1f72db90002a31300eba69eb8", "size_in_bytes": 27657}, {"_path": "Lib/site-packages/setuptools/_distutils/compilers/C/__pycache__/unix.cpython-311.pyc", "path_type": "hardlink", "sha256": "7cdab81610bff57722b556215ff272bf7954eb4d60fa16e917d2ba7f6b2f6464", "sha256_in_prefix": "7cdab81610bff57722b556215ff272bf7954eb4d60fa16e917d2ba7f6b2f6464", "size_in_bytes": 17248}, {"_path": "Lib/site-packages/setuptools/_distutils/compilers/C/__pycache__/zos.cpython-311.pyc", "path_type": "hardlink", "sha256": "bafc62aa684b84ab25702ca09b7271e7368b5138626bb8907f0335f69c05dd54", "sha256_in_prefix": "bafc62aa684b84ab25702ca09b7271e7368b5138626bb8907f0335f69c05dd54", "size_in_bytes": 7022}, {"_path": "Lib/site-packages/setuptools/_distutils/compilers/C/base.py", "path_type": "hardlink", "sha256": "5d1d6b0424ad0aabaa9bb40e6170f8d7e2dfbec15c3e91b1af0c5e5f32729ffc", "sha256_in_prefix": "5d1d6b0424ad0aabaa9bb40e6170f8d7e2dfbec15c3e91b1af0c5e5f32729ffc", "size_in_bytes": 54876}, {"_path": "Lib/site-packages/setuptools/_distutils/compilers/C/cygwin.py", "path_type": "hardlink", "sha256": "0d49704126f9e5a8fb39d72671d76b98299512311ac48889e611d43b71813cdb", "sha256_in_prefix": "0d49704126f9e5a8fb39d72671d76b98299512311ac48889e611d43b71813cdb", "size_in_bytes": 11844}, {"_path": "Lib/site-packages/setuptools/_distutils/compilers/C/errors.py", "path_type": "hardlink", "sha256": "b0a395cc96a331498d75fcb0a3d50cfd0406b0a15c7250e1b48e5394289730b7", "sha256_in_prefix": "b0a395cc96a331498d75fcb0a3d50cfd0406b0a15c7250e1b48e5394289730b7", "size_in_bytes": 573}, {"_path": "Lib/site-packages/setuptools/_distutils/compilers/C/msvc.py", "path_type": "hardlink", "sha256": "3bcce8e8d2830300aebf917414f65e02ec986fb0055c82ede4db676463e5c8d8", "sha256_in_prefix": "3bcce8e8d2830300aebf917414f65e02ec986fb0055c82ede4db676463e5c8d8", "size_in_bytes": 21802}, {"_path": "Lib/site-packages/setuptools/_distutils/compilers/C/tests/__pycache__/test_base.cpython-311.pyc", "path_type": "hardlink", "sha256": "59794831511007e631fbdc87f0ebcc70246270ada9db0ae72e52a45986268085", "sha256_in_prefix": "59794831511007e631fbdc87f0ebcc70246270ada9db0ae72e52a45986268085", "size_in_bytes": 4630}, {"_path": "Lib/site-packages/setuptools/_distutils/compilers/C/tests/__pycache__/test_cygwin.cpython-311.pyc", "path_type": "hardlink", "sha256": "6896cfb4d7696e252793b4cc5a5ba70435c6a2878782316c9e85b1eefd5b669a", "sha256_in_prefix": "6896cfb4d7696e252793b4cc5a5ba70435c6a2878782316c9e85b1eefd5b669a", "size_in_bytes": 4883}, {"_path": "Lib/site-packages/setuptools/_distutils/compilers/C/tests/__pycache__/test_mingw.cpython-311.pyc", "path_type": "hardlink", "sha256": "a58080e61da7150e723d65454ef9a32727e993575ea6a142837107d00f0e3c3c", "sha256_in_prefix": "a58080e61da7150e723d65454ef9a32727e993575ea6a142837107d00f0e3c3c", "size_in_bytes": 4372}, {"_path": "Lib/site-packages/setuptools/_distutils/compilers/C/tests/__pycache__/test_msvc.cpython-311.pyc", "path_type": "hardlink", "sha256": "57f6866c98013355e62a14b5f7b10b8c604648d8e0d2de8ece02aab270a3e67d", "sha256_in_prefix": "57f6866c98013355e62a14b5f7b10b8c604648d8e0d2de8ece02aab270a3e67d", "size_in_bytes": 8587}, {"_path": "Lib/site-packages/setuptools/_distutils/compilers/C/tests/__pycache__/test_unix.cpython-311.pyc", "path_type": "hardlink", "sha256": "a2dcd6a6847e244800bcdb010158306ca701bd99df7597d62fab06d050d86198", "sha256_in_prefix": "a2dcd6a6847e244800bcdb010158306ca701bd99df7597d62fab06d050d86198", "size_in_bytes": 17737}, {"_path": "Lib/site-packages/setuptools/_distutils/compilers/C/tests/test_base.py", "path_type": "hardlink", "sha256": "add847739e9b857b66e4d9cdf41487c2be9cebd52accc22d650ce5c3602c74c7", "sha256_in_prefix": "add847739e9b857b66e4d9cdf41487c2be9cebd52accc22d650ce5c3602c74c7", "size_in_bytes": 2706}, {"_path": "Lib/site-packages/setuptools/_distutils/compilers/C/tests/test_cygwin.py", "path_type": "hardlink", "sha256": "5205765605178f756e95c6c373450159f132243c78dad812c12e0bcc78b1de66", "sha256_in_prefix": "5205765605178f756e95c6c373450159f132243c78dad812c12e0bcc78b1de66", "size_in_bytes": 2701}, {"_path": "Lib/site-packages/setuptools/_distutils/compilers/C/tests/test_mingw.py", "path_type": "hardlink", "sha256": "8429b0cb2c084a9468c8ec926c51c12f84e9ad6455d265160ca98e2cef170571", "sha256_in_prefix": "8429b0cb2c084a9468c8ec926c51c12f84e9ad6455d265160ca98e2cef170571", "size_in_bytes": 1900}, {"_path": "Lib/site-packages/setuptools/_distutils/compilers/C/tests/test_msvc.py", "path_type": "hardlink", "sha256": "0e51a3999d660523172209a5bbcd0129ced5f8424e66e62e730270161e5d9f6f", "sha256_in_prefix": "0e51a3999d660523172209a5bbcd0129ced5f8424e66e62e730270161e5d9f6f", "size_in_bytes": 4151}, {"_path": "Lib/site-packages/setuptools/_distutils/compilers/C/tests/test_unix.py", "path_type": "hardlink", "sha256": "12b6d85a2a3b7a363666a4263e4e00c0ebb51c55b8fbff9a65d52f19ad56d85c", "sha256_in_prefix": "12b6d85a2a3b7a363666a4263e4e00c0ebb51c55b8fbff9a65d52f19ad56d85c", "size_in_bytes": 11834}, {"_path": "Lib/site-packages/setuptools/_distutils/compilers/C/unix.py", "path_type": "hardlink", "sha256": "97b0b1638ac3240102268faf72fea2a344819a63c9f4998de664a665c8a7d955", "sha256_in_prefix": "97b0b1638ac3240102268faf72fea2a344819a63c9f4998de664a665c8a7d955", "size_in_bytes": 16502}, {"_path": "Lib/site-packages/setuptools/_distutils/compilers/C/zos.py", "path_type": "hardlink", "sha256": "be735e58b45991d224759f98c819cbf2275351f7023a7d2d2cc5b938127449c5", "sha256_in_prefix": "be735e58b45991d224759f98c819cbf2275351f7023a7d2d2cc5b938127449c5", "size_in_bytes": 6586}, {"_path": "Lib/site-packages/setuptools/_distutils/core.py", "path_type": "hardlink", "sha256": "1841ca6850b8f13de8fbf4a61f8f3ae06bcacb1d4881b542e884883d5971edae", "sha256_in_prefix": "1841ca6850b8f13de8fbf4a61f8f3ae06bcacb1d4881b542e884883d5971edae", "size_in_bytes": 9364}, {"_path": "Lib/site-packages/setuptools/_distutils/cygwinccompiler.py", "path_type": "hardlink", "sha256": "986fdc53c4956786a60ff56d179bc7e815cfd3e920846b033db0d25eb43deb77", "sha256_in_prefix": "986fdc53c4956786a60ff56d179bc7e815cfd3e920846b033db0d25eb43deb77", "size_in_bytes": 594}, {"_path": "Lib/site-packages/setuptools/_distutils/debug.py", "path_type": "hardlink", "sha256": "37a32b4c0a8aea5f52564ead5b0791d74f0f33c3a5eea3657f257e9c770b86c6", "sha256_in_prefix": "37a32b4c0a8aea5f52564ead5b0791d74f0f33c3a5eea3657f257e9c770b86c6", "size_in_bytes": 139}, {"_path": "Lib/site-packages/setuptools/_distutils/dep_util.py", "path_type": "hardlink", "sha256": "c4def9a7a6691e13221c473eae92f65e29494329c79c336269f1ed79a678b635", "sha256_in_prefix": "c4def9a7a6691e13221c473eae92f65e29494329c79c336269f1ed79a678b635", "size_in_bytes": 349}, {"_path": "Lib/site-packages/setuptools/_distutils/dir_util.py", "path_type": "hardlink", "sha256": "0d73d495f5551ac83d07e26083802dfe3f53eef33ad0e8303579101ea4e8efe2", "sha256_in_prefix": "0d73d495f5551ac83d07e26083802dfe3f53eef33ad0e8303579101ea4e8efe2", "size_in_bytes": 7236}, {"_path": "Lib/site-packages/setuptools/_distutils/dist.py", "path_type": "hardlink", "sha256": "816e7df1413458c9335d0437d4dafef0becc3f0d2820ecf9392491cd8665c2b3", "sha256_in_prefix": "816e7df1413458c9335d0437d4dafef0becc3f0d2820ecf9392491cd8665c2b3", "size_in_bytes": 55794}, {"_path": "Lib/site-packages/setuptools/_distutils/errors.py", "path_type": "hardlink", "sha256": "3cf136a03461e72f50d5b78a2bdae176f0da0b34218b81c25ece0a72a7ea8196", "sha256_in_prefix": "3cf136a03461e72f50d5b78a2bdae176f0da0b34218b81c25ece0a72a7ea8196", "size_in_bytes": 3092}, {"_path": "Lib/site-packages/setuptools/_distutils/extension.py", "path_type": "hardlink", "sha256": "168caee2050b70faa6d7f53dceb6181f1364e0daa0957bf5adbb0e93f42b49db", "sha256_in_prefix": "168caee2050b70faa6d7f53dceb6181f1364e0daa0957bf5adbb0e93f42b49db", "size_in_bytes": 11155}, {"_path": "Lib/site-packages/setuptools/_distutils/fancy_getopt.py", "path_type": "hardlink", "sha256": "3e374ef9b5825b48a657f50df8c184c3d47618fd8e884f291e32138264c06374", "sha256_in_prefix": "3e374ef9b5825b48a657f50df8c184c3d47618fd8e884f291e32138264c06374", "size_in_bytes": 17895}, {"_path": "Lib/site-packages/setuptools/_distutils/file_util.py", "path_type": "hardlink", "sha256": "60540bfe90f784bb8447d208fc7ebe8430bf608184a2891d778c1e74bba24d6d", "sha256_in_prefix": "60540bfe90f784bb8447d208fc7ebe8430bf608184a2891d778c1e74bba24d6d", "size_in_bytes": 7978}, {"_path": "Lib/site-packages/setuptools/_distutils/filelist.py", "path_type": "hardlink", "sha256": "30179244998f70a983bfca28660494e018903d9d0a870bfc97f8e10f9d17c9c2", "sha256_in_prefix": "30179244998f70a983bfca28660494e018903d9d0a870bfc97f8e10f9d17c9c2", "size_in_bytes": 15337}, {"_path": "Lib/site-packages/setuptools/_distutils/log.py", "path_type": "hardlink", "sha256": "57206ce63ef3e3e2ba5d310405385473d1f2329a0f2c6b50a4446a6f3e72970c", "sha256_in_prefix": "57206ce63ef3e3e2ba5d310405385473d1f2329a0f2c6b50a4446a6f3e72970c", "size_in_bytes": 1200}, {"_path": "Lib/site-packages/setuptools/_distutils/spawn.py", "path_type": "hardlink", "sha256": "cec78287db0489fca9d08e5583bd7d24d2004a544e2767a15ea4271e5a6df3d4", "sha256_in_prefix": "cec78287db0489fca9d08e5583bd7d24d2004a544e2767a15ea4271e5a6df3d4", "size_in_bytes": 4086}, {"_path": "Lib/site-packages/setuptools/_distutils/sysconfig.py", "path_type": "hardlink", "sha256": "29e23c3876ccb84cc727c4347017b3f4a667cbc891cba67a634024333d6396c5", "sha256_in_prefix": "29e23c3876ccb84cc727c4347017b3f4a667cbc891cba67a634024333d6396c5", "size_in_bytes": 19728}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__init__.py", "path_type": "hardlink", "sha256": "8fe2283d912d42fdc438fbaa353c1a96be862f2463cc20be38e68dbd9ce61ec2", "sha256_in_prefix": "8fe2283d912d42fdc438fbaa353c1a96be862f2463cc20be38e68dbd9ce61ec2", "size_in_bytes": 1485}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "511c227ca8fa2f4a7a86123c1a9a1c16f8f0c356be34480a6ff0957083861886", "sha256_in_prefix": "511c227ca8fa2f4a7a86123c1a9a1c16f8f0c356be34480a6ff0957083861886", "size_in_bytes": 2050}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/support.cpython-311.pyc", "path_type": "hardlink", "sha256": "c07389de8586dbcff459045343ecfd1ab7dc064f0dd5ad5aafbb10e2d3ba49be", "sha256_in_prefix": "c07389de8586dbcff459045343ecfd1ab7dc064f0dd5ad5aafbb10e2d3ba49be", "size_in_bytes": 7100}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_archive_util.cpython-311.pyc", "path_type": "hardlink", "sha256": "45d8b6c723aa6869c4ff0e7cb6ae19801d83f99a5fb945381a57eb81a8628f47", "sha256_in_prefix": "45d8b6c723aa6869c4ff0e7cb6ae19801d83f99a5fb945381a57eb81a8628f47", "size_in_bytes": 23356}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_bdist.cpython-311.pyc", "path_type": "hardlink", "sha256": "199256d6a00cb36bf0b94fce48006fba87b1c632546bd829eeeef91375ee15cc", "sha256_in_prefix": "199256d6a00cb36bf0b94fce48006fba87b1c632546bd829eeeef91375ee15cc", "size_in_bytes": 2096}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_bdist_dumb.cpython-311.pyc", "path_type": "hardlink", "sha256": "287845b474dd3451a318857f347f4dcf98f66d474dfa7e9331de74bc2f929159", "sha256_in_prefix": "287845b474dd3451a318857f347f4dcf98f66d474dfa7e9331de74bc2f929159", "size_in_bytes": 4139}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_bdist_rpm.cpython-311.pyc", "path_type": "hardlink", "sha256": "ef626e917392924640aaf25e01a81ede8310a1df9f557fbe3c96b23774f1e733", "sha256_in_prefix": "ef626e917392924640aaf25e01a81ede8310a1df9f557fbe3c96b23774f1e733", "size_in_bytes": 6139}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_build.cpython-311.pyc", "path_type": "hardlink", "sha256": "976c03bd18a1dfc6af0099b6f38f21e37137f6f684149c721b38d7b37c41284b", "sha256_in_prefix": "976c03bd18a1dfc6af0099b6f38f21e37137f6f684149c721b38d7b37c41284b", "size_in_bytes": 2774}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_build_clib.cpython-311.pyc", "path_type": "hardlink", "sha256": "0041d082506dcd452a40bf8ea07c0c1378bd2d4c38a740fe1d9d36dcc798a587", "sha256_in_prefix": "0041d082506dcd452a40bf8ea07c0c1378bd2d4c38a740fe1d9d36dcc798a587", "size_in_bytes": 8049}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_build_ext.cpython-311.pyc", "path_type": "hardlink", "sha256": "a83b558aa3a5c57cfbabbde54b0906e434a3f29cbe1c06144a7fa548e60954d9", "sha256_in_prefix": "a83b558aa3a5c57cfbabbde54b0906e434a3f29cbe1c06144a7fa548e60954d9", "size_in_bytes": 33524}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_build_py.cpython-311.pyc", "path_type": "hardlink", "sha256": "af1ca8d54560bd74313f3a96ab8057c269da1ccd71ff2458e659a0c7742e9803", "sha256_in_prefix": "af1ca8d54560bd74313f3a96ab8057c269da1ccd71ff2458e659a0c7742e9803", "size_in_bytes": 10652}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_build_scripts.cpython-311.pyc", "path_type": "hardlink", "sha256": "b0d678e81b11987c36f3a6b9c10cccc517871fcf3ac5a97c52b6d0fbde8cbb4e", "sha256_in_prefix": "b0d678e81b11987c36f3a6b9c10cccc517871fcf3ac5a97c52b6d0fbde8cbb4e", "size_in_bytes": 5322}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_check.cpython-311.pyc", "path_type": "hardlink", "sha256": "118c705481e37372bb92e527c3bbede8412bc2325ea651577c9acacddaa4b1e4", "sha256_in_prefix": "118c705481e37372bb92e527c3bbede8412bc2325ea651577c9acacddaa4b1e4", "size_in_bytes": 7979}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_clean.cpython-311.pyc", "path_type": "hardlink", "sha256": "e34f724acfd0148d8f146ee5f37c9070bcb169a407373e6c4a2abef4b4de8c98", "sha256_in_prefix": "e34f724acfd0148d8f146ee5f37c9070bcb169a407373e6c4a2abef4b4de8c98", "size_in_bytes": 2304}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_cmd.cpython-311.pyc", "path_type": "hardlink", "sha256": "bdcc612fae1bfee15b4f268554fe3b5af3c251e6de152eed82e26474d1ec8cc9", "sha256_in_prefix": "bdcc612fae1bfee15b4f268554fe3b5af3c251e6de152eed82e26474d1ec8cc9", "size_in_bytes": 7666}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_config_cmd.cpython-311.pyc", "path_type": "hardlink", "sha256": "c1ca0d0181e8e4481e7bfe5af7125e21d99e09ff6fb3a44a130b6c316b35b19f", "sha256_in_prefix": "c1ca0d0181e8e4481e7bfe5af7125e21d99e09ff6fb3a44a130b6c316b35b19f", "size_in_bytes": 5655}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_core.cpython-311.pyc", "path_type": "hardlink", "sha256": "8bbc9332fd229d32b819877bfb0e17ab2295405c52bec8e2c07c277a11a6dc20", "sha256_in_prefix": "8bbc9332fd229d32b819877bfb0e17ab2295405c52bec8e2c07c277a11a6dc20", "size_in_bytes": 6606}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_dir_util.cpython-311.pyc", "path_type": "hardlink", "sha256": "bf334b49601d9f54dc235a8043cf3478bdeb0abe85bdf879f7169ef70ab59010", "sha256_in_prefix": "bf334b49601d9f54dc235a8043cf3478bdeb0abe85bdf879f7169ef70ab59010", "size_in_bytes": 9213}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_dist.cpython-311.pyc", "path_type": "hardlink", "sha256": "f8bff8d03a3018e437ed9c0d5b3f5f4aa4e80633c73eeda79df534a64c0af5fb", "sha256_in_prefix": "f8bff8d03a3018e437ed9c0d5b3f5f4aa4e80633c73eeda79df534a64c0af5fb", "size_in_bytes": 30239}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_extension.cpython-311.pyc", "path_type": "hardlink", "sha256": "0f52d0751541b347a304d37616dfb4777ec9be0a766926e721ea7e70c62feeef", "sha256_in_prefix": "0f52d0751541b347a304d37616dfb4777ec9be0a766926e721ea7e70c62feeef", "size_in_bytes": 4925}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_file_util.cpython-311.pyc", "path_type": "hardlink", "sha256": "12fe001a956f37088fed598eb9ce7201f606d375e931fb6cd114992be67e5fdf", "sha256_in_prefix": "12fe001a956f37088fed598eb9ce7201f606d375e931fb6cd114992be67e5fdf", "size_in_bytes": 7544}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_filelist.cpython-311.pyc", "path_type": "hardlink", "sha256": "97641722577997b143aab6904f75f0ef98784b89c691899f49e0accdf293989d", "sha256_in_prefix": "97641722577997b143aab6904f75f0ef98784b89c691899f49e0accdf293989d", "size_in_bytes": 16543}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_install.cpython-311.pyc", "path_type": "hardlink", "sha256": "139dd32c7558c685ad402b9f349bf8bd7c1176e46ef0a868b252f7f38e0c3bb6", "sha256_in_prefix": "139dd32c7558c685ad402b9f349bf8bd7c1176e46ef0a868b252f7f38e0c3bb6", "size_in_bytes": 15461}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_install_data.cpython-311.pyc", "path_type": "hardlink", "sha256": "66143b28a792903f4a7687027d3a5b87dc36960274b92a975400183b89fabfa1", "sha256_in_prefix": "66143b28a792903f4a7687027d3a5b87dc36960274b92a975400183b89fabfa1", "size_in_bytes": 5191}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_install_headers.cpython-311.pyc", "path_type": "hardlink", "sha256": "469f3a6724e1112423900bbd49894a5658c1019e354f0a0a8be6032dee503293", "sha256_in_prefix": "469f3a6724e1112423900bbd49894a5658c1019e354f0a0a8be6032dee503293", "size_in_bytes": 2093}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_install_lib.cpython-311.pyc", "path_type": "hardlink", "sha256": "39e0b210d200f10cda8669b1091b0daf7be025a679e062d279ba0e8605530b4b", "sha256_in_prefix": "39e0b210d200f10cda8669b1091b0daf7be025a679e062d279ba0e8605530b4b", "size_in_bytes": 6770}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_install_scripts.cpython-311.pyc", "path_type": "hardlink", "sha256": "f102f94b3ebc58c55a9decbb87ffe1f5b0e72333776b8269925f1b21e9681093", "sha256_in_prefix": "f102f94b3ebc58c55a9decbb87ffe1f5b0e72333776b8269925f1b21e9681093", "size_in_bytes": 2813}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_log.cpython-311.pyc", "path_type": "hardlink", "sha256": "9acc6a3b23bf781315a5c57750de7a34c6a3b5760e28e44f61c8f8645c512ee9", "sha256_in_prefix": "9acc6a3b23bf781315a5c57750de7a34c6a3b5760e28e44f61c8f8645c512ee9", "size_in_bytes": 1035}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_modified.cpython-311.pyc", "path_type": "hardlink", "sha256": "12aa8426eaa6c4850bdd25725bc088b64fcff01b7826c4a61f775339e1142c0d", "sha256_in_prefix": "12aa8426eaa6c4850bdd25725bc088b64fcff01b7826c4a61f775339e1142c0d", "size_in_bytes": 8389}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_sdist.cpython-311.pyc", "path_type": "hardlink", "sha256": "543315a78ce9491a1d0cfdf2d9e8b718303007fb7aed73919312eb0e3f26eecc", "sha256_in_prefix": "543315a78ce9491a1d0cfdf2d9e8b718303007fb7aed73919312eb0e3f26eecc", "size_in_bytes": 23472}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_spawn.cpython-311.pyc", "path_type": "hardlink", "sha256": "35563ed3c41c89476b63c3c3c008afc4e9f464141a8b66cb5b7a457a54738a8a", "sha256_in_prefix": "35563ed3c41c89476b63c3c3c008afc4e9f464141a8b66cb5b7a457a54738a8a", "size_in_bytes": 8626}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_sysconfig.cpython-311.pyc", "path_type": "hardlink", "sha256": "f2c5dce1a1f9d0754c363dc78c7dc2a1113d24b1699aaa1f42af2bdd832aeb82", "sha256_in_prefix": "f2c5dce1a1f9d0754c363dc78c7dc2a1113d24b1699aaa1f42af2bdd832aeb82", "size_in_bytes": 19423}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_text_file.cpython-311.pyc", "path_type": "hardlink", "sha256": "77a4ebed3d38256cf1246ce8b41e8a9b4b1ced4f8754b7d856bad6b45a47b638", "sha256_in_prefix": "77a4ebed3d38256cf1246ce8b41e8a9b4b1ced4f8754b7d856bad6b45a47b638", "size_in_bytes": 3844}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_util.cpython-311.pyc", "path_type": "hardlink", "sha256": "c67dae2a6ae6785c37428e1b1bf52d0bf60597f45af9a4c99111a51da9dce633", "sha256_in_prefix": "c67dae2a6ae6785c37428e1b1bf52d0bf60597f45af9a4c99111a51da9dce633", "size_in_bytes": 15797}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_version.cpython-311.pyc", "path_type": "hardlink", "sha256": "d56360d8be4f970f21a82484abc66b56071b0ea29b8b5121bda3e96554d400bc", "sha256_in_prefix": "d56360d8be4f970f21a82484abc66b56071b0ea29b8b5121bda3e96554d400bc", "size_in_bytes": 4451}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_versionpredicate.cpython-311.pyc", "path_type": "hardlink", "sha256": "7a04b7425abeb3bdfe9d708978464bb7d504a251263f05bdb2733a32ed7bc494", "sha256_in_prefix": "7a04b7425abeb3bdfe9d708978464bb7d504a251263f05bdb2733a32ed7bc494", "size_in_bytes": 179}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/unix_compat.cpython-311.pyc", "path_type": "hardlink", "sha256": "289099add722490e98907c51e26a9f705863a5ef4ec51ef04d54fd336b87f88d", "sha256_in_prefix": "289099add722490e98907c51e26a9f705863a5ef4ec51ef04d54fd336b87f88d", "size_in_bytes": 770}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/compat/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/compat/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "9b46897c8be9ebd32edf4f6e5bc82dc0a5637418c727ac420f49d35e4f3e26ef", "sha256_in_prefix": "9b46897c8be9ebd32edf4f6e5bc82dc0a5637418c727ac420f49d35e4f3e26ef", "size_in_bytes": 173}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/compat/__pycache__/py39.cpython-311.pyc", "path_type": "hardlink", "sha256": "7a9db91eac19bcf21b5d39ecb7ebf6e9fdf9cd02a5cd1d75cc8d7f29f6842b98", "sha256_in_prefix": "7a9db91eac19bcf21b5d39ecb7ebf6e9fdf9cd02a5cd1d75cc8d7f29f6842b98", "size_in_bytes": 967}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/compat/py39.py", "path_type": "hardlink", "sha256": "b741814ccfb7d235fef7309f93094d045b73cda6de9b1eb4eb9989d1df7f551c", "sha256_in_prefix": "b741814ccfb7d235fef7309f93094d045b73cda6de9b1eb4eb9989d1df7f551c", "size_in_bytes": 1026}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/support.py", "path_type": "hardlink", "sha256": "b63b18b32c6fa532b836b902b1e876ba3bc320657431ffdbe522397cfd93d323", "sha256_in_prefix": "b63b18b32c6fa532b836b902b1e876ba3bc320657431ffdbe522397cfd93d323", "size_in_bytes": 4099}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_archive_util.py", "path_type": "hardlink", "sha256": "8e8ce2992c0f045f89a097cdfef0da895199a7ae8135c5991a1df81655b9ec34", "sha256_in_prefix": "8e8ce2992c0f045f89a097cdfef0da895199a7ae8135c5991a1df81655b9ec34", "size_in_bytes": 11787}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_bdist.py", "path_type": "hardlink", "sha256": "c4d1f152c2e51ec6504709332dbfe2483db8b3ef4c93e357d9f7c15b03b23f27", "sha256_in_prefix": "c4d1f152c2e51ec6504709332dbfe2483db8b3ef4c93e357d9f7c15b03b23f27", "size_in_bytes": 1396}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_bdist_dumb.py", "path_type": "hardlink", "sha256": "405d393073613ce759ca1f3c5e9c3c2ac3bae2cee9445925f0a2fe4685785cad", "sha256_in_prefix": "405d393073613ce759ca1f3c5e9c3c2ac3bae2cee9445925f0a2fe4685785cad", "size_in_bytes": 2247}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_bdist_rpm.py", "path_type": "hardlink", "sha256": "1dd9bea705a0c9aa067466c470665af1c461194e39cbc8072bcba639a9d38e29", "sha256_in_prefix": "1dd9bea705a0c9aa067466c470665af1c461194e39cbc8072bcba639a9d38e29", "size_in_bytes": 3932}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_build.py", "path_type": "hardlink", "sha256": "2496395e9399728db9658d29b2dc65fa223c987b163f4ba37f9a3c68eb6e6586", "sha256_in_prefix": "2496395e9399728db9658d29b2dc65fa223c987b163f4ba37f9a3c68eb6e6586", "size_in_bytes": 1742}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_build_clib.py", "path_type": "hardlink", "sha256": "328d5915be02d555c160e1af9da965c0ded80a74edaf6e1a90b0cef198b80ac6", "sha256_in_prefix": "328d5915be02d555c160e1af9da965c0ded80a74edaf6e1a90b0cef198b80ac6", "size_in_bytes": 4331}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_build_ext.py", "path_type": "hardlink", "sha256": "4053bda98561596749bb5ec75dce31f513272d99619349401e2f47569a5bb97e", "sha256_in_prefix": "4053bda98561596749bb5ec75dce31f513272d99619349401e2f47569a5bb97e", "size_in_bytes": 22545}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_build_py.py", "path_type": "hardlink", "sha256": "36c7e646ba2338705734ca9647f9a9e60e0f2d3823843ee264551f7c664521dc", "sha256_in_prefix": "36c7e646ba2338705734ca9647f9a9e60e0f2d3823843ee264551f7c664521dc", "size_in_bytes": 6882}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_build_scripts.py", "path_type": "hardlink", "sha256": "703f85472fa85f9e6c5d15f9133e7140269e1eb59a8f229ce17bb0bf67dee3cc", "sha256_in_prefix": "703f85472fa85f9e6c5d15f9133e7140269e1eb59a8f229ce17bb0bf67dee3cc", "size_in_bytes": 2880}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_check.py", "path_type": "hardlink", "sha256": "847495d3ba9fed8a12c46b136dbb1443db6cb19cf945135d6eb635b364b06852", "sha256_in_prefix": "847495d3ba9fed8a12c46b136dbb1443db6cb19cf945135d6eb635b364b06852", "size_in_bytes": 6226}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_clean.py", "path_type": "hardlink", "sha256": "84f1fa8df22918552bbd66c5d6dc6488d55235a031b76c2ae578d5e3df733b81", "sha256_in_prefix": "84f1fa8df22918552bbd66c5d6dc6488d55235a031b76c2ae578d5e3df733b81", "size_in_bytes": 1240}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_cmd.py", "path_type": "hardlink", "sha256": "6e0441efd9a2b6838a4753a2c991e70a882f1b1b77a56931793a880b4e254164", "sha256_in_prefix": "6e0441efd9a2b6838a4753a2c991e70a882f1b1b77a56931793a880b4e254164", "size_in_bytes": 3254}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_config_cmd.py", "path_type": "hardlink", "sha256": "66ce965f421fc43be6b82d7d5f3b953676029d3afd63e865ef74c09834813786", "sha256_in_prefix": "66ce965f421fc43be6b82d7d5f3b953676029d3afd63e865ef74c09834813786", "size_in_bytes": 2664}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_core.py", "path_type": "hardlink", "sha256": "2fb5ca540c5af8c1a8019780368a67b8af5f44a9de621912429830f1742f705f", "sha256_in_prefix": "2fb5ca540c5af8c1a8019780368a67b8af5f44a9de621912429830f1742f705f", "size_in_bytes": 3829}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_dir_util.py", "path_type": "hardlink", "sha256": "13ce250be938ae2554c1447259a43426ac76ba2dbe8a8fb446e25adcceea909b", "sha256_in_prefix": "13ce250be938ae2554c1447259a43426ac76ba2dbe8a8fb446e25adcceea909b", "size_in_bytes": 4500}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_dist.py", "path_type": "hardlink", "sha256": "6bac257397d025de6a43a1ce9ddcdcba93618d3c6f8fafbf24bb69b98bda3f53", "sha256_in_prefix": "6bac257397d025de6a43a1ce9ddcdcba93618d3c6f8fafbf24bb69b98bda3f53", "size_in_bytes": 18793}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_extension.py", "path_type": "hardlink", "sha256": "f987a32e0642bb2705ace05deb8a551f426fc0c73d3708731ef431bef8d71ea9", "sha256_in_prefix": "f987a32e0642bb2705ace05deb8a551f426fc0c73d3708731ef431bef8d71ea9", "size_in_bytes": 3670}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_file_util.py", "path_type": "hardlink", "sha256": "962be39e5dc592295096b076ac574542af67be3115647ca73726b46dfceffdbe", "sha256_in_prefix": "962be39e5dc592295096b076ac574542af67be3115647ca73726b46dfceffdbe", "size_in_bytes": 3522}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_filelist.py", "path_type": "hardlink", "sha256": "ac9c24a8251f9060e05a50f6d93a33b13f3271bba930707c0d7a93873c13d53e", "sha256_in_prefix": "ac9c24a8251f9060e05a50f6d93a33b13f3271bba930707c0d7a93873c13d53e", "size_in_bytes": 10766}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_install.py", "path_type": "hardlink", "sha256": "4df081d32921231c9d202d90e12b93019cd21efb5e30782b04bf708684a02bd4", "sha256_in_prefix": "4df081d32921231c9d202d90e12b93019cd21efb5e30782b04bf708684a02bd4", "size_in_bytes": 8618}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_install_data.py", "path_type": "hardlink", "sha256": "bcaab72bdee4d210409ce837f279b011d7fb7040d5afdad357209e2689606f80", "sha256_in_prefix": "bcaab72bdee4d210409ce837f279b011d7fb7040d5afdad357209e2689606f80", "size_in_bytes": 2464}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_install_headers.py", "path_type": "hardlink", "sha256": "3d5018a68fed625f7cd107fae033ce9a64afc9e7c81dd534e9fed5b09799ca41", "sha256_in_prefix": "3d5018a68fed625f7cd107fae033ce9a64afc9e7c81dd534e9fed5b09799ca41", "size_in_bytes": 936}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_install_lib.py", "path_type": "hardlink", "sha256": "aab8ba465fa668d4d0acd0d5f036de5cd974863b1f4482a2238adf64bae65812", "sha256_in_prefix": "aab8ba465fa668d4d0acd0d5f036de5cd974863b1f4482a2238adf64bae65812", "size_in_bytes": 3612}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_install_scripts.py", "path_type": "hardlink", "sha256": "284defd1c0e4156fbdd083880fe3a665918cda6872f99904bae5bb5174b6487c", "sha256_in_prefix": "284defd1c0e4156fbdd083880fe3a665918cda6872f99904bae5bb5174b6487c", "size_in_bytes": 1600}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_log.py", "path_type": "hardlink", "sha256": "8ac16d3ae7e5a02c84759690395edc554af8e86c2d755323e37986041e571fb9", "sha256_in_prefix": "8ac16d3ae7e5a02c84759690395edc554af8e86c2d755323e37986041e571fb9", "size_in_bytes": 323}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_modified.py", "path_type": "hardlink", "sha256": "875fbe6ce5a6b49a356e9555eae4617674bd6ebef508188d0ccd4c0f0486a6e8", "sha256_in_prefix": "875fbe6ce5a6b49a356e9555eae4617674bd6ebef508188d0ccd4c0f0486a6e8", "size_in_bytes": 4221}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_sdist.py", "path_type": "hardlink", "sha256": "71fcd4865080e35f3ed6f1fdb5adc806cdba73f8d405b909a0538ae469c0c8d9", "sha256_in_prefix": "71fcd4865080e35f3ed6f1fdb5adc806cdba73f8d405b909a0538ae469c0c8d9", "size_in_bytes": 15062}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_spawn.py", "path_type": "hardlink", "sha256": "792f30f43edb4f1c852d2c916a12567ae87c29cd45f11596898fdd486e41e417", "sha256_in_prefix": "792f30f43edb4f1c852d2c916a12567ae87c29cd45f11596898fdd486e41e417", "size_in_bytes": 4803}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_sysconfig.py", "path_type": "hardlink", "sha256": "97133c2ec522d53a268c35781e860af2ee6752806478d2fad14abc3d8d437305", "sha256_in_prefix": "97133c2ec522d53a268c35781e860af2ee6752806478d2fad14abc3d8d437305", "size_in_bytes": 11986}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_text_file.py", "path_type": "hardlink", "sha256": "59059207901f7410d968c03c045822a493e7b096ffd9228c7cbf747d291156dc", "sha256_in_prefix": "59059207901f7410d968c03c045822a493e7b096ffd9228c7cbf747d291156dc", "size_in_bytes": 3460}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_util.py", "path_type": "hardlink", "sha256": "1fdce5678cf8561e137e33580c1b313fbc20b902e9c427c963239c9b5c995377", "sha256_in_prefix": "1fdce5678cf8561e137e33580c1b313fbc20b902e9c427c963239c9b5c995377", "size_in_bytes": 7988}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_version.py", "path_type": "hardlink", "sha256": "6a17e0fe63fcc11cb5b20c18fbf3f1e61ae381febfba94c8a670a0a51e325919", "sha256_in_prefix": "6a17e0fe63fcc11cb5b20c18fbf3f1e61ae381febfba94c8a670a0a51e325919", "size_in_bytes": 2750}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_versionpredicate.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/unix_compat.py", "path_type": "hardlink", "sha256": "cfea29e82da255d5f56aae4120147b72a3b18a3284f7b6a537026a1cd74ba682", "sha256_in_prefix": "cfea29e82da255d5f56aae4120147b72a3b18a3284f7b6a537026a1cd74ba682", "size_in_bytes": 386}, {"_path": "Lib/site-packages/setuptools/_distutils/text_file.py", "path_type": "hardlink", "sha256": "cf876438906bf41a362c6d1336a9bcb03eb72c411a29248fd09d1b581ac51b77", "sha256_in_prefix": "cf876438906bf41a362c6d1336a9bcb03eb72c411a29248fd09d1b581ac51b77", "size_in_bytes": 12101}, {"_path": "Lib/site-packages/setuptools/_distutils/unixccompiler.py", "path_type": "hardlink", "sha256": "d5b5c9587e1f8aefc0d967eb887cdff3cc639654135e79e352465d44ab3d7165", "sha256_in_prefix": "d5b5c9587e1f8aefc0d967eb887cdff3cc639654135e79e352465d44ab3d7165", "size_in_bytes": 212}, {"_path": "Lib/site-packages/setuptools/_distutils/util.py", "path_type": "hardlink", "sha256": "3637e7aa4eb4ccc7648808d19c6713597dede3dfa86c76a93a56cdbf2225d362", "sha256_in_prefix": "3637e7aa4eb4ccc7648808d19c6713597dede3dfa86c76a93a56cdbf2225d362", "size_in_bytes": 18094}, {"_path": "Lib/site-packages/setuptools/_distutils/version.py", "path_type": "hardlink", "sha256": "bc8993e7e1025e4436d6828bd17605893a8ae8dc8cd3d729cc136803fdf80905", "sha256_in_prefix": "bc8993e7e1025e4436d6828bd17605893a8ae8dc8cd3d729cc136803fdf80905", "size_in_bytes": 12619}, {"_path": "Lib/site-packages/setuptools/_distutils/versionpredicate.py", "path_type": "hardlink", "sha256": "a81590eb04e3d76383cada13988c9d79f218da36f8b98d6c75b81bb8b9fe2093", "sha256_in_prefix": "a81590eb04e3d76383cada13988c9d79f218da36f8b98d6c75b81bb8b9fe2093", "size_in_bytes": 5205}, {"_path": "Lib/site-packages/setuptools/_distutils/zosccompiler.py", "path_type": "hardlink", "sha256": "b2f7625d9da475cc0aac929f8548626f4df2f20cfb68664aba45c7dc8ed89017", "sha256_in_prefix": "b2f7625d9da475cc0aac929f8548626f4df2f20cfb68664aba45c7dc8ed89017", "size_in_bytes": 58}, {"_path": "Lib/site-packages/setuptools/_entry_points.py", "path_type": "hardlink", "sha256": "63741413d24a156fd8caab839e97df3564ace9fde3284b757be767c7efbdf8ac", "sha256_in_prefix": "63741413d24a156fd8caab839e97df3564ace9fde3284b757be767c7efbdf8ac", "size_in_bytes": 2310}, {"_path": "Lib/site-packages/setuptools/_imp.py", "path_type": "hardlink", "sha256": "618d448d910dfb4cd8722d5cc4ed7407f69d0043abee2f1e2bc26809cf487ab7", "sha256_in_prefix": "618d448d910dfb4cd8722d5cc4ed7407f69d0043abee2f1e2bc26809cf487ab7", "size_in_bytes": 2435}, {"_path": "Lib/site-packages/setuptools/_importlib.py", "path_type": "hardlink", "sha256": "68a22370ad07297373d83f974ebc5a8b11a7fe3b9390e3709aeddd72178c385d", "sha256_in_prefix": "68a22370ad07297373d83f974ebc5a8b11a7fe3b9390e3709aeddd72178c385d", "size_in_bytes": 223}, {"_path": "Lib/site-packages/setuptools/_itertools.py", "path_type": "hardlink", "sha256": "8d645fb08ae90bb9b2a28cf78435118fd1adbe9b3065e2978361da926121363a", "sha256_in_prefix": "8d645fb08ae90bb9b2a28cf78435118fd1adbe9b3065e2978361da926121363a", "size_in_bytes": 657}, {"_path": "Lib/site-packages/setuptools/_normalization.py", "path_type": "hardlink", "sha256": "9009867ebc23179763c9d11f2cbc8a82391709b2ffd3f67150f3be0e52e59886", "sha256_in_prefix": "9009867ebc23179763c9d11f2cbc8a82391709b2ffd3f67150f3be0e52e59886", "size_in_bytes": 5824}, {"_path": "Lib/site-packages/setuptools/_path.py", "path_type": "hardlink", "sha256": "70fbf8d6fd371c3eee118a82228f84fdc1917e93d5af8972c010a22be1d2ac28", "sha256_in_prefix": "70fbf8d6fd371c3eee118a82228f84fdc1917e93d5af8972c010a22be1d2ac28", "size_in_bytes": 2685}, {"_path": "Lib/site-packages/setuptools/_reqs.py", "path_type": "hardlink", "sha256": "408dc2f6e38148d45c72edb4f2a3e78b11f1e759f10abcbbfe73d32096926313", "sha256_in_prefix": "408dc2f6e38148d45c72edb4f2a3e78b11f1e759f10abcbbfe73d32096926313", "size_in_bytes": 1438}, {"_path": "Lib/site-packages/setuptools/_shutil.py", "path_type": "hardlink", "sha256": "7003a595ca323135ece492e8c9b422dbdc88e6000193cda17a9272381bf66ccc", "sha256_in_prefix": "7003a595ca323135ece492e8c9b422dbdc88e6000193cda17a9272381bf66ccc", "size_in_bytes": 1496}, {"_path": "Lib/site-packages/setuptools/_static.py", "path_type": "hardlink", "sha256": "19347bf60112175fc968ae2dacb9290eb12e09e12d3e5c105b4311bfb54d417e", "sha256_in_prefix": "19347bf60112175fc968ae2dacb9290eb12e09e12d3e5c105b4311bfb54d417e", "size_in_bytes": 4855}, {"_path": "Lib/site-packages/setuptools/_vendor/__pycache__/typing_extensions.cpython-311.pyc", "path_type": "hardlink", "sha256": "7828e907e2f89aa01579172e6474d588a878aac882bbb7a9422bc76bdad4ee5a", "sha256_in_prefix": "7828e907e2f89aa01579172e6474d588a878aac882bbb7a9422bc76bdad4ee5a", "size_in_bytes": 151452}, {"_path": "Lib/site-packages/setuptools/_vendor/autocommand-2.2.2.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "Lib/site-packages/setuptools/_vendor/autocommand-2.2.2.dist-info/LICENSE", "path_type": "hardlink", "sha256": "ade78d04982d69972d444a8e14a94f87a2334dd3855cc80348ea8e240aa0df2d", "sha256_in_prefix": "ade78d04982d69972d444a8e14a94f87a2334dd3855cc80348ea8e240aa0df2d", "size_in_bytes": 7634}, {"_path": "Lib/site-packages/setuptools/_vendor/autocommand-2.2.2.dist-info/METADATA", "path_type": "hardlink", "sha256": "3800d9b91dceea2065a6ed6279383362e97ac38b8e56b9343f404ee531860099", "sha256_in_prefix": "3800d9b91dceea2065a6ed6279383362e97ac38b8e56b9343f404ee531860099", "size_in_bytes": 15006}, {"_path": "Lib/site-packages/setuptools/_vendor/autocommand-2.2.2.dist-info/RECORD", "path_type": "hardlink", "sha256": "822bba66b41526fa547186b80221f85da50d652bee5493dbfe5d14085112f0c3", "sha256_in_prefix": "822bba66b41526fa547186b80221f85da50d652bee5493dbfe5d14085112f0c3", "size_in_bytes": 1308}, {"_path": "Lib/site-packages/setuptools/_vendor/autocommand-2.2.2.dist-info/WHEEL", "path_type": "hardlink", "sha256": "db07a93359e4e034b8785a58ad6d534ea3dca0635f1e184efe2e66e1c3a299ba", "sha256_in_prefix": "db07a93359e4e034b8785a58ad6d534ea3dca0635f1e184efe2e66e1c3a299ba", "size_in_bytes": 92}, {"_path": "Lib/site-packages/setuptools/_vendor/autocommand-2.2.2.dist-info/top_level.txt", "path_type": "hardlink", "sha256": "0337e180a292f04740c16513485f2681e5506d7398f64a241c1ea44aac30aaed", "sha256_in_prefix": "0337e180a292f04740c16513485f2681e5506d7398f64a241c1ea44aac30aaed", "size_in_bytes": 12}, {"_path": "Lib/site-packages/setuptools/_vendor/autocommand/__init__.py", "path_type": "hardlink", "sha256": "ce4a39467be896f6fe5178c2c7fd80acf4c6056c142b9418e0b21020a611ec0b", "sha256_in_prefix": "ce4a39467be896f6fe5178c2c7fd80acf4c6056c142b9418e0b21020a611ec0b", "size_in_bytes": 1037}, {"_path": "Lib/site-packages/setuptools/_vendor/autocommand/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "870a5ad8a8cbeaa8794a996dea4f81cd8339cf7fa832c30c8a3051158317ae8b", "sha256_in_prefix": "870a5ad8a8cbeaa8794a996dea4f81cd8339cf7fa832c30c8a3051158317ae8b", "size_in_bytes": 479}, {"_path": "Lib/site-packages/setuptools/_vendor/autocommand/__pycache__/autoasync.cpython-311.pyc", "path_type": "hardlink", "sha256": "21e257de6a22e2280738dd265662f91e544329103206ffa9d5ac548c7869907e", "sha256_in_prefix": "21e257de6a22e2280738dd265662f91e544329103206ffa9d5ac548c7869907e", "size_in_bytes": 5305}, {"_path": "Lib/site-packages/setuptools/_vendor/autocommand/__pycache__/autocommand.cpython-311.pyc", "path_type": "hardlink", "sha256": "bb623ffeaf0cf640bc0db65f8876053e5ce2c2e566d00655da55c485fc583eb0", "sha256_in_prefix": "bb623ffeaf0cf640bc0db65f8876053e5ce2c2e566d00655da55c485fc583eb0", "size_in_bytes": 1494}, {"_path": "Lib/site-packages/setuptools/_vendor/autocommand/__pycache__/automain.cpython-311.pyc", "path_type": "hardlink", "sha256": "f3125c28d418e13b2a17ef5e8b41ecf1d9b601cd3f4f0a84e632eb5178650830", "sha256_in_prefix": "f3125c28d418e13b2a17ef5e8b41ecf1d9b601cd3f4f0a84e632eb5178650830", "size_in_bytes": 2001}, {"_path": "Lib/site-packages/setuptools/_vendor/autocommand/__pycache__/autoparse.cpython-311.pyc", "path_type": "hardlink", "sha256": "e02c5e23b228a8e372cfc1f4aac515614fef44685bc1d239aa3d921b165ba87c", "sha256_in_prefix": "e02c5e23b228a8e372cfc1f4aac515614fef44685bc1d239aa3d921b165ba87c", "size_in_bytes": 12265}, {"_path": "Lib/site-packages/setuptools/_vendor/autocommand/__pycache__/errors.cpython-311.pyc", "path_type": "hardlink", "sha256": "ac04cb39db0e38747a962ec11ec723283a94d7d6606e8cb231530328c85d07e4", "sha256_in_prefix": "ac04cb39db0e38747a962ec11ec723283a94d7d6606e8cb231530328c85d07e4", "size_in_bytes": 466}, {"_path": "Lib/site-packages/setuptools/_vendor/autocommand/autoasync.py", "path_type": "hardlink", "sha256": "00c772af1352e29a9625f3ffc6ea0e70898e1d60fea93ef1d3ac2628dd55a7e5", "sha256_in_prefix": "00c772af1352e29a9625f3ffc6ea0e70898e1d60fea93ef1d3ac2628dd55a7e5", "size_in_bytes": 5680}, {"_path": "Lib/site-packages/setuptools/_vendor/autocommand/autocommand.py", "path_type": "hardlink", "sha256": "866904990ef61ed2f9e609d44558c33a7b1f62519de652d76ef4f8286e3de90c", "sha256_in_prefix": "866904990ef61ed2f9e609d44558c33a7b1f62519de652d76ef4f8286e3de90c", "size_in_bytes": 2505}, {"_path": "Lib/site-packages/setuptools/_vendor/autocommand/automain.py", "path_type": "hardlink", "sha256": "0366fc8bbe7833173f0e353d585afabea6035a5873d1c9fc9a2bbc77c12cc55f", "sha256_in_prefix": "0366fc8bbe7833173f0e353d585afabea6035a5873d1c9fc9a2bbc77c12cc55f", "size_in_bytes": 2076}, {"_path": "Lib/site-packages/setuptools/_vendor/autocommand/autoparse.py", "path_type": "hardlink", "sha256": "5955a66493dc6f350a5cfe34ada430ff41c3f2a3c9d95f551b57851669a7171c", "sha256_in_prefix": "5955a66493dc6f350a5cfe34ada430ff41c3f2a3c9d95f551b57851669a7171c", "size_in_bytes": 11642}, {"_path": "Lib/site-packages/setuptools/_vendor/autocommand/errors.py", "path_type": "hardlink", "sha256": "eda6b7ae887d1deaddea720aa501cd584b25584f28abb1a21d8554b91a8e4670", "sha256_in_prefix": "eda6b7ae887d1deaddea720aa501cd584b25584f28abb1a21d8554b91a8e4670", "size_in_bytes": 886}, {"_path": "Lib/site-packages/setuptools/_vendor/backports.tarfile-1.2.0.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "Lib/site-packages/setuptools/_vendor/backports.tarfile-1.2.0.dist-info/LICENSE", "path_type": "hardlink", "sha256": "86da0f01aeae46348a3c3d465195dc1ceccde79f79e87769a64b8da04b2a4741", "sha256_in_prefix": "86da0f01aeae46348a3c3d465195dc1ceccde79f79e87769a64b8da04b2a4741", "size_in_bytes": 1023}, {"_path": "Lib/site-packages/setuptools/_vendor/backports.tarfile-1.2.0.dist-info/METADATA", "path_type": "hardlink", "sha256": "8215c54ead77d9dc5a108a25c6bdc72b5999aa6f62c9499a440359412afa5a51", "sha256_in_prefix": "8215c54ead77d9dc5a108a25c6bdc72b5999aa6f62c9499a440359412afa5a51", "size_in_bytes": 2020}, {"_path": "Lib/site-packages/setuptools/_vendor/backports.tarfile-1.2.0.dist-info/RECORD", "path_type": "hardlink", "sha256": "258a1f1c849e1175069a55a5d6ce357afdd04e34cd5de27093e4acec7a9d2ce1", "sha256_in_prefix": "258a1f1c849e1175069a55a5d6ce357afdd04e34cd5de27093e4acec7a9d2ce1", "size_in_bytes": 1360}, {"_path": "Lib/site-packages/setuptools/_vendor/backports.tarfile-1.2.0.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_vendor/backports.tarfile-1.2.0.dist-info/WHEEL", "path_type": "hardlink", "sha256": "189eedfe4581172c1b6a02b97a8f48a14c0b5baa3239e4ca990fbd8871553714", "sha256_in_prefix": "189eedfe4581172c1b6a02b97a8f48a14c0b5baa3239e4ca990fbd8871553714", "size_in_bytes": 92}, {"_path": "Lib/site-packages/setuptools/_vendor/backports.tarfile-1.2.0.dist-info/top_level.txt", "path_type": "hardlink", "sha256": "7068da2cc3a8051d452b4029a23b73595995893b49ec91882bf1f05e212cbed5", "sha256_in_prefix": "7068da2cc3a8051d452b4029a23b73595995893b49ec91882bf1f05e212cbed5", "size_in_bytes": 10}, {"_path": "Lib/site-packages/setuptools/_vendor/backports/__init__.py", "path_type": "hardlink", "sha256": "88e10cc2794e4567b374ef3edafc4120f491dfb0fb2468e5b99f1fe79bf3c65b", "sha256_in_prefix": "88e10cc2794e4567b374ef3edafc4120f491dfb0fb2468e5b99f1fe79bf3c65b", "size_in_bytes": 81}, {"_path": "Lib/site-packages/setuptools/_vendor/backports/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "3f1352bfa7d7b11daa784f5d668b1c015ea52c0c9315b4ba946d92137e2c05e1", "sha256_in_prefix": "3f1352bfa7d7b11daa784f5d668b1c015ea52c0c9315b4ba946d92137e2c05e1", "size_in_bytes": 305}, {"_path": "Lib/site-packages/setuptools/_vendor/backports/tarfile/__init__.py", "path_type": "hardlink", "sha256": "3f07f6a9421f0744a89493c229cc77bf3dd412efda89db38838b007f1cbde2a8", "sha256_in_prefix": "3f07f6a9421f0744a89493c229cc77bf3dd412efda89db38838b007f1cbde2a8", "size_in_bytes": 108491}, {"_path": "Lib/site-packages/setuptools/_vendor/backports/tarfile/__main__.py", "path_type": "hardlink", "sha256": "630da8193d5a7ebcf6781b24cdd3d82fc45e07fde5880a6684590dd846c399ce", "sha256_in_prefix": "630da8193d5a7ebcf6781b24cdd3d82fc45e07fde5880a6684590dd846c399ce", "size_in_bytes": 59}, {"_path": "Lib/site-packages/setuptools/_vendor/backports/tarfile/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "48952b7b1e7c3c6921f0a23beff4bb0b05b57392ce9bba67192754adc8110d87", "sha256_in_prefix": "48952b7b1e7c3c6921f0a23beff4bb0b05b57392ce9bba67192754adc8110d87", "size_in_bytes": 132677}, {"_path": "Lib/site-packages/setuptools/_vendor/backports/tarfile/__pycache__/__main__.cpython-311.pyc", "path_type": "hardlink", "sha256": "3c36d0a30f9610121806a40827179e4ac5c3b0fec2daa71e1a0b7c530a9121c4", "sha256_in_prefix": "3c36d0a30f9610121806a40827179e4ac5c3b0fec2daa71e1a0b7c530a9121c4", "size_in_bytes": 304}, {"_path": "Lib/site-packages/setuptools/_vendor/backports/tarfile/compat/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_vendor/backports/tarfile/compat/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "e5de5c18f1e82038e2185ae1fee5b079140c0a2ae33ab7bbe756205bafc0495b", "sha256_in_prefix": "e5de5c18f1e82038e2185ae1fee5b079140c0a2ae33ab7bbe756205bafc0495b", "size_in_bytes": 182}, {"_path": "Lib/site-packages/setuptools/_vendor/backports/tarfile/compat/__pycache__/py38.cpython-311.pyc", "path_type": "hardlink", "sha256": "3b68f11c0e230b4c286ed13093a937c0bcaba7e18920e9046f3cf5c874a01f9b", "sha256_in_prefix": "3b68f11c0e230b4c286ed13093a937c0bcaba7e18920e9046f3cf5c874a01f9b", "size_in_bytes": 1206}, {"_path": "Lib/site-packages/setuptools/_vendor/backports/tarfile/compat/py38.py", "path_type": "hardlink", "sha256": "898932b7f82f5a32f31944c90fd4ee4df30c8ce93e7abb17666465bd060ddaa1", "sha256_in_prefix": "898932b7f82f5a32f31944c90fd4ee4df30c8ce93e7abb17666465bd060ddaa1", "size_in_bytes": 568}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/LICENSE", "path_type": "hardlink", "sha256": "cfc7749b96f63bd31c3c42b5c471bf756814053e847c10f3eb003417bc523d30", "sha256_in_prefix": "cfc7749b96f63bd31c3c42b5c471bf756814053e847c10f3eb003417bc523d30", "size_in_bytes": 11358}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/METADATA", "path_type": "hardlink", "sha256": "6a7b90effee1e09d5b484cdf7232016a43e2d9cc9543bcbb8e494b1ec05e1f59", "sha256_in_prefix": "6a7b90effee1e09d5b484cdf7232016a43e2d9cc9543bcbb8e494b1ec05e1f59", "size_in_bytes": 4648}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/RECORD", "path_type": "hardlink", "sha256": "0d8d3c6eeb9ebbe86cac7d60861552433c329da9ea51248b61d02be2e5e64030", "sha256_in_prefix": "0d8d3c6eeb9ebbe86cac7d60861552433c329da9ea51248b61d02be2e5e64030", "size_in_bytes": 2518}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/WHEEL", "path_type": "hardlink", "sha256": "9a0b8c95618c5fe5479cca4a3a38d089d228d6cb1194216ee1ae26069cf5b363", "sha256_in_prefix": "9a0b8c95618c5fe5479cca4a3a38d089d228d6cb1194216ee1ae26069cf5b363", "size_in_bytes": 91}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/top_level.txt", "path_type": "hardlink", "sha256": "08eddf0fdcb29403625e4acca38a872d5fe6a972f6b02e4914a82dd725804fe0", "sha256_in_prefix": "08eddf0fdcb29403625e4acca38a872d5fe6a972f6b02e4914a82dd725804fe0", "size_in_bytes": 19}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata/__init__.py", "path_type": "hardlink", "sha256": "b59341fb6de1f018b18bdb82ad0aa3f587f469e0bef89a2c772dc8651210781d", "sha256_in_prefix": "b59341fb6de1f018b18bdb82ad0aa3f587f469e0bef89a2c772dc8651210781d", "size_in_bytes": 33798}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "c2909835c81baeeb0079fef1c123755c5388bea8a215291c874f88815cd0ca96", "sha256_in_prefix": "c2909835c81baeeb0079fef1c123755c5388bea8a215291c874f88815cd0ca96", "size_in_bytes": 58813}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/_adapters.cpython-311.pyc", "path_type": "hardlink", "sha256": "b3012ef54359e72a700ee98a6d88d1a334975a684a20c405520addd34f3f3888", "sha256_in_prefix": "b3012ef54359e72a700ee98a6d88d1a334975a684a20c405520addd34f3f3888", "size_in_bytes": 4447}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/_collections.cpython-311.pyc", "path_type": "hardlink", "sha256": "7fbfccf4bf0cea7c5e5ec6eb4094dc907f3a72e009041f2de977283b73768e5c", "sha256_in_prefix": "7fbfccf4bf0cea7c5e5ec6eb4094dc907f3a72e009041f2de977283b73768e5c", "size_in_bytes": 2156}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/_compat.cpython-311.pyc", "path_type": "hardlink", "sha256": "5ad98c0dae625aa5324b13aeacc4eeeb5b37c07118c83164a0f42c12d73718e3", "sha256_in_prefix": "5ad98c0dae625aa5324b13aeacc4eeeb5b37c07118c83164a0f42c12d73718e3", "size_in_bytes": 2445}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/_functools.cpython-311.pyc", "path_type": "hardlink", "sha256": "3aef90e54ce875cb40b9d96615fef41c2f4c247da477d9802eb0e505f99316f0", "sha256_in_prefix": "3aef90e54ce875cb40b9d96615fef41c2f4c247da477d9802eb0e505f99316f0", "size_in_bytes": 3596}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/_itertools.cpython-311.pyc", "path_type": "hardlink", "sha256": "d1a9fd863cb9613fbfba8e04aedaf91a9853d9b447b3b1ef3e3af90a479edbf3", "sha256_in_prefix": "d1a9fd863cb9613fbfba8e04aedaf91a9853d9b447b3b1ef3e3af90a479edbf3", "size_in_bytes": 2559}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/_meta.cpython-311.pyc", "path_type": "hardlink", "sha256": "cfc14c7eb919c1d25dca70cfe7698eb2b90a9861df6fdf3c77e07239a800433f", "sha256_in_prefix": "cfc14c7eb919c1d25dca70cfe7698eb2b90a9861df6fdf3c77e07239a800433f", "size_in_bytes": 4072}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/_text.cpython-311.pyc", "path_type": "hardlink", "sha256": "d1ccdf415851d3d0aeff6f5a729da138d83166b0c9a2227116ab602e4b35014f", "sha256_in_prefix": "d1ccdf415851d3d0aeff6f5a729da138d83166b0c9a2227116ab602e4b35014f", "size_in_bytes": 4354}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/diagnose.cpython-311.pyc", "path_type": "hardlink", "sha256": "fd08d11ab19ebbe5735b71f25ee6a3370b71b6e9a970578fdb6877bb23d95d08", "sha256_in_prefix": "fd08d11ab19ebbe5735b71f25ee6a3370b71b6e9a970578fdb6877bb23d95d08", "size_in_bytes": 1354}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata/_adapters.py", "path_type": "hardlink", "sha256": "ac88564f006f600d5b57b8bee457d55f7f2a1170d35c5792e5c6f9c49b4fde4b", "sha256_in_prefix": "ac88564f006f600d5b57b8bee457d55f7f2a1170d35c5792e5c6f9c49b4fde4b", "size_in_bytes": 2317}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata/_collections.py", "path_type": "hardlink", "sha256": "089d0e4c21c88d6034648552e2fa0e440b27d91e11d9c40112d3ec6442690126", "sha256_in_prefix": "089d0e4c21c88d6034648552e2fa0e440b27d91e11d9c40112d3ec6442690126", "size_in_bytes": 743}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata/_compat.py", "path_type": "hardlink", "sha256": "ef740aacdf4a368699ce16d7e723c20996be15a00bc177bc4cf94347bd898015", "sha256_in_prefix": "ef740aacdf4a368699ce16d7e723c20996be15a00bc177bc4cf94347bd898015", "size_in_bytes": 1314}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata/_functools.py", "path_type": "hardlink", "sha256": "3ec636fb8aeb297e1155e442d681a9d65075a660bd78a37cf3f7fe6c3f6e3a80", "sha256_in_prefix": "3ec636fb8aeb297e1155e442d681a9d65075a660bd78a37cf3f7fe6c3f6e3a80", "size_in_bytes": 2895}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata/_itertools.py", "path_type": "hardlink", "sha256": "72faffdaff0145bc5c225e71e6575fa9d1e3848f188bcb3cca4e741bf9e6ea34", "sha256_in_prefix": "72faffdaff0145bc5c225e71e6575fa9d1e3848f188bcb3cca4e741bf9e6ea34", "size_in_bytes": 2068}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata/_meta.py", "path_type": "hardlink", "sha256": "9f167b0bc19595c04500a5b254e9ff767ee8b7fb7005c6e6d4d9af8c87ad0472", "sha256_in_prefix": "9f167b0bc19595c04500a5b254e9ff767ee8b7fb7005c6e6d4d9af8c87ad0472", "size_in_bytes": 1801}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata/_text.py", "path_type": "hardlink", "sha256": "1c2b0592c66924b7933f734493f9e0ac079755146d4ebb7287d78e001a113f80", "sha256_in_prefix": "1c2b0592c66924b7933f734493f9e0ac079755146d4ebb7287d78e001a113f80", "size_in_bytes": 2166}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata/compat/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata/compat/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "e713c7ff0e093aa39098d7afc0cd3e3c014f749e327e0a23e06f4489e5103428", "sha256_in_prefix": "e713c7ff0e093aa39098d7afc0cd3e3c014f749e327e0a23e06f4489e5103428", "size_in_bytes": 183}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata/compat/__pycache__/py311.cpython-311.pyc", "path_type": "hardlink", "sha256": "5279138d08b65c8fedb4c2a7c331f4dd9502904b1d69fcba42931edad9c9a93b", "sha256_in_prefix": "5279138d08b65c8fedb4c2a7c331f4dd9502904b1d69fcba42931edad9c9a93b", "size_in_bytes": 1303}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata/compat/__pycache__/py39.cpython-311.pyc", "path_type": "hardlink", "sha256": "a8199e6b42c9d854b8ca3953529595386ba3650b72e08d0cbeba940485cf9fdf", "sha256_in_prefix": "a8199e6b42c9d854b8ca3953529595386ba3650b72e08d0cbeba940485cf9fdf", "size_in_bytes": 1761}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata/compat/py311.py", "path_type": "hardlink", "sha256": "baa9be2beba88728f5d38d931f86bd12bfc8e68efaebb0efba5703fa00bf7d20", "sha256_in_prefix": "baa9be2beba88728f5d38d931f86bd12bfc8e68efaebb0efba5703fa00bf7d20", "size_in_bytes": 608}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata/compat/py39.py", "path_type": "hardlink", "sha256": "70f90cbfafb48a52bed09c3f4e49f4c586ce28965ce1624a407a30d1cd93e38c", "sha256_in_prefix": "70f90cbfafb48a52bed09c3f4e49f4c586ce28965ce1624a407a30d1cd93e38c", "size_in_bytes": 1102}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata/diagnose.py", "path_type": "hardlink", "sha256": "9e4491322a309669212d884a86f0a0f60966b7fd750a8c7e1262f311ba984daf", "sha256_in_prefix": "9e4491322a309669212d884a86f0a0f60966b7fd750a8c7e1262f311ba984daf", "size_in_bytes": 379}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata/py.typed", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_vendor/inflect-7.3.1.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "Lib/site-packages/setuptools/_vendor/inflect-7.3.1.dist-info/LICENSE", "path_type": "hardlink", "sha256": "86da0f01aeae46348a3c3d465195dc1ceccde79f79e87769a64b8da04b2a4741", "sha256_in_prefix": "86da0f01aeae46348a3c3d465195dc1ceccde79f79e87769a64b8da04b2a4741", "size_in_bytes": 1023}, {"_path": "Lib/site-packages/setuptools/_vendor/inflect-7.3.1.dist-info/METADATA", "path_type": "hardlink", "sha256": "66030d634580651b3e53cc19895d9231f8d22aa06b327817c8332cfc20303308", "sha256_in_prefix": "66030d634580651b3e53cc19895d9231f8d22aa06b327817c8332cfc20303308", "size_in_bytes": 21079}, {"_path": "Lib/site-packages/setuptools/_vendor/inflect-7.3.1.dist-info/RECORD", "path_type": "hardlink", "sha256": "5d7834ac1ba2612c6801050fde57a7b98b0f36acf88c3c2d4f376fd8911b3091", "sha256_in_prefix": "5d7834ac1ba2612c6801050fde57a7b98b0f36acf88c3c2d4f376fd8911b3091", "size_in_bytes": 943}, {"_path": "Lib/site-packages/setuptools/_vendor/inflect-7.3.1.dist-info/WHEEL", "path_type": "hardlink", "sha256": "cb8997f92397e1f6089289ec0060393743b2fbcfe0238157c391cd235c6abd68", "sha256_in_prefix": "cb8997f92397e1f6089289ec0060393743b2fbcfe0238157c391cd235c6abd68", "size_in_bytes": 91}, {"_path": "Lib/site-packages/setuptools/_vendor/inflect-7.3.1.dist-info/top_level.txt", "path_type": "hardlink", "sha256": "9b9dae8dda75d02a93ea38755d0c594fa9049ed727bfeed397b52218d4f35990", "sha256_in_prefix": "9b9dae8dda75d02a93ea38755d0c594fa9049ed727bfeed397b52218d4f35990", "size_in_bytes": 8}, {"_path": "Lib/site-packages/setuptools/_vendor/inflect/__init__.py", "path_type": "hardlink", "sha256": "271cb51c95d9899f3990778b021926bf3e84313745a817be76ebeddf847a20e7", "sha256_in_prefix": "271cb51c95d9899f3990778b021926bf3e84313745a817be76ebeddf847a20e7", "size_in_bytes": 103796}, {"_path": "Lib/site-packages/setuptools/_vendor/inflect/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "7bbf3e8d171ba5762e7f7385878e1929b1e2e7cf6430813ae9f300de6f2087d1", "sha256_in_prefix": "7bbf3e8d171ba5762e7f7385878e1929b1e2e7cf6430813ae9f300de6f2087d1", "size_in_bytes": 125053}, {"_path": "Lib/site-packages/setuptools/_vendor/inflect/compat/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_vendor/inflect/compat/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "360a75dbb29acd46a0e3ea85b3063a873f3a3959a124ff82e6dcc7da4e32c97d", "sha256_in_prefix": "360a75dbb29acd46a0e3ea85b3063a873f3a3959a124ff82e6dcc7da4e32c97d", "size_in_bytes": 172}, {"_path": "Lib/site-packages/setuptools/_vendor/inflect/compat/__pycache__/py38.cpython-311.pyc", "path_type": "hardlink", "sha256": "27667d713147b325f9db7f7e6f153c24b65f3eb2cc539747dbd4d1b73f7b784f", "sha256_in_prefix": "27667d713147b325f9db7f7e6f153c24b65f3eb2cc539747dbd4d1b73f7b784f", "size_in_bytes": 367}, {"_path": "Lib/site-packages/setuptools/_vendor/inflect/compat/py38.py", "path_type": "hardlink", "sha256": "a0e6d57d59d65fdfcea673ae1099f59856c9c55870c91e5ea5b8933570c36aca", "sha256_in_prefix": "a0e6d57d59d65fdfcea673ae1099f59856c9c55870c91e5ea5b8933570c36aca", "size_in_bytes": 160}, {"_path": "Lib/site-packages/setuptools/_vendor/inflect/py.typed", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco.collections-5.1.0.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco.collections-5.1.0.dist-info/LICENSE", "path_type": "hardlink", "sha256": "86da0f01aeae46348a3c3d465195dc1ceccde79f79e87769a64b8da04b2a4741", "sha256_in_prefix": "86da0f01aeae46348a3c3d465195dc1ceccde79f79e87769a64b8da04b2a4741", "size_in_bytes": 1023}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco.collections-5.1.0.dist-info/METADATA", "path_type": "hardlink", "sha256": "20c51a96236c0395f53b1f4c5d458e6a0721e51e16c1bff733b7aba76f5d06d8", "sha256_in_prefix": "20c51a96236c0395f53b1f4c5d458e6a0721e51e16c1bff733b7aba76f5d06d8", "size_in_bytes": 3933}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco.collections-5.1.0.dist-info/RECORD", "path_type": "hardlink", "sha256": "1e9b62bd70e4a5fa26e9594cbb80860ffeca3debfee8773daefa774cd259ca06", "sha256_in_prefix": "1e9b62bd70e4a5fa26e9594cbb80860ffeca3debfee8773daefa774cd259ca06", "size_in_bytes": 873}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco.collections-5.1.0.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco.collections-5.1.0.dist-info/WHEEL", "path_type": "hardlink", "sha256": "31d8bd3c3370119a6d3a34e551c02d87b5c90c5b4aac761a40c3ee9597810a24", "sha256_in_prefix": "31d8bd3c3370119a6d3a34e551c02d87b5c90c5b4aac761a40c3ee9597810a24", "size_in_bytes": 91}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco.collections-5.1.0.dist-info/top_level.txt", "path_type": "hardlink", "sha256": "d099cddcb7d71f82c845f5cbf9014e18227341664edc42f1e11d5dfe5a2ea103", "sha256_in_prefix": "d099cddcb7d71f82c845f5cbf9014e18227341664edc42f1e11d5dfe5a2ea103", "size_in_bytes": 7}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco.context-5.3.0.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco.context-5.3.0.dist-info/LICENSE", "path_type": "hardlink", "sha256": "86da0f01aeae46348a3c3d465195dc1ceccde79f79e87769a64b8da04b2a4741", "sha256_in_prefix": "86da0f01aeae46348a3c3d465195dc1ceccde79f79e87769a64b8da04b2a4741", "size_in_bytes": 1023}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco.context-5.3.0.dist-info/METADATA", "path_type": "hardlink", "sha256": "c43b60b897a3d2d37d8845c252fc44261d9aef171e21154111a9012d2afffed6", "sha256_in_prefix": "c43b60b897a3d2d37d8845c252fc44261d9aef171e21154111a9012d2afffed6", "size_in_bytes": 4020}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco.context-5.3.0.dist-info/RECORD", "path_type": "hardlink", "sha256": "55197b88a78443297bb2d827a75baae740b33896251d872835d4b4c75ec2f57e", "sha256_in_prefix": "55197b88a78443297bb2d827a75baae740b33896251d872835d4b4c75ec2f57e", "size_in_bytes": 641}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco.context-5.3.0.dist-info/WHEEL", "path_type": "hardlink", "sha256": "189eedfe4581172c1b6a02b97a8f48a14c0b5baa3239e4ca990fbd8871553714", "sha256_in_prefix": "189eedfe4581172c1b6a02b97a8f48a14c0b5baa3239e4ca990fbd8871553714", "size_in_bytes": 92}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco.context-5.3.0.dist-info/top_level.txt", "path_type": "hardlink", "sha256": "d099cddcb7d71f82c845f5cbf9014e18227341664edc42f1e11d5dfe5a2ea103", "sha256_in_prefix": "d099cddcb7d71f82c845f5cbf9014e18227341664edc42f1e11d5dfe5a2ea103", "size_in_bytes": 7}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco.functools-4.0.1.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco.functools-4.0.1.dist-info/LICENSE", "path_type": "hardlink", "sha256": "86da0f01aeae46348a3c3d465195dc1ceccde79f79e87769a64b8da04b2a4741", "sha256_in_prefix": "86da0f01aeae46348a3c3d465195dc1ceccde79f79e87769a64b8da04b2a4741", "size_in_bytes": 1023}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco.functools-4.0.1.dist-info/METADATA", "path_type": "hardlink", "sha256": "8b86946900d7fa38dd1102b9c1ebe17a0cb1f09c8b7e29f61f2bda4a4dc51eca", "sha256_in_prefix": "8b86946900d7fa38dd1102b9c1ebe17a0cb1f09c8b7e29f61f2bda4a4dc51eca", "size_in_bytes": 2891}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco.functools-4.0.1.dist-info/RECORD", "path_type": "hardlink", "sha256": "632aa7c04f7c4bcc01c027af5b9bc76fe8958f4a181035b957a3bd3014ba248b", "sha256_in_prefix": "632aa7c04f7c4bcc01c027af5b9bc76fe8958f4a181035b957a3bd3014ba248b", "size_in_bytes": 843}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco.functools-4.0.1.dist-info/WHEEL", "path_type": "hardlink", "sha256": "189eedfe4581172c1b6a02b97a8f48a14c0b5baa3239e4ca990fbd8871553714", "sha256_in_prefix": "189eedfe4581172c1b6a02b97a8f48a14c0b5baa3239e4ca990fbd8871553714", "size_in_bytes": 92}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco.functools-4.0.1.dist-info/top_level.txt", "path_type": "hardlink", "sha256": "d099cddcb7d71f82c845f5cbf9014e18227341664edc42f1e11d5dfe5a2ea103", "sha256_in_prefix": "d099cddcb7d71f82c845f5cbf9014e18227341664edc42f1e11d5dfe5a2ea103", "size_in_bytes": 7}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco.text-3.12.1.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco.text-3.12.1.dist-info/LICENSE", "path_type": "hardlink", "sha256": "86da0f01aeae46348a3c3d465195dc1ceccde79f79e87769a64b8da04b2a4741", "sha256_in_prefix": "86da0f01aeae46348a3c3d465195dc1ceccde79f79e87769a64b8da04b2a4741", "size_in_bytes": 1023}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco.text-3.12.1.dist-info/METADATA", "path_type": "hardlink", "sha256": "03359d9ba56231f0ce3e840c7cb5a7db380141218949ccaa78ddbd4dcb965d52", "sha256_in_prefix": "03359d9ba56231f0ce3e840c7cb5a7db380141218949ccaa78ddbd4dcb965d52", "size_in_bytes": 3658}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco.text-3.12.1.dist-info/RECORD", "path_type": "hardlink", "sha256": "816d945741dca246099388ca3eed74fc0667acbaa36f70b559b2494c3979b1f6", "sha256_in_prefix": "816d945741dca246099388ca3eed74fc0667acbaa36f70b559b2494c3979b1f6", "size_in_bytes": 1500}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco.text-3.12.1.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco.text-3.12.1.dist-info/WHEEL", "path_type": "hardlink", "sha256": "189eedfe4581172c1b6a02b97a8f48a14c0b5baa3239e4ca990fbd8871553714", "sha256_in_prefix": "189eedfe4581172c1b6a02b97a8f48a14c0b5baa3239e4ca990fbd8871553714", "size_in_bytes": 92}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco.text-3.12.1.dist-info/top_level.txt", "path_type": "hardlink", "sha256": "d099cddcb7d71f82c845f5cbf9014e18227341664edc42f1e11d5dfe5a2ea103", "sha256_in_prefix": "d099cddcb7d71f82c845f5cbf9014e18227341664edc42f1e11d5dfe5a2ea103", "size_in_bytes": 7}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco/__pycache__/context.cpython-311.pyc", "path_type": "hardlink", "sha256": "4364f2f0cb905851568dfefaa2670b97b882dc4cb446562a16bc9d33aeb0588c", "sha256_in_prefix": "4364f2f0cb905851568dfefaa2670b97b882dc4cb446562a16bc9d33aeb0588c", "size_in_bytes": 15654}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco/collections/__init__.py", "path_type": "hardlink", "sha256": "3dcd7e4aa8d69bcd5a7753f4f86b6da64c0efcb5a59da63a814abc81c2a1dafd", "sha256_in_prefix": "3dcd7e4aa8d69bcd5a7753f4f86b6da64c0efcb5a59da63a814abc81c2a1dafd", "size_in_bytes": 26640}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco/collections/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "59e5b31c6fcea287c5366b5134ede5e0b0b05456aac557015bc552ca77d92f1b", "sha256_in_prefix": "59e5b31c6fcea287c5366b5134ede5e0b0b05456aac557015bc552ca77d92f1b", "size_in_bytes": 44292}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco/collections/py.typed", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco/context.py", "path_type": "hardlink", "sha256": "444a0b2310e43b931f118a30b7f5a8ba9342468c414b9bfb617d8f6d6f2521cb", "sha256_in_prefix": "444a0b2310e43b931f118a30b7f5a8ba9342468c414b9bfb617d8f6d6f2521cb", "size_in_bytes": 9552}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco/functools/__init__.py", "path_type": "hardlink", "sha256": "844009692dae49946e17f258e02c421c8621efd669c5a3e9f4e887cabf44275c", "sha256_in_prefix": "844009692dae49946e17f258e02c421c8621efd669c5a3e9f4e887cabf44275c", "size_in_bytes": 16642}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco/functools/__init__.pyi", "path_type": "hardlink", "sha256": "824dddb201f3a3917f53be07cc0be9362bc500f0a43c9d5bdbec8277ad9d7e7c", "sha256_in_prefix": "824dddb201f3a3917f53be07cc0be9362bc500f0a43c9d5bdbec8277ad9d7e7c", "size_in_bytes": 3878}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco/functools/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "1d798340f9d71609d45edc586e89bc98265acff5e07e497af71a74b46d56deb9", "sha256_in_prefix": "1d798340f9d71609d45edc586e89bc98265acff5e07e497af71a74b46d56deb9", "size_in_bytes": 24345}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco/functools/py.typed", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco/text/Lorem ipsum.txt", "path_type": "hardlink", "sha256": "37fedcffbf73c4eb9f058f47677cb33203a436ff9390e4d38a8e01c9dad28e0b", "sha256_in_prefix": "37fedcffbf73c4eb9f058f47677cb33203a436ff9390e4d38a8e01c9dad28e0b", "size_in_bytes": 1335}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco/text/__init__.py", "path_type": "hardlink", "sha256": "636614a9747fa2b5280da6384a43d17ba65985be4750707f021f5108db15ca1a", "sha256_in_prefix": "636614a9747fa2b5280da6384a43d17ba65985be4750707f021f5108db15ca1a", "size_in_bytes": 16250}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco/text/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "f924cb9629bce140391a340454e493e94cc1f20010413de2bab45fb41ceebc20", "sha256_in_prefix": "f924cb9629bce140391a340454e493e94cc1f20010413de2bab45fb41ceebc20", "size_in_bytes": 27748}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco/text/__pycache__/layouts.cpython-311.pyc", "path_type": "hardlink", "sha256": "fa6e0097ba5fd911293c81c1e66c759b24901bc05d46584f80877d6d1de53a71", "sha256_in_prefix": "fa6e0097ba5fd911293c81c1e66c759b24901bc05d46584f80877d6d1de53a71", "size_in_bytes": 1183}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco/text/__pycache__/show-newlines.cpython-311.pyc", "path_type": "hardlink", "sha256": "0524e30ecbdb075663fc95d83a2c7c9e2b218c85f02afcabf8e14684b0eaf340", "sha256_in_prefix": "0524e30ecbdb075663fc95d83a2c7c9e2b218c85f02afcabf8e14684b0eaf340", "size_in_bytes": 1607}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco/text/__pycache__/strip-prefix.cpython-311.pyc", "path_type": "hardlink", "sha256": "ca17dc2ebc444c9032de6392c1772b8c754fdfd6f76a496ac3f8e7794240d0a7", "sha256_in_prefix": "ca17dc2ebc444c9032de6392c1772b8c754fdfd6f76a496ac3f8e7794240d0a7", "size_in_bytes": 916}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco/text/__pycache__/to-dvorak.cpython-311.pyc", "path_type": "hardlink", "sha256": "875ec4ac1fc9442803d7dc18f511143268f3d2147fa47fc6868fc0e1bddf3585", "sha256_in_prefix": "875ec4ac1fc9442803d7dc18f511143268f3d2147fa47fc6868fc0e1bddf3585", "size_in_bytes": 420}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco/text/__pycache__/to-qwerty.cpython-311.pyc", "path_type": "hardlink", "sha256": "5886fd81f2ad80b64f3fdd0d93af0b0c1db049e4e96dc4a5d1ce95ad1d5d56f0", "sha256_in_prefix": "5886fd81f2ad80b64f3fdd0d93af0b0c1db049e4e96dc4a5d1ce95ad1d5d56f0", "size_in_bytes": 420}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco/text/layouts.py", "path_type": "hardlink", "sha256": "1d30bc6924cb67bb978a9c8e5daa51302d79f23b9e7232ba455c22b5f999f7fc", "sha256_in_prefix": "1d30bc6924cb67bb978a9c8e5daa51302d79f23b9e7232ba455c22b5f999f7fc", "size_in_bytes": 643}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco/text/show-newlines.py", "path_type": "hardlink", "sha256": "58641aeb97bc97285bf762d438ba959fa29a0ada1e560570b3a5ad49198b93ac", "sha256_in_prefix": "58641aeb97bc97285bf762d438ba959fa29a0ada1e560570b3a5ad49198b93ac", "size_in_bytes": 904}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco/text/strip-prefix.py", "path_type": "hardlink", "sha256": "35f55757c255368ea7a9cb980127cc57bff2e04a3cccc42a942386bc09d1215c", "sha256_in_prefix": "35f55757c255368ea7a9cb980127cc57bff2e04a3cccc42a942386bc09d1215c", "size_in_bytes": 412}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco/text/to-dvorak.py", "path_type": "hardlink", "sha256": "d5235c6d2b2f212a575e0f8b9f26c81c763e45414e42bdfacdc1e4635a5616ac", "sha256_in_prefix": "d5235c6d2b2f212a575e0f8b9f26c81c763e45414e42bdfacdc1e4635a5616ac", "size_in_bytes": 119}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco/text/to-qwerty.py", "path_type": "hardlink", "sha256": "b3850c4149cfc059ff741e6e642dbb06eac7390718a277417f322954be69133c", "sha256_in_prefix": "b3850c4149cfc059ff741e6e642dbb06eac7390718a277417f322954be69133c", "size_in_bytes": 119}, {"_path": "Lib/site-packages/setuptools/_vendor/more_itertools-10.3.0.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "Lib/site-packages/setuptools/_vendor/more_itertools-10.3.0.dist-info/LICENSE", "path_type": "hardlink", "sha256": "09f1c8c9e941af3e584d59641ea9b87d83c0cb0fd007eb5ef391a7e2643c1a46", "sha256_in_prefix": "09f1c8c9e941af3e584d59641ea9b87d83c0cb0fd007eb5ef391a7e2643c1a46", "size_in_bytes": 1053}, {"_path": "Lib/site-packages/setuptools/_vendor/more_itertools-10.3.0.dist-info/METADATA", "path_type": "hardlink", "sha256": "0453bdd0ef9f2cd89540ca63ee8212e73b73809514419dd3037d8fe471f737e0", "sha256_in_prefix": "0453bdd0ef9f2cd89540ca63ee8212e73b73809514419dd3037d8fe471f737e0", "size_in_bytes": 36293}, {"_path": "Lib/site-packages/setuptools/_vendor/more_itertools-10.3.0.dist-info/RECORD", "path_type": "hardlink", "sha256": "77c8e73e018dc0fd7e9ed6c80b05a4404545f641fb085220ce42b368b59aa3d3", "sha256_in_prefix": "77c8e73e018dc0fd7e9ed6c80b05a4404545f641fb085220ce42b368b59aa3d3", "size_in_bytes": 1259}, {"_path": "Lib/site-packages/setuptools/_vendor/more_itertools-10.3.0.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_vendor/more_itertools-10.3.0.dist-info/WHEEL", "path_type": "hardlink", "sha256": "ad282afc9a4717d7c7475971e77ab083fd7ed8bca9644fea99cb976d552af78f", "sha256_in_prefix": "ad282afc9a4717d7c7475971e77ab083fd7ed8bca9644fea99cb976d552af78f", "size_in_bytes": 81}, {"_path": "Lib/site-packages/setuptools/_vendor/more_itertools/__init__.py", "path_type": "hardlink", "sha256": "76d01b1a34c39a7fe08625396177e1c83cb4a610255d576de649590397d46be4", "sha256_in_prefix": "76d01b1a34c39a7fe08625396177e1c83cb4a610255d576de649590397d46be4", "size_in_bytes": 149}, {"_path": "Lib/site-packages/setuptools/_vendor/more_itertools/__init__.pyi", "path_type": "hardlink", "sha256": "e41dde4f338dd4106e38ba1bd6f09f97211bda549deaeb17410f82bfe85791e0", "sha256_in_prefix": "e41dde4f338dd4106e38ba1bd6f09f97211bda549deaeb17410f82bfe85791e0", "size_in_bytes": 43}, {"_path": "Lib/site-packages/setuptools/_vendor/more_itertools/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "f58386790a22ce619099a35e297b224dfb82ce37a1bb7e24c3d77f01385e7c75", "sha256_in_prefix": "f58386790a22ce619099a35e297b224dfb82ce37a1bb7e24c3d77f01385e7c75", "size_in_bytes": 337}, {"_path": "Lib/site-packages/setuptools/_vendor/more_itertools/__pycache__/more.cpython-311.pyc", "path_type": "hardlink", "sha256": "0b2794505107c164c68ef0759060d923a1fd4823cd9f9d599aa298446c72431d", "sha256_in_prefix": "0b2794505107c164c68ef0759060d923a1fd4823cd9f9d599aa298446c72431d", "size_in_bytes": 189622}, {"_path": "Lib/site-packages/setuptools/_vendor/more_itertools/__pycache__/recipes.cpython-311.pyc", "path_type": "hardlink", "sha256": "8c9643a5a5f42e3ffbbde7a140745c62fd81af40c833ff2deac77eba64e4dea1", "sha256_in_prefix": "8c9643a5a5f42e3ffbbde7a140745c62fd81af40c833ff2deac77eba64e4dea1", "size_in_bytes": 39749}, {"_path": "Lib/site-packages/setuptools/_vendor/more_itertools/more.py", "path_type": "hardlink", "sha256": "d44e64cc59dc44a4c3c34718bf5c915cc80376e9ecb4b41dd504ad7272fa53dd", "sha256_in_prefix": "d44e64cc59dc44a4c3c34718bf5c915cc80376e9ecb4b41dd504ad7272fa53dd", "size_in_bytes": 148370}, {"_path": "Lib/site-packages/setuptools/_vendor/more_itertools/more.pyi", "path_type": "hardlink", "sha256": "8975deaade3c3717bc5469885a99155ee2a947615836ebb60d4f2740b5820aed", "sha256_in_prefix": "8975deaade3c3717bc5469885a99155ee2a947615836ebb60d4f2740b5820aed", "size_in_bytes": 21484}, {"_path": "Lib/site-packages/setuptools/_vendor/more_itertools/py.typed", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_vendor/more_itertools/recipes.py", "path_type": "hardlink", "sha256": "59e76185f846560aface28bc7c86c62559914f0d1929188b2a76010c626fe276", "sha256_in_prefix": "59e76185f846560aface28bc7c86c62559914f0d1929188b2a76010c626fe276", "size_in_bytes": 28591}, {"_path": "Lib/site-packages/setuptools/_vendor/more_itertools/recipes.pyi", "path_type": "hardlink", "sha256": "4ff99d1a970575facfdc94966f0cd83fd272355f86a3eed13dfa717dfb405a50", "sha256_in_prefix": "4ff99d1a970575facfdc94966f0cd83fd272355f86a3eed13dfa717dfb405a50", "size_in_bytes": 4617}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging-24.2.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging-24.2.dist-info/LICENSE", "path_type": "hardlink", "sha256": "cad1ef5bd340d73e074ba614d26f7deaca5c7940c3d8c34852e65c4909686c48", "sha256_in_prefix": "cad1ef5bd340d73e074ba614d26f7deaca5c7940c3d8c34852e65c4909686c48", "size_in_bytes": 197}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging-24.2.dist-info/LICENSE.APACHE", "path_type": "hardlink", "sha256": "0d542e0c8804e39aa7f37eb00da5a762149dc682d7829451287e11b938e94594", "sha256_in_prefix": "0d542e0c8804e39aa7f37eb00da5a762149dc682d7829451287e11b938e94594", "size_in_bytes": 10174}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging-24.2.dist-info/LICENSE.BSD", "path_type": "hardlink", "sha256": "b70e7e9b742f1cc6f948b34c16aa39ffece94196364bc88ff0d2180f0028fac5", "sha256_in_prefix": "b70e7e9b742f1cc6f948b34c16aa39ffece94196364bc88ff0d2180f0028fac5", "size_in_bytes": 1344}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging-24.2.dist-info/METADATA", "path_type": "hardlink", "sha256": "a211fceacea4e6621f4316364d2d0b7127c00de3856b8062082f9bc5957ea4db", "sha256_in_prefix": "a211fceacea4e6621f4316364d2d0b7127c00de3856b8062082f9bc5957ea4db", "size_in_bytes": 3204}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging-24.2.dist-info/RECORD", "path_type": "hardlink", "sha256": "6380eb5ccd0a63402b7f385b0046b52d814fc16dd612011e2f8882a977be03e3", "sha256_in_prefix": "6380eb5ccd0a63402b7f385b0046b52d814fc16dd612011e2f8882a977be03e3", "size_in_bytes": 2009}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging-24.2.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging-24.2.dist-info/WHEEL", "path_type": "hardlink", "sha256": "0a950253178741b44de54191407611268acee407fe432fdf1cc72d710f034862", "sha256_in_prefix": "0a950253178741b44de54191407611268acee407fe432fdf1cc72d710f034862", "size_in_bytes": 82}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/__init__.py", "path_type": "hardlink", "sha256": "764e136bfbe67552716070dc7f286f40dc3c5773e0481a2628d5ea83e0f62436", "sha256_in_prefix": "764e136bfbe67552716070dc7f286f40dc3c5773e0481a2628d5ea83e0f62436", "size_in_bytes": 494}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "b244a8242a3e65124c0668b4eab4505658cd69d4143ce282a4f80b2be1cca094", "sha256_in_prefix": "b244a8242a3e65124c0668b4eab4505658cd69d4143ce282a4f80b2be1cca094", "size_in_bytes": 527}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/_elffile.cpython-311.pyc", "path_type": "hardlink", "sha256": "e97f6375d93f14a88e157df53e86a2ee8b5d4877117f63fb09c88668da7a5e67", "sha256_in_prefix": "e97f6375d93f14a88e157df53e86a2ee8b5d4877117f63fb09c88668da7a5e67", "size_in_bytes": 5485}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/_manylinux.cpython-311.pyc", "path_type": "hardlink", "sha256": "711ef28b39953740d4c969e6e9344c40e7d9b3703c48bc1cdc58a6376833ec59", "sha256_in_prefix": "711ef28b39953740d4c969e6e9344c40e7d9b3703c48bc1cdc58a6376833ec59", "size_in_bytes": 10918}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/_musllinux.cpython-311.pyc", "path_type": "hardlink", "sha256": "d5efbeb377a569809724734e0b62d97732c5a9010ebf7f9a1f63f2bb15f0b7d6", "sha256_in_prefix": "d5efbeb377a569809724734e0b62d97732c5a9010ebf7f9a1f63f2bb15f0b7d6", "size_in_bytes": 5278}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/_parser.cpython-311.pyc", "path_type": "hardlink", "sha256": "52b7f500bf20057d71db82b9cd91bbe0f5b6b82f7949a0144357fbc56dbed521", "sha256_in_prefix": "52b7f500bf20057d71db82b9cd91bbe0f5b6b82f7949a0144357fbc56dbed521", "size_in_bytes": 16250}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/_structures.cpython-311.pyc", "path_type": "hardlink", "sha256": "661ce0e114e16053b0ec1f312dcb925a649de34081fbe32f1fdcf2e9dd42244f", "sha256_in_prefix": "661ce0e114e16053b0ec1f312dcb925a649de34081fbe32f1fdcf2e9dd42244f", "size_in_bytes": 3651}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/_tokenizer.cpython-311.pyc", "path_type": "hardlink", "sha256": "6f623ed39f1b40ab58cd3ed3848e18960b8e15953aa4bf202a7e5e02214070fe", "sha256_in_prefix": "6f623ed39f1b40ab58cd3ed3848e18960b8e15953aa4bf202a7e5e02214070fe", "size_in_bytes": 8515}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/markers.cpython-311.pyc", "path_type": "hardlink", "sha256": "03e404fff9c0441bf8560963e1703fed742bf44bd810399e278a2b816f14c5a1", "sha256_in_prefix": "03e404fff9c0441bf8560963e1703fed742bf44bd810399e278a2b816f14c5a1", "size_in_bytes": 13070}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/metadata.cpython-311.pyc", "path_type": "hardlink", "sha256": "cdf1d98fb67514b68e2bb866d121278eeb64db7be9be5c59fa5722577b8fce55", "sha256_in_prefix": "cdf1d98fb67514b68e2bb866d121278eeb64db7be9be5c59fa5722577b8fce55", "size_in_bytes": 31057}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/requirements.cpython-311.pyc", "path_type": "hardlink", "sha256": "c1cb5f4ab5a5df06b42b551b484e4b8e5966c9ce2374fce067540a02854e5c56", "sha256_in_prefix": "c1cb5f4ab5a5df06b42b551b484e4b8e5966c9ce2374fce067540a02854e5c56", "size_in_bytes": 4692}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/specifiers.cpython-311.pyc", "path_type": "hardlink", "sha256": "48290e67d9f1c86269311c4ec0e939b43f10459bab1940aaf9fedb0ddd32ba33", "sha256_in_prefix": "48290e67d9f1c86269311c4ec0e939b43f10459bab1940aaf9fedb0ddd32ba33", "size_in_bytes": 41479}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/tags.cpython-311.pyc", "path_type": "hardlink", "sha256": "c73ef084e59ee1676f7ad90d1140c057116cd4f98583694a6c09408f8e72104f", "sha256_in_prefix": "c73ef084e59ee1676f7ad90d1140c057116cd4f98583694a6c09408f8e72104f", "size_in_bytes": 25851}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/utils.cpython-311.pyc", "path_type": "hardlink", "sha256": "b652c6621def7aea1b038cc90377d489855523faf044dc960f21eeda88c9d5bd", "sha256_in_prefix": "b652c6621def7aea1b038cc90377d489855523faf044dc960f21eeda88c9d5bd", "size_in_bytes": 7553}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/version.cpython-311.pyc", "path_type": "hardlink", "sha256": "c9b7b86718023f589897257577f713389df4b25fa499af9d510ebf559f2633f9", "sha256_in_prefix": "c9b7b86718023f589897257577f713389df4b25fa499af9d510ebf559f2633f9", "size_in_bytes": 21939}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/_elffile.py", "path_type": "hardlink", "sha256": "71f940400904db9b738589aafda0a2ef641f6d3fed9fcf75b4fcdfa5b7873b01", "sha256_in_prefix": "71f940400904db9b738589aafda0a2ef641f6d3fed9fcf75b4fcdfa5b7873b01", "size_in_bytes": 3306}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/_manylinux.py", "path_type": "hardlink", "sha256": "be5e4e0a8cf8931f341f9af05ca7975a397d58d2121a6af86604e94cff6553d7", "sha256_in_prefix": "be5e4e0a8cf8931f341f9af05ca7975a397d58d2121a6af86604e94cff6553d7", "size_in_bytes": 9612}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/_musllinux.py", "path_type": "hardlink", "sha256": "a7d66a35888e22d19e7bc29c64578717f61c76157018774aeabfbc9608b1bc64", "sha256_in_prefix": "a7d66a35888e22d19e7bc29c64578717f61c76157018774aeabfbc9608b1bc64", "size_in_bytes": 2694}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/_parser.py", "path_type": "hardlink", "sha256": "b3f4ef4ef0cd2b436b336401dd529385d58533835cd0fe899e439b925dcc8e93", "sha256_in_prefix": "b3f4ef4ef0cd2b436b336401dd529385d58533835cd0fe899e439b925dcc8e93", "size_in_bytes": 10236}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/_structures.py", "path_type": "hardlink", "sha256": "ab77953666d62461bf4b40e2b7f4b7028f2a42acffe4f6135c500a0597b9cabe", "sha256_in_prefix": "ab77953666d62461bf4b40e2b7f4b7028f2a42acffe4f6135c500a0597b9cabe", "size_in_bytes": 1431}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/_tokenizer.py", "path_type": "hardlink", "sha256": "27abf91fb273bdbfa0f35c69ff640008ac0eecbc47400ea292bc8c53bcd7c0df", "sha256_in_prefix": "27abf91fb273bdbfa0f35c69ff640008ac0eecbc47400ea292bc8c53bcd7c0df", "size_in_bytes": 5273}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/licenses/__init__.py", "path_type": "hardlink", "sha256": "d71e4cd671188dc83011b2edd1d5f0cf6ba48ebd7c0e20b30b4b2b690a89f96c", "sha256_in_prefix": "d71e4cd671188dc83011b2edd1d5f0cf6ba48ebd7c0e20b30b4b2b690a89f96c", "size_in_bytes": 5715}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/licenses/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "91ef2c818566a95a65a4967cca6cb43750e810c32133b27e23a8011e3ee478e5", "sha256_in_prefix": "91ef2c818566a95a65a4967cca6cb43750e810c32133b27e23a8011e3ee478e5", "size_in_bytes": 5011}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/licenses/__pycache__/_spdx.cpython-311.pyc", "path_type": "hardlink", "sha256": "42b8d7f2a62fd50e0b36b498efaebf6455997c5d4df41681a456f9be8f709b03", "sha256_in_prefix": "42b8d7f2a62fd50e0b36b498efaebf6455997c5d4df41681a456f9be8f709b03", "size_in_bytes": 50235}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/licenses/_spdx.py", "path_type": "hardlink", "sha256": "a009b5ced3c5c25b2608a7bb94002cbff38839f4b57160eef5b34191ebbeda7b", "sha256_in_prefix": "a009b5ced3c5c25b2608a7bb94002cbff38839f4b57160eef5b34191ebbeda7b", "size_in_bytes": 48398}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/markers.py", "path_type": "hardlink", "sha256": "73cf5337307b65d198864a2f9ba3d89aa1b21f15e561568b5b9f753c750d283f", "sha256_in_prefix": "73cf5337307b65d198864a2f9ba3d89aa1b21f15e561568b5b9f753c750d283f", "size_in_bytes": 10561}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/metadata.py", "path_type": "hardlink", "sha256": "60989b33b1987b8adef3ed1adce9579864be5c38131283b8b6506ddaadb90678", "sha256_in_prefix": "60989b33b1987b8adef3ed1adce9579864be5c38131283b8b6506ddaadb90678", "size_in_bytes": 34762}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/py.typed", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/requirements.py", "path_type": "hardlink", "sha256": "818c9148075bac8c8a0d8ebaba02035108d132fc641f600b8a84e65f7b672faa", "sha256_in_prefix": "818c9148075bac8c8a0d8ebaba02035108d132fc641f600b8a84e65f7b672faa", "size_in_bytes": 2947}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/specifiers.py", "path_type": "hardlink", "sha256": "186d703cd31c2f47cc24eebcbc5e77c0a31dc277de84371a23eafd3694df8a50", "sha256_in_prefix": "186d703cd31c2f47cc24eebcbc5e77c0a31dc277de84371a23eafd3694df8a50", "size_in_bytes": 40074}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/tags.py", "path_type": "hardlink", "sha256": "085aab2730337365cd19ec5eac7fff4fe639230abb59bb185ec88b1112d6c58d", "sha256_in_prefix": "085aab2730337365cd19ec5eac7fff4fe639230abb59bb185ec88b1112d6c58d", "size_in_bytes": 21014}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/utils.py", "path_type": "hardlink", "sha256": "d05dc787d385b9182b8538066549792b6d85bf560fdad665d73ff680eea42620", "sha256_in_prefix": "d05dc787d385b9182b8538066549792b6d85bf560fdad665d73ff680eea42620", "size_in_bytes": 5050}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/version.py", "path_type": "hardlink", "sha256": "a257f2ba4fc33db7e5364278c0159eb57435edcef8c770c1e74d5d7a052fec36", "sha256_in_prefix": "a257f2ba4fc33db7e5364278c0159eb57435edcef8c770c1e74d5d7a052fec36", "size_in_bytes": 16676}, {"_path": "Lib/site-packages/setuptools/_vendor/platformdirs-4.2.2.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "Lib/site-packages/setuptools/_vendor/platformdirs-4.2.2.dist-info/METADATA", "path_type": "hardlink", "sha256": "ce6b227b4d46d4cb57474c2022fe57a557933bb89daf4596bdf9b12ac296b869", "sha256_in_prefix": "ce6b227b4d46d4cb57474c2022fe57a557933bb89daf4596bdf9b12ac296b869", "size_in_bytes": 11429}, {"_path": "Lib/site-packages/setuptools/_vendor/platformdirs-4.2.2.dist-info/RECORD", "path_type": "hardlink", "sha256": "4c211d76d42ed40efc3acfcc866d8912a718afbca2b7e51849442366d6e99fe8", "sha256_in_prefix": "4c211d76d42ed40efc3acfcc866d8912a718afbca2b7e51849442366d6e99fe8", "size_in_bytes": 1642}, {"_path": "Lib/site-packages/setuptools/_vendor/platformdirs-4.2.2.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_vendor/platformdirs-4.2.2.dist-info/WHEEL", "path_type": "hardlink", "sha256": "cc431c46bf4aaf4df1d68cc6c20e6ff4d4012a7de49dda7a2d2a1295583e8e15", "sha256_in_prefix": "cc431c46bf4aaf4df1d68cc6c20e6ff4d4012a7de49dda7a2d2a1295583e8e15", "size_in_bytes": 87}, {"_path": "Lib/site-packages/setuptools/_vendor/platformdirs-4.2.2.dist-info/licenses/LICENSE", "path_type": "hardlink", "sha256": "29e0fd62e929850e86eb28c3fdccf0cefdf4fa94879011cffb3d0d4bed6d4db6", "sha256_in_prefix": "29e0fd62e929850e86eb28c3fdccf0cefdf4fa94879011cffb3d0d4bed6d4db6", "size_in_bytes": 1089}, {"_path": "Lib/site-packages/setuptools/_vendor/platformdirs/__init__.py", "path_type": "hardlink", "sha256": "10c184f2a787451f42cc316bf242f7b40f217596678988d705565dd1419358ad", "sha256_in_prefix": "10c184f2a787451f42cc316bf242f7b40f217596678988d705565dd1419358ad", "size_in_bytes": 22225}, {"_path": "Lib/site-packages/setuptools/_vendor/platformdirs/__main__.py", "path_type": "hardlink", "sha256": "1e7b14407a6205a893c70726c15c3e9c568f755359b5021d8b57960ed23e3332", "sha256_in_prefix": "1e7b14407a6205a893c70726c15c3e9c568f755359b5021d8b57960ed23e3332", "size_in_bytes": 1493}, {"_path": "Lib/site-packages/setuptools/_vendor/platformdirs/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "c1ab170acd0d59665d4429098daa44279c119bcf84546054d3b5d6c8228c3675", "sha256_in_prefix": "c1ab170acd0d59665d4429098daa44279c119bcf84546054d3b5d6c8228c3675", "size_in_bytes": 19110}, {"_path": "Lib/site-packages/setuptools/_vendor/platformdirs/__pycache__/__main__.cpython-311.pyc", "path_type": "hardlink", "sha256": "57ae4e3c270069be4de5b8b60feac5d95bd73e06b3d719332757082a194d0dd6", "sha256_in_prefix": "57ae4e3c270069be4de5b8b60feac5d95bd73e06b3d719332757082a194d0dd6", "size_in_bytes": 2252}, {"_path": "Lib/site-packages/setuptools/_vendor/platformdirs/__pycache__/android.cpython-311.pyc", "path_type": "hardlink", "sha256": "7d9e6b03b38b596e5e649dce35a652b5477a8a5168efa32b5d3174a14653f9f2", "sha256_in_prefix": "7d9e6b03b38b596e5e649dce35a652b5477a8a5168efa32b5d3174a14653f9f2", "size_in_bytes": 11864}, {"_path": "Lib/site-packages/setuptools/_vendor/platformdirs/__pycache__/api.cpython-311.pyc", "path_type": "hardlink", "sha256": "fc7c2a117175fe736e6ae54b3779d18108a7e82c57904100158f37c5e79a945e", "sha256_in_prefix": "fc7c2a117175fe736e6ae54b3779d18108a7e82c57904100158f37c5e79a945e", "size_in_bytes": 14076}, {"_path": "Lib/site-packages/setuptools/_vendor/platformdirs/__pycache__/macos.cpython-311.pyc", "path_type": "hardlink", "sha256": "f63c2e16493def70405f5a51fa866c2fee780a5a7af230f10af675f10f1688a8", "sha256_in_prefix": "f63c2e16493def70405f5a51fa866c2fee780a5a7af230f10af675f10f1688a8", "size_in_bytes": 8485}, {"_path": "Lib/site-packages/setuptools/_vendor/platformdirs/__pycache__/unix.cpython-311.pyc", "path_type": "hardlink", "sha256": "361059454e8787dfe407bfba942626b160bd26b9067caada23940370a4855e14", "sha256_in_prefix": "361059454e8787dfe407bfba942626b160bd26b9067caada23940370a4855e14", "size_in_bytes": 16529}, {"_path": "Lib/site-packages/setuptools/_vendor/platformdirs/__pycache__/version.cpython-311.pyc", "path_type": "hardlink", "sha256": "5c41ec63f4f7694c978662a65bd63a29a5cc5abc23418ee0692a8ad58040f4a1", "sha256_in_prefix": "5c41ec63f4f7694c978662a65bd63a29a5cc5abc23418ee0692a8ad58040f4a1", "size_in_bytes": 627}, {"_path": "Lib/site-packages/setuptools/_vendor/platformdirs/__pycache__/windows.cpython-311.pyc", "path_type": "hardlink", "sha256": "b74d0ed5a1977ae94ec0c4f99b263b8e52cd920d3cac1214c0a69c452d473d5d", "sha256_in_prefix": "b74d0ed5a1977ae94ec0c4f99b263b8e52cd920d3cac1214c0a69c452d473d5d", "size_in_bytes": 14670}, {"_path": "Lib/site-packages/setuptools/_vendor/platformdirs/android.py", "path_type": "hardlink", "sha256": "c595d8f49778e963acc53d94ebee47b0db4367e210ab170452b04b977858938a", "sha256_in_prefix": "c595d8f49778e963acc53d94ebee47b0db4367e210ab170452b04b977858938a", "size_in_bytes": 9016}, {"_path": "Lib/site-packages/setuptools/_vendor/platformdirs/api.py", "path_type": "hardlink", "sha256": "40161d51a736782e76d5e93fcb9dee0f50dcabe9495fc22049155de089c2eae7", "sha256_in_prefix": "40161d51a736782e76d5e93fcb9dee0f50dcabe9495fc22049155de089c2eae7", "size_in_bytes": 8996}, {"_path": "Lib/site-packages/setuptools/_vendor/platformdirs/macos.py", "path_type": "hardlink", "sha256": "c1fb6c6ecbeaea767458e4574a20ab64d9111f3fd62ae92d9746ba982ecc1642", "sha256_in_prefix": "c1fb6c6ecbeaea767458e4574a20ab64d9111f3fd62ae92d9746ba982ecc1642", "size_in_bytes": 5580}, {"_path": "Lib/site-packages/setuptools/_vendor/platformdirs/py.typed", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_vendor/platformdirs/unix.py", "path_type": "hardlink", "sha256": "09c8bd5aab77e5d00cb20e874fd9d11874815b9a1b6f4a51dc01352499ec0978", "sha256_in_prefix": "09c8bd5aab77e5d00cb20e874fd9d11874815b9a1b6f4a51dc01352499ec0978", "size_in_bytes": 10643}, {"_path": "Lib/site-packages/setuptools/_vendor/platformdirs/version.py", "path_type": "hardlink", "sha256": "afb17bead6518e040aceba71fc8d3f64c40e314f8f4bb7869c70fbcc42b7281d", "sha256_in_prefix": "afb17bead6518e040aceba71fc8d3f64c40e314f8f4bb7869c70fbcc42b7281d", "size_in_bytes": 411}, {"_path": "Lib/site-packages/setuptools/_vendor/platformdirs/windows.py", "path_type": "hardlink", "sha256": "205a62a21501c313ed0b39722b036dc725b8264f2169ae96f28e7d99fac35d5a", "sha256_in_prefix": "205a62a21501c313ed0b39722b036dc725b8264f2169ae96f28e7d99fac35d5a", "size_in_bytes": 10125}, {"_path": "Lib/site-packages/setuptools/_vendor/tomli-2.0.1.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "Lib/site-packages/setuptools/_vendor/tomli-2.0.1.dist-info/LICENSE", "path_type": "hardlink", "sha256": "b80816b0d530b8accb4c2211783790984a6e3b61922c2b5ee92f3372ab2742fe", "sha256_in_prefix": "b80816b0d530b8accb4c2211783790984a6e3b61922c2b5ee92f3372ab2742fe", "size_in_bytes": 1072}, {"_path": "Lib/site-packages/setuptools/_vendor/tomli-2.0.1.dist-info/METADATA", "path_type": "hardlink", "sha256": "ccf0dc78a98fc0918b5ad67292b1e2c4bed65575a6246cd9d63c914f9942a0f2", "sha256_in_prefix": "ccf0dc78a98fc0918b5ad67292b1e2c4bed65575a6246cd9d63c914f9942a0f2", "size_in_bytes": 8875}, {"_path": "Lib/site-packages/setuptools/_vendor/tomli-2.0.1.dist-info/RECORD", "path_type": "hardlink", "sha256": "0cb9f9a451a1e365ac54b4c88662e1da0cb54a72d16a5258fb0abff9d3e1c022", "sha256_in_prefix": "0cb9f9a451a1e365ac54b4c88662e1da0cb54a72d16a5258fb0abff9d3e1c022", "size_in_bytes": 999}, {"_path": "Lib/site-packages/setuptools/_vendor/tomli-2.0.1.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_vendor/tomli-2.0.1.dist-info/WHEEL", "path_type": "hardlink", "sha256": "8cf311fc3ce47385f889c42d9b3f35967358fe402c7e883baf2eeaa11bd82d7c", "sha256_in_prefix": "8cf311fc3ce47385f889c42d9b3f35967358fe402c7e883baf2eeaa11bd82d7c", "size_in_bytes": 81}, {"_path": "Lib/site-packages/setuptools/_vendor/tomli/__init__.py", "path_type": "hardlink", "sha256": "26153057ae830758381efb7551009531d7c2bbe220015f055e6bc353da27c5de", "sha256_in_prefix": "26153057ae830758381efb7551009531d7c2bbe220015f055e6bc353da27c5de", "size_in_bytes": 396}, {"_path": "Lib/site-packages/setuptools/_vendor/tomli/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "c756b96cb7ee0d0ef75a3a0445e917eea3ec56bd95756f514c927f362b92e5f0", "sha256_in_prefix": "c756b96cb7ee0d0ef75a3a0445e917eea3ec56bd95756f514c927f362b92e5f0", "size_in_bytes": 379}, {"_path": "Lib/site-packages/setuptools/_vendor/tomli/__pycache__/_parser.cpython-311.pyc", "path_type": "hardlink", "sha256": "d97031a4eccde1e8833bd8ba0af342bc6ec52b38a7b30802da9fc32af0bfc2fe", "sha256_in_prefix": "d97031a4eccde1e8833bd8ba0af342bc6ec52b38a7b30802da9fc32af0bfc2fe", "size_in_bytes": 30818}, {"_path": "Lib/site-packages/setuptools/_vendor/tomli/__pycache__/_re.cpython-311.pyc", "path_type": "hardlink", "sha256": "bfb10ec778ae986066a1779508fb8138e105c9b263ca2f47edae6c835815b276", "sha256_in_prefix": "bfb10ec778ae986066a1779508fb8138e105c9b263ca2f47edae6c835815b276", "size_in_bytes": 4458}, {"_path": "Lib/site-packages/setuptools/_vendor/tomli/__pycache__/_types.cpython-311.pyc", "path_type": "hardlink", "sha256": "cd3aef0218f9a82c480aa959d0411695d3900143a93aaa31665e5592f6ccc8f3", "sha256_in_prefix": "cd3aef0218f9a82c480aa959d0411695d3900143a93aaa31665e5592f6ccc8f3", "size_in_bytes": 371}, {"_path": "Lib/site-packages/setuptools/_vendor/tomli/_parser.py", "path_type": "hardlink", "sha256": "83df8435a00b4be07c768918a42bb35056a55a5a20ed3f922183232d9496aed3", "sha256_in_prefix": "83df8435a00b4be07c768918a42bb35056a55a5a20ed3f922183232d9496aed3", "size_in_bytes": 22633}, {"_path": "Lib/site-packages/setuptools/_vendor/tomli/_re.py", "path_type": "hardlink", "sha256": "75b8e0e428594f6dca6bdcfd0c73977ddb52a4fc147dd80c5e78fc34ea25cbec", "sha256_in_prefix": "75b8e0e428594f6dca6bdcfd0c73977ddb52a4fc147dd80c5e78fc34ea25cbec", "size_in_bytes": 2943}, {"_path": "Lib/site-packages/setuptools/_vendor/tomli/_types.py", "path_type": "hardlink", "sha256": "f864c6d9552a929c7032ace654ee05ef26ca75d21b027b801d77e65907138b74", "sha256_in_prefix": "f864c6d9552a929c7032ace654ee05ef26ca75d21b027b801d77e65907138b74", "size_in_bytes": 254}, {"_path": "Lib/site-packages/setuptools/_vendor/tomli/py.typed", "path_type": "hardlink", "sha256": "f0f8f2675695a10a5156fb7bd66bafbaae6a13e8d315990af862c792175e6e67", "sha256_in_prefix": "f0f8f2675695a10a5156fb7bd66bafbaae6a13e8d315990af862c792175e6e67", "size_in_bytes": 26}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard-4.3.0.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard-4.3.0.dist-info/LICENSE", "path_type": "hardlink", "sha256": "6163f7987dfb38d6bc320ce2b70b2f02b862bc41126516d552ef1cd43247e758", "sha256_in_prefix": "6163f7987dfb38d6bc320ce2b70b2f02b862bc41126516d552ef1cd43247e758", "size_in_bytes": 1130}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard-4.3.0.dist-info/METADATA", "path_type": "hardlink", "sha256": "cf675c1c0a744f08580855390de87cc77d676b312582e8d4cfdb5bb8fd298d21", "sha256_in_prefix": "cf675c1c0a744f08580855390de87cc77d676b312582e8d4cfdb5bb8fd298d21", "size_in_bytes": 3717}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard-4.3.0.dist-info/RECORD", "path_type": "hardlink", "sha256": "48a51959582478352275428ceecd78ef77d79ac9dae796e39a2eaf2540282552", "sha256_in_prefix": "48a51959582478352275428ceecd78ef77d79ac9dae796e39a2eaf2540282552", "size_in_bytes": 2402}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard-4.3.0.dist-info/WHEEL", "path_type": "hardlink", "sha256": "189eedfe4581172c1b6a02b97a8f48a14c0b5baa3239e4ca990fbd8871553714", "sha256_in_prefix": "189eedfe4581172c1b6a02b97a8f48a14c0b5baa3239e4ca990fbd8871553714", "size_in_bytes": 92}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard-4.3.0.dist-info/entry_points.txt", "path_type": "hardlink", "sha256": "aa9ecd43568bb624a0310aa8ea05a57c6a72d08217ce830999e4132e9cea1594", "sha256_in_prefix": "aa9ecd43568bb624a0310aa8ea05a57c6a72d08217ce830999e4132e9cea1594", "size_in_bytes": 48}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard-4.3.0.dist-info/top_level.txt", "path_type": "hardlink", "sha256": "e33dbc021b83a1dc114bf73527f97c1f9d6de50bb07d3b1eb24633971a7a82bb", "sha256_in_prefix": "e33dbc021b83a1dc114bf73527f97c1f9d6de50bb07d3b1eb24633971a7a82bb", "size_in_bytes": 10}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/__init__.py", "path_type": "hardlink", "sha256": "3a7878c37f1e94f0a3b65714dc963d93787bd0d8ecc5722401f966427f99d056", "sha256_in_prefix": "3a7878c37f1e94f0a3b65714dc963d93787bd0d8ecc5722401f966427f99d056", "size_in_bytes": 2071}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "8724531368984b35b1eaf01c8f20cb2c76f5c498e38dce5c046662fa9866db6b", "sha256_in_prefix": "8724531368984b35b1eaf01c8f20cb2c76f5c498e38dce5c046662fa9866db6b", "size_in_bytes": 2538}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/__pycache__/_checkers.cpython-311.pyc", "path_type": "hardlink", "sha256": "4b2a6c190690cb0932fe13e7cd839aa48c52753638955f21f70a3e90e7d4b4bc", "sha256_in_prefix": "4b2a6c190690cb0932fe13e7cd839aa48c52753638955f21f70a3e90e7d4b4bc", "size_in_bytes": 38445}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/__pycache__/_config.cpython-311.pyc", "path_type": "hardlink", "sha256": "7d1f800d0b71bcd4b8f4d18fcab4a5ea2b92276c50c1275a98bf619befd860e7", "sha256_in_prefix": "7d1f800d0b71bcd4b8f4d18fcab4a5ea2b92276c50c1275a98bf619befd860e7", "size_in_bytes": 4246}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/__pycache__/_decorators.cpython-311.pyc", "path_type": "hardlink", "sha256": "a21305b30b124c203504e259dff09c69a4aa7ba1b13c2e1110b55dbdfd795302", "sha256_in_prefix": "a21305b30b124c203504e259dff09c69a4aa7ba1b13c2e1110b55dbdfd795302", "size_in_bytes": 11319}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/__pycache__/_exceptions.cpython-311.pyc", "path_type": "hardlink", "sha256": "8bccb7139b64777e10f98fed833a3562b965434df4be0aace4cb87efc995e4cc", "sha256_in_prefix": "8bccb7139b64777e10f98fed833a3562b965434df4be0aace4cb87efc995e4cc", "size_in_bytes": 3292}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/__pycache__/_functions.cpython-311.pyc", "path_type": "hardlink", "sha256": "83cd378597f9eb288e459245edfc8215516091365fafb0820d71e0d452369af6", "sha256_in_prefix": "83cd378597f9eb288e459245edfc8215516091365fafb0820d71e0d452369af6", "size_in_bytes": 12975}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/__pycache__/_importhook.cpython-311.pyc", "path_type": "hardlink", "sha256": "2170b492d280e3bea4e1dc8d7f008d2d1c588c43e6e42c8977e758cb3465d2fe", "sha256_in_prefix": "2170b492d280e3bea4e1dc8d7f008d2d1c588c43e6e42c8977e758cb3465d2fe", "size_in_bytes": 10354}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/__pycache__/_memo.cpython-311.pyc", "path_type": "hardlink", "sha256": "5295de884a40f864d090a1f6cad172552aaa964ea5b197ba92f774d905561033", "sha256_in_prefix": "5295de884a40f864d090a1f6cad172552aaa964ea5b197ba92f774d905561033", "size_in_bytes": 1848}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/__pycache__/_pytest_plugin.cpython-311.pyc", "path_type": "hardlink", "sha256": "09b0f72d23d7a2d896da9aefd3e377d37afa5ee7e5a4cc5ee7aa4f2d54c679b5", "sha256_in_prefix": "09b0f72d23d7a2d896da9aefd3e377d37afa5ee7e5a4cc5ee7aa4f2d54c679b5", "size_in_bytes": 6477}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/__pycache__/_suppression.cpython-311.pyc", "path_type": "hardlink", "sha256": "2e48ea1db8c214afaf85a02bac6e470bd7745a5ebbb6d680eb32c175c105283b", "sha256_in_prefix": "2e48ea1db8c214afaf85a02bac6e470bd7745a5ebbb6d680eb32c175c105283b", "size_in_bytes": 4267}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/__pycache__/_transformer.cpython-311.pyc", "path_type": "hardlink", "sha256": "2081bc3a554e60807bd034842ca4b974ae07853613d802dc2bae240a25cd6043", "sha256_in_prefix": "2081bc3a554e60807bd034842ca4b974ae07853613d802dc2bae240a25cd6043", "size_in_bytes": 55661}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/__pycache__/_union_transformer.cpython-311.pyc", "path_type": "hardlink", "sha256": "0afb4c825150763db6d0ca6e5042c4a17b43d48e5afe83726cab74854e298842", "sha256_in_prefix": "0afb4c825150763db6d0ca6e5042c4a17b43d48e5afe83726cab74854e298842", "size_in_bytes": 2889}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/__pycache__/_utils.cpython-311.pyc", "path_type": "hardlink", "sha256": "0a557208ef06ea273f07390c90e3a87ee93a12a9fe06c9fa053ef7cd23f02d15", "sha256_in_prefix": "0a557208ef06ea273f07390c90e3a87ee93a12a9fe06c9fa053ef7cd23f02d15", "size_in_bytes": 8564}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/_checkers.py", "path_type": "hardlink", "sha256": "251ae02a271d3847c8068344b5e81808422586969df9ad6ed449bb1828f45822", "sha256_in_prefix": "251ae02a271d3847c8068344b5e81808422586969df9ad6ed449bb1828f45822", "size_in_bytes": 31360}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/_config.py", "path_type": "hardlink", "sha256": "9c8cfc4300dafa814edcbf4ef3feacaf396677df6949bcb6c0e33859bec5fc1d", "sha256_in_prefix": "9c8cfc4300dafa814edcbf4ef3feacaf396677df6949bcb6c0e33859bec5fc1d", "size_in_bytes": 2846}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/_decorators.py", "path_type": "hardlink", "sha256": "bfa76c21e5af3e113118b3ffc1717e4660d4ca365ffc0936f20ee0049fefd3ed", "sha256_in_prefix": "bfa76c21e5af3e113118b3ffc1717e4660d4ca365ffc0936f20ee0049fefd3ed", "size_in_bytes": 9033}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/_exceptions.py", "path_type": "hardlink", "sha256": "6483de895f8505de449b0d8469677616f96caf08b8a1cc13d9f54604802d1dc4", "sha256_in_prefix": "6483de895f8505de449b0d8469677616f96caf08b8a1cc13d9f54604802d1dc4", "size_in_bytes": 1121}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/_functions.py", "path_type": "hardlink", "sha256": "89b81200a6b9a6d226d5e47d0111b4052a3300524f14d01266a84f57241eaa28", "sha256_in_prefix": "89b81200a6b9a6d226d5e47d0111b4052a3300524f14d01266a84f57241eaa28", "size_in_bytes": 10393}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/_importhook.py", "path_type": "hardlink", "sha256": "ba08c20ef15c756314ed4ba0aa5246f7522954da44231b51afef7db3487593b3", "sha256_in_prefix": "ba08c20ef15c756314ed4ba0aa5246f7522954da44231b51afef7db3487593b3", "size_in_bytes": 6389}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/_memo.py", "path_type": "hardlink", "sha256": "d63b9057fbf19c3d8960a6d2ade6e242e8f8a0a1f3ea7ebbbfda5803e0822128", "sha256_in_prefix": "d63b9057fbf19c3d8960a6d2ade6e242e8f8a0a1f3ea7ebbbfda5803e0822128", "size_in_bytes": 1303}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/_pytest_plugin.py", "path_type": "hardlink", "sha256": "f9f712aa4bf9e2b21f205f290dabd8e5840f923e0e5fc18cb7f94bec24120f82", "sha256_in_prefix": "f9f712aa4bf9e2b21f205f290dabd8e5840f923e0e5fc18cb7f94bec24120f82", "size_in_bytes": 4416}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/_suppression.py", "path_type": "hardlink", "sha256": "5507f3c5cc086eede27f47fb54190a33b86460e03bb4d170f5aee3301b26320e", "sha256_in_prefix": "5507f3c5cc086eede27f47fb54190a33b86460e03bb4d170f5aee3301b26320e", "size_in_bytes": 2266}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/_transformer.py", "path_type": "hardlink", "sha256": "f476bbfd085dc285278bfea1bdd63e8596ee11eae0a301eb34bdafcc721a9056", "sha256_in_prefix": "f476bbfd085dc285278bfea1bdd63e8596ee11eae0a301eb34bdafcc721a9056", "size_in_bytes": 44937}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/_union_transformer.py", "path_type": "hardlink", "sha256": "bffe36afbfba1ee457d92a05c27c89f84e4f9715e955e5093c9475f8753da92a", "sha256_in_prefix": "bffe36afbfba1ee457d92a05c27c89f84e4f9715e955e5093c9475f8753da92a", "size_in_bytes": 1354}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/_utils.py", "path_type": "hardlink", "sha256": "e4784ed6b3e7e5fd4ceb29a454012fed79a5cf5717fa3d0e9d3325c87aaaad1f", "sha256_in_prefix": "e4784ed6b3e7e5fd4ceb29a454012fed79a5cf5717fa3d0e9d3325c87aaaad1f", "size_in_bytes": 5270}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/py.typed", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_vendor/typing_extensions-4.12.2.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "Lib/site-packages/setuptools/_vendor/typing_extensions-4.12.2.dist-info/LICENSE", "path_type": "hardlink", "sha256": "3b2f81fe21d181c499c59a256c8e1968455d6689d269aa85373bfb6af41da3bf", "sha256_in_prefix": "3b2f81fe21d181c499c59a256c8e1968455d6689d269aa85373bfb6af41da3bf", "size_in_bytes": 13936}, {"_path": "Lib/site-packages/setuptools/_vendor/typing_extensions-4.12.2.dist-info/METADATA", "path_type": "hardlink", "sha256": "05e51021af1c9d86eb8d6c7e37c4cece733d5065b91a6d8389c5690ed440f16d", "sha256_in_prefix": "05e51021af1c9d86eb8d6c7e37c4cece733d5065b91a6d8389c5690ed440f16d", "size_in_bytes": 3018}, {"_path": "Lib/site-packages/setuptools/_vendor/typing_extensions-4.12.2.dist-info/RECORD", "path_type": "hardlink", "sha256": "7710002d81971e632aa6a2fc33dc5d74aaf5d7caae22040a65d3e31503b05ee9", "sha256_in_prefix": "7710002d81971e632aa6a2fc33dc5d74aaf5d7caae22040a65d3e31503b05ee9", "size_in_bytes": 571}, {"_path": "Lib/site-packages/setuptools/_vendor/typing_extensions-4.12.2.dist-info/WHEEL", "path_type": "hardlink", "sha256": "1196c6921ec87b83e865f450f08d19b8ff5592537f4ef719e83484e546abe33e", "sha256_in_prefix": "1196c6921ec87b83e865f450f08d19b8ff5592537f4ef719e83484e546abe33e", "size_in_bytes": 81}, {"_path": "Lib/site-packages/setuptools/_vendor/typing_extensions.py", "path_type": "hardlink", "sha256": "8307a4a721bd0d51b797158a5f89e2f2eee793759ee6c946f7c980f45dc3250c", "sha256_in_prefix": "8307a4a721bd0d51b797158a5f89e2f2eee793759ee6c946f7c980f45dc3250c", "size_in_bytes": 134451}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel-0.45.1.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel-0.45.1.dist-info/LICENSE.txt", "path_type": "hardlink", "sha256": "30c23618679108f3e8ea1d2a658c7ca417bdfc891c98ef1a89fa4ff0c9828654", "sha256_in_prefix": "30c23618679108f3e8ea1d2a658c7ca417bdfc891c98ef1a89fa4ff0c9828654", "size_in_bytes": 1107}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel-0.45.1.dist-info/METADATA", "path_type": "hardlink", "sha256": "98acfce07ee6ee3b31272cde21c4d53918936f434f315dfd2af3886211a09a30", "sha256_in_prefix": "98acfce07ee6ee3b31272cde21c4d53918936f434f315dfd2af3886211a09a30", "size_in_bytes": 2313}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel-0.45.1.dist-info/RECORD", "path_type": "hardlink", "sha256": "d639f1ac7c993c3715bd42f27c616189b6b86792fdfd1b17afd77170d6e16984", "sha256_in_prefix": "d639f1ac7c993c3715bd42f27c616189b6b86792fdfd1b17afd77170d6e16984", "size_in_bytes": 4900}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel-0.45.1.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel-0.45.1.dist-info/WHEEL", "path_type": "hardlink", "sha256": "0a950253178741b44de54191407611268acee407fe432fdf1cc72d710f034862", "sha256_in_prefix": "0a950253178741b44de54191407611268acee407fe432fdf1cc72d710f034862", "size_in_bytes": 82}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel-0.45.1.dist-info/entry_points.txt", "path_type": "hardlink", "sha256": "ad363505b90f1e1906326e10dc5d29233241cd6da4331a06d68ae27dfbc6740d", "sha256_in_prefix": "ad363505b90f1e1906326e10dc5d29233241cd6da4331a06d68ae27dfbc6740d", "size_in_bytes": 104}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/__init__.py", "path_type": "hardlink", "sha256": "9abc4c9ef757002babfcb59e81b51f879839cac599addeb75099fcf74c2f18d9", "sha256_in_prefix": "9abc4c9ef757002babfcb59e81b51f879839cac599addeb75099fcf74c2f18d9", "size_in_bytes": 59}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/__main__.py", "path_type": "hardlink", "sha256": "3643149ee4c219c3a4818d0804b8010950bf04619c58e471d8af236064b5d941", "sha256_in_prefix": "3643149ee4c219c3a4818d0804b8010950bf04619c58e471d8af236064b5d941", "size_in_bytes": 455}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "b6325dec309217864a6d5966bb4adee298e5c587c5ea21ce7bb8d90867759123", "sha256_in_prefix": "b6325dec309217864a6d5966bb4adee298e5c587c5ea21ce7bb8d90867759123", "size_in_bytes": 251}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/__pycache__/__main__.cpython-311.pyc", "path_type": "hardlink", "sha256": "f5d88f38183ebf56ef24171b91501e0928e36768a34b5c600a07874b87f2ac7a", "sha256_in_prefix": "f5d88f38183ebf56ef24171b91501e0928e36768a34b5c600a07874b87f2ac7a", "size_in_bytes": 1056}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/__pycache__/_bdist_wheel.cpython-311.pyc", "path_type": "hardlink", "sha256": "7c43fcbe881f264e30144e6233e72810d6869827297203208ef3428f43af0c62", "sha256_in_prefix": "7c43fcbe881f264e30144e6233e72810d6869827297203208ef3428f43af0c62", "size_in_bytes": 28537}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/__pycache__/_setuptools_logging.cpython-311.pyc", "path_type": "hardlink", "sha256": "751bf32e4aa0569c539ea51b47baea6935d2a76f8780afd32287e943ae379bf9", "sha256_in_prefix": "751bf32e4aa0569c539ea51b47baea6935d2a76f8780afd32287e943ae379bf9", "size_in_bytes": 1468}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/__pycache__/bdist_wheel.cpython-311.pyc", "path_type": "hardlink", "sha256": "3e26c1c18a674dfd72bb6415910f7cd5c55119e75a5cebec53bcaeb47f986793", "sha256_in_prefix": "3e26c1c18a674dfd72bb6415910f7cd5c55119e75a5cebec53bcaeb47f986793", "size_in_bytes": 884}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/__pycache__/macosx_libfile.cpython-311.pyc", "path_type": "hardlink", "sha256": "bd714c98736b315933db3396c24ab7b6f12b2f0b37fc7d3a78a73279e1bd9895", "sha256_in_prefix": "bd714c98736b315933db3396c24ab7b6f12b2f0b37fc7d3a78a73279e1bd9895", "size_in_bytes": 17811}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/__pycache__/metadata.cpython-311.pyc", "path_type": "hardlink", "sha256": "a1ab9c417ed438ef20f799610c3618b2b1d0316089f36da81545c4f94181e48c", "sha256_in_prefix": "a1ab9c417ed438ef20f799610c3618b2b1d0316089f36da81545c4f94181e48c", "size_in_bytes": 9706}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/__pycache__/util.cpython-311.pyc", "path_type": "hardlink", "sha256": "502fb371d8fbcf9e60d05143a8103af665595e434c2ce92041108a209daf45cd", "sha256_in_prefix": "502fb371d8fbcf9e60d05143a8103af665595e434c2ce92041108a209daf45cd", "size_in_bytes": 1015}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/__pycache__/wheelfile.cpython-311.pyc", "path_type": "hardlink", "sha256": "73f75768049b9dbaa68f22be53765859ae405f7174442e1cf350384472913842", "sha256_in_prefix": "73f75768049b9dbaa68f22be53765859ae405f7174442e1cf350384472913842", "size_in_bytes": 12550}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/_bdist_wheel.py", "path_type": "hardlink", "sha256": "520842423487fe955f71987aa118f34b0fd342171fdda9d2c753a488b48bf363", "sha256_in_prefix": "520842423487fe955f71987aa118f34b0fd342171fdda9d2c753a488b48bf363", "size_in_bytes": 21694}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/_setuptools_logging.py", "path_type": "hardlink", "sha256": "fb9282fa59ded2294e5162037ce92a6a951618c15986e2980c86af219881e643", "sha256_in_prefix": "fb9282fa59ded2294e5162037ce92a6a951618c15986e2980c86af219881e643", "size_in_bytes": 781}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/bdist_wheel.py", "path_type": "hardlink", "sha256": "b697fd5ae7e248ed51b84320e683e121f486f0333388267fe26b82285ebd0aaa", "sha256_in_prefix": "b697fd5ae7e248ed51b84320e683e121f486f0333388267fe26b82285ebd0aaa", "size_in_bytes": 1107}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/cli/__init__.py", "path_type": "hardlink", "sha256": "369abafe32a2d3776121c46799bb85870be2549c703b4b5812712158cbfd709a", "sha256_in_prefix": "369abafe32a2d3776121c46799bb85870be2549c703b4b5812712158cbfd709a", "size_in_bytes": 4402}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/cli/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "dc2f9421de11d25777d318b5898cb1de69f380634d605c35a93ec008ebcf4c52", "sha256_in_prefix": "dc2f9421de11d25777d318b5898cb1de69f380634d605c35a93ec008ebcf4c52", "size_in_bytes": 7774}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/cli/__pycache__/convert.cpython-311.pyc", "path_type": "hardlink", "sha256": "799b2b8b0b2b4749be2948ee2cb0508d06c183dfabef40932fef6dbe94ff89a5", "sha256_in_prefix": "799b2b8b0b2b4749be2948ee2cb0508d06c183dfabef40932fef6dbe94ff89a5", "size_in_bytes": 18557}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/cli/__pycache__/pack.cpython-311.pyc", "path_type": "hardlink", "sha256": "bfc7c130d2644b43123c9bad7e161918ce8ff9b5cf49413fdfcbb6f7e23a9d80", "sha256_in_prefix": "bfc7c130d2644b43123c9bad7e161918ce8ff9b5cf49413fdfcbb6f7e23a9d80", "size_in_bytes": 5832}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/cli/__pycache__/tags.cpython-311.pyc", "path_type": "hardlink", "sha256": "c5cfc570b38678787b7a1716b47385053267647532f3fded8df0fbedc3d2f2b8", "sha256_in_prefix": "c5cfc570b38678787b7a1716b47385053267647532f3fded8df0fbedc3d2f2b8", "size_in_bytes": 7928}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/cli/__pycache__/unpack.cpython-311.pyc", "path_type": "hardlink", "sha256": "4646f46db45c3bb2e45bcc20557792e6d55a6f12452bdf824c1f41759ec95979", "sha256_in_prefix": "4646f46db45c3bb2e45bcc20557792e6d55a6f12452bdf824c1f41759ec95979", "size_in_bytes": 1756}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/cli/convert.py", "path_type": "hardlink", "sha256": "062d27b445dbf674e5942c56793450e23fa73ecdeccd64842a2a46fc68273244", "sha256_in_prefix": "062d27b445dbf674e5942c56793450e23fa73ecdeccd64842a2a46fc68273244", "size_in_bytes": 12634}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/cli/pack.py", "path_type": "hardlink", "sha256": "08015c1dd055ba5bec1d82659dd2953bb28c23d26a053673e628b43cac7108eb", "sha256_in_prefix": "08015c1dd055ba5bec1d82659dd2953bb28c23d26a053673e628b43cac7108eb", "size_in_bytes": 3103}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/cli/tags.py", "path_type": "hardlink", "sha256": "947c3e2da5ab912e49cbfa96730fbaa528de34ceb20230e7a8a2371392534c25", "sha256_in_prefix": "947c3e2da5ab912e49cbfa96730fbaa528de34ceb20230e7a8a2371392534c25", "size_in_bytes": 4760}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/cli/unpack.py", "path_type": "hardlink", "sha256": "63f27bca7c4f4a81454d3ec7d1f3206c195512bc320c670e6e099ee4c06ecf9f", "sha256_in_prefix": "63f27bca7c4f4a81454d3ec7d1f3206c195512bc320c670e6e099ee4c06ecf9f", "size_in_bytes": 1021}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/macosx_libfile.py", "path_type": "hardlink", "sha256": "935c7b084dcb3ed3951aa8fa3574359d319854f69e46b855cd41bf28fab7cc3b", "sha256_in_prefix": "935c7b084dcb3ed3951aa8fa3574359d319854f69e46b855cd41bf28fab7cc3b", "size_in_bytes": 16572}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/metadata.py", "path_type": "hardlink", "sha256": "242e29ee395066ed9b513010d9f7af92a2e383f5fa8273724612e7e8e50ed6d7", "sha256_in_prefix": "242e29ee395066ed9b513010d9f7af92a2e383f5fa8273724612e7e8e50ed6d7", "size_in_bytes": 6171}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/util.py", "path_type": "hardlink", "sha256": "68beda89b1f061481f73c5a5a252f9b577645780dab5b2716476f59301c52405", "sha256_in_prefix": "68beda89b1f061481f73c5a5a252f9b577645780dab5b2716476f59301c52405", "size_in_bytes": 423}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "3af6c543b1b40831d49dd929de8a985b8a32cda92d43a551c12859b367c9670f", "sha256_in_prefix": "3af6c543b1b40831d49dd929de8a985b8a32cda92d43a551c12859b367c9670f", "size_in_bytes": 172}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/LICENSE", "path_type": "hardlink", "sha256": "cad1ef5bd340d73e074ba614d26f7deaca5c7940c3d8c34852e65c4909686c48", "sha256_in_prefix": "cad1ef5bd340d73e074ba614d26f7deaca5c7940c3d8c34852e65c4909686c48", "size_in_bytes": 197}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/LICENSE.APACHE", "path_type": "hardlink", "sha256": "0d542e0c8804e39aa7f37eb00da5a762149dc682d7829451287e11b938e94594", "sha256_in_prefix": "0d542e0c8804e39aa7f37eb00da5a762149dc682d7829451287e11b938e94594", "size_in_bytes": 10174}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/LICENSE.BSD", "path_type": "hardlink", "sha256": "b70e7e9b742f1cc6f948b34c16aa39ffece94196364bc88ff0d2180f0028fac5", "sha256_in_prefix": "b70e7e9b742f1cc6f948b34c16aa39ffece94196364bc88ff0d2180f0028fac5", "size_in_bytes": 1344}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "614b4af9ebce42e7cb79810fb912e8b1a46be4853a87a9b469c9c8e593bd7b6a", "sha256_in_prefix": "614b4af9ebce42e7cb79810fb912e8b1a46be4853a87a9b469c9c8e593bd7b6a", "size_in_bytes": 182}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/_elffile.cpython-311.pyc", "path_type": "hardlink", "sha256": "789b1f5793d4c4aa103bca35d362b702b3372435a3f9d48dbba548872dbc8888", "sha256_in_prefix": "789b1f5793d4c4aa103bca35d362b702b3372435a3f9d48dbba548872dbc8888", "size_in_bytes": 5462}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/_manylinux.cpython-311.pyc", "path_type": "hardlink", "sha256": "95bf1bbf3f490ccf37aeee3a219b10328903edec7bfe87ff32224487c499efe0", "sha256_in_prefix": "95bf1bbf3f490ccf37aeee3a219b10328903edec7bfe87ff32224487c499efe0", "size_in_bytes": 11058}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/_musllinux.cpython-311.pyc", "path_type": "hardlink", "sha256": "e8977dd4044666509064912a16925753a7991ec693f8436db98d6455a3d6387b", "sha256_in_prefix": "e8977dd4044666509064912a16925753a7991ec693f8436db98d6455a3d6387b", "size_in_bytes": 5273}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/_parser.cpython-311.pyc", "path_type": "hardlink", "sha256": "8117f21a8ee0635e7ad57b17f678cbf3d2004009f29db9976b241b375ab5421b", "sha256_in_prefix": "8117f21a8ee0635e7ad57b17f678cbf3d2004009f29db9976b241b375ab5421b", "size_in_bytes": 16296}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/_structures.cpython-311.pyc", "path_type": "hardlink", "sha256": "484b7188ac2b87fffba53181d3a39dba434716c5c0ddd0dc44015e2edb267b16", "sha256_in_prefix": "484b7188ac2b87fffba53181d3a39dba434716c5c0ddd0dc44015e2edb267b16", "size_in_bytes": 3666}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/_tokenizer.cpython-311.pyc", "path_type": "hardlink", "sha256": "531c61a460f8141074547b678eaee636a08ff06505ea8772b7935396e16e75ed", "sha256_in_prefix": "531c61a460f8141074547b678eaee636a08ff06505ea8772b7935396e16e75ed", "size_in_bytes": 8643}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/markers.cpython-311.pyc", "path_type": "hardlink", "sha256": "74da151452a4c3b125627fd988ae04d024fd1a037cabfb727810f434b3f4d07a", "sha256_in_prefix": "74da151452a4c3b125627fd988ae04d024fd1a037cabfb727810f434b3f4d07a", "size_in_bytes": 12031}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/requirements.cpython-311.pyc", "path_type": "hardlink", "sha256": "428b3691478f46abd9380a379bda28f92af1e4477975623059de26e53eb31612", "sha256_in_prefix": "428b3691478f46abd9380a379bda28f92af1e4477975623059de26e53eb31612", "size_in_bytes": 4701}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/specifiers.cpython-311.pyc", "path_type": "hardlink", "sha256": "b57ebe706f391918054c83e065313b59809181ef04d8415f249cfb2b1a94ea9b", "sha256_in_prefix": "b57ebe706f391918054c83e065313b59809181ef04d8415f249cfb2b1a94ea9b", "size_in_bytes": 42016}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/tags.cpython-311.pyc", "path_type": "hardlink", "sha256": "c714d405e8e341b5e207603782d7f4785361f42460d4fe423588c00bc2c9fedc", "sha256_in_prefix": "c714d405e8e341b5e207603782d7f4785361f42460d4fe423588c00bc2c9fedc", "size_in_bytes": 24612}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/utils.cpython-311.pyc", "path_type": "hardlink", "sha256": "68dca2a2c66a11785d80240ba21ab3ecf334521725e7b93229ea8e2f7f145f44", "sha256_in_prefix": "68dca2a2c66a11785d80240ba21ab3ecf334521725e7b93229ea8e2f7f145f44", "size_in_bytes": 8257}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/version.cpython-311.pyc", "path_type": "hardlink", "sha256": "ed922133938725895e398247fa636dacc8a58023e2a95fec345c5f3297b7daa7", "sha256_in_prefix": "ed922133938725895e398247fa636dacc8a58023e2a95fec345c5f3297b7daa7", "size_in_bytes": 21434}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/_elffile.py", "path_type": "hardlink", "sha256": "85b98af0e0fa67b7d8ea1c229c7114703d5bcbb73390688d62eed28671449369", "sha256_in_prefix": "85b98af0e0fa67b7d8ea1c229c7114703d5bcbb73390688d62eed28671449369", "size_in_bytes": 3266}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/_manylinux.py", "path_type": "hardlink", "sha256": "3fbb1d479ffb5c1634f4b55860f8479b274c2482303d75ac878a2593be14ba3e", "sha256_in_prefix": "3fbb1d479ffb5c1634f4b55860f8479b274c2482303d75ac878a2593be14ba3e", "size_in_bytes": 9588}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/_musllinux.py", "path_type": "hardlink", "sha256": "cf5b3c4e8da1434be99ff77e3b68b9ab11b010af1698694bb7777fdba57b35e6", "sha256_in_prefix": "cf5b3c4e8da1434be99ff77e3b68b9ab11b010af1698694bb7777fdba57b35e6", "size_in_bytes": 2674}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/_parser.py", "path_type": "hardlink", "sha256": "e2d4f87a64a5daa4da53b553404d576bda358cc3c2b017b3b18071c8d31437eb", "sha256_in_prefix": "e2d4f87a64a5daa4da53b553404d576bda358cc3c2b017b3b18071c8d31437eb", "size_in_bytes": 10347}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/_structures.py", "path_type": "hardlink", "sha256": "ab77953666d62461bf4b40e2b7f4b7028f2a42acffe4f6135c500a0597b9cabe", "sha256_in_prefix": "ab77953666d62461bf4b40e2b7f4b7028f2a42acffe4f6135c500a0597b9cabe", "size_in_bytes": 1431}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/_tokenizer.py", "path_type": "hardlink", "sha256": "6a50ad6f05e138502614667a050fb0093485a11009db3fb2b087fbfff31327f9", "sha256_in_prefix": "6a50ad6f05e138502614667a050fb0093485a11009db3fb2b087fbfff31327f9", "size_in_bytes": 5292}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/markers.py", "path_type": "hardlink", "sha256": "fd348f2350612583bb069f40cd398743122a1c45576938e60e1f46fb0f2accf0", "sha256_in_prefix": "fd348f2350612583bb069f40cd398743122a1c45576938e60e1f46fb0f2accf0", "size_in_bytes": 8232}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/requirements.py", "path_type": "hardlink", "sha256": "760a01795a6b3eed9813a43c9c67f038f4e30131db45afd918bc978451259fa4", "sha256_in_prefix": "760a01795a6b3eed9813a43c9c67f038f4e30131db45afd918bc978451259fa4", "size_in_bytes": 2933}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/specifiers.py", "path_type": "hardlink", "sha256": "2164add12acb48fef685e5a1002f142f4786bdab3b5c84078ea8958957e63ca1", "sha256_in_prefix": "2164add12acb48fef685e5a1002f142f4786bdab3b5c84078ea8958957e63ca1", "size_in_bytes": 39778}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/tags.py", "path_type": "hardlink", "sha256": "7de7475e2387901c4d6535e8b57bfcb973e630553d69ef93281ba38181e281c0", "sha256_in_prefix": "7de7475e2387901c4d6535e8b57bfcb973e630553d69ef93281ba38181e281c0", "size_in_bytes": 18950}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/utils.py", "path_type": "hardlink", "sha256": "5e07663f7cb1f7ec101058ceecebcc8fd46311fe49951e4714547af6fed243d1", "sha256_in_prefix": "5e07663f7cb1f7ec101058ceecebcc8fd46311fe49951e4714547af6fed243d1", "size_in_bytes": 5268}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/version.py", "path_type": "hardlink", "sha256": "3c525a6190f1060cb191f6211f7490c38a9f13d202096ad39a2b6fab5e32ddbb", "sha256_in_prefix": "3c525a6190f1060cb191f6211f7490c38a9f13d202096ad39a2b6fab5e32ddbb", "size_in_bytes": 16234}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/vendor.txt", "path_type": "hardlink", "sha256": "67610d8c1d62e69adf7b3f0274cd5276bddce99c6fdab451a253292e60677001", "sha256_in_prefix": "67610d8c1d62e69adf7b3f0274cd5276bddce99c6fdab451a253292e60677001", "size_in_bytes": 16}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/wheelfile.py", "path_type": "hardlink", "sha256": "5120adb4d949c1a7f1b79d5860514a1bb8e7c73f1d7e16f2a8064bea331303db", "sha256_in_prefix": "5120adb4d949c1a7f1b79d5860514a1bb8e7c73f1d7e16f2a8064bea331303db", "size_in_bytes": 8411}, {"_path": "Lib/site-packages/setuptools/_vendor/zipp-3.19.2.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "Lib/site-packages/setuptools/_vendor/zipp-3.19.2.dist-info/LICENSE", "path_type": "hardlink", "sha256": "86da0f01aeae46348a3c3d465195dc1ceccde79f79e87769a64b8da04b2a4741", "sha256_in_prefix": "86da0f01aeae46348a3c3d465195dc1ceccde79f79e87769a64b8da04b2a4741", "size_in_bytes": 1023}, {"_path": "Lib/site-packages/setuptools/_vendor/zipp-3.19.2.dist-info/METADATA", "path_type": "hardlink", "sha256": "508ae4fe43081c64b0b0a2828588b3a8cc3430c6693d1676662569400b0dfdb1", "sha256_in_prefix": "508ae4fe43081c64b0b0a2828588b3a8cc3430c6693d1676662569400b0dfdb1", "size_in_bytes": 3575}, {"_path": "Lib/site-packages/setuptools/_vendor/zipp-3.19.2.dist-info/RECORD", "path_type": "hardlink", "sha256": "f316f2e03fd9ade7ebbc0b154706848e2bb8fd568b90935109f0d8e3ce2b9bfe", "sha256_in_prefix": "f316f2e03fd9ade7ebbc0b154706848e2bb8fd568b90935109f0d8e3ce2b9bfe", "size_in_bytes": 1039}, {"_path": "Lib/site-packages/setuptools/_vendor/zipp-3.19.2.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_vendor/zipp-3.19.2.dist-info/WHEEL", "path_type": "hardlink", "sha256": "189eedfe4581172c1b6a02b97a8f48a14c0b5baa3239e4ca990fbd8871553714", "sha256_in_prefix": "189eedfe4581172c1b6a02b97a8f48a14c0b5baa3239e4ca990fbd8871553714", "size_in_bytes": 92}, {"_path": "Lib/site-packages/setuptools/_vendor/zipp-3.19.2.dist-info/top_level.txt", "path_type": "hardlink", "sha256": "8806dda121df686a817d56f65ee47d26a4901c2a0eb0eb46eb2f42fcb4a9a85c", "sha256_in_prefix": "8806dda121df686a817d56f65ee47d26a4901c2a0eb0eb46eb2f42fcb4a9a85c", "size_in_bytes": 5}, {"_path": "Lib/site-packages/setuptools/_vendor/zipp/__init__.py", "path_type": "hardlink", "sha256": "42e235834d06e1f440706b7e1ea6d5d285889264a079d086198b071d8ccd6bc0", "sha256_in_prefix": "42e235834d06e1f440706b7e1ea6d5d285889264a079d086198b071d8ccd6bc0", "size_in_bytes": 13412}, {"_path": "Lib/site-packages/setuptools/_vendor/zipp/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "b9a5a48016931749f64952b78456da6921484cfca1bff6612e60bf30cc6fd7b0", "sha256_in_prefix": "b9a5a48016931749f64952b78456da6921484cfca1bff6612e60bf30cc6fd7b0", "size_in_bytes": 24496}, {"_path": "Lib/site-packages/setuptools/_vendor/zipp/__pycache__/glob.cpython-311.pyc", "path_type": "hardlink", "sha256": "bd9181de8bdf781d835812a3e35f249e1b524ec7b34c75c57a95ec436b3ee383", "sha256_in_prefix": "bd9181de8bdf781d835812a3e35f249e1b524ec7b34c75c57a95ec436b3ee383", "size_in_bytes": 5613}, {"_path": "Lib/site-packages/setuptools/_vendor/zipp/compat/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_vendor/zipp/compat/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "966b9240156aafc7992db5b7851a35ff99e8900bbb0294425628a6cb639504d4", "sha256_in_prefix": "966b9240156aafc7992db5b7851a35ff99e8900bbb0294425628a6cb639504d4", "size_in_bytes": 169}, {"_path": "Lib/site-packages/setuptools/_vendor/zipp/compat/__pycache__/py310.cpython-311.pyc", "path_type": "hardlink", "sha256": "375dfcbf11a22bb1686a8fa21e0717f972b6b69a544cff771799734d7c77084d", "sha256_in_prefix": "375dfcbf11a22bb1686a8fa21e0717f972b6b69a544cff771799734d7c77084d", "size_in_bytes": 480}, {"_path": "Lib/site-packages/setuptools/_vendor/zipp/compat/py310.py", "path_type": "hardlink", "sha256": "799a645b4cd1b6e9e484487c8e35f780219edb67a6a0a081270ef666de119210", "sha256_in_prefix": "799a645b4cd1b6e9e484487c8e35f780219edb67a6a0a081270ef666de119210", "size_in_bytes": 219}, {"_path": "Lib/site-packages/setuptools/_vendor/zipp/glob.py", "path_type": "hardlink", "sha256": "7ad5a99df1284727d4beb52c8bab13886984aef3f07ba1f363aa53f2383f9542", "sha256_in_prefix": "7ad5a99df1284727d4beb52c8bab13886984aef3f07ba1f363aa53f2383f9542", "size_in_bytes": 3082}, {"_path": "Lib/site-packages/setuptools/archive_util.py", "path_type": "hardlink", "sha256": "4e5ffae21493b5ce32f31ef16bdf2b15551b1b6e2802ba63ccb0181983f6fec2", "sha256_in_prefix": "4e5ffae21493b5ce32f31ef16bdf2b15551b1b6e2802ba63ccb0181983f6fec2", "size_in_bytes": 7356}, {"_path": "Lib/site-packages/setuptools/build_meta.py", "path_type": "hardlink", "sha256": "aebcbe2e8c2abd616cc46e909b94167ad1c919e113cd1762439f9bb386ce923a", "sha256_in_prefix": "aebcbe2e8c2abd616cc46e909b94167ad1c919e113cd1762439f9bb386ce923a", "size_in_bytes": 20446}, {"_path": "Lib/site-packages/setuptools/cli-32.exe", "path_type": "hardlink", "sha256": "32acc1bc543116cbe2cff10cb867772df2f254ff2634c870aef0b46c4b696fdb", "sha256_in_prefix": "32acc1bc543116cbe2cff10cb867772df2f254ff2634c870aef0b46c4b696fdb", "size_in_bytes": 11776}, {"_path": "Lib/site-packages/setuptools/cli-64.exe", "path_type": "hardlink", "sha256": "bbb3de5707629e6a60a0c238cd477b28f07f0066982fda953fa6fcec39073a4a", "sha256_in_prefix": "bbb3de5707629e6a60a0c238cd477b28f07f0066982fda953fa6fcec39073a4a", "size_in_bytes": 14336}, {"_path": "Lib/site-packages/setuptools/cli-arm64.exe", "path_type": "hardlink", "sha256": "b9a7d08da880dfac8bcf548eba4b06fb59b6f09b17d33148a0f6618328926c61", "sha256_in_prefix": "b9a7d08da880dfac8bcf548eba4b06fb59b6f09b17d33148a0f6618328926c61", "size_in_bytes": 13824}, {"_path": "Lib/site-packages/setuptools/cli.exe", "path_type": "hardlink", "sha256": "32acc1bc543116cbe2cff10cb867772df2f254ff2634c870aef0b46c4b696fdb", "sha256_in_prefix": "32acc1bc543116cbe2cff10cb867772df2f254ff2634c870aef0b46c4b696fdb", "size_in_bytes": 11776}, {"_path": "Lib/site-packages/setuptools/command/__init__.py", "path_type": "hardlink", "sha256": "c1d4ab94d4743fa9c2cfdfe816d08088091e14932c65ad633dca574f9ddfd123", "sha256_in_prefix": "c1d4ab94d4743fa9c2cfdfe816d08088091e14932c65ad633dca574f9ddfd123", "size_in_bytes": 803}, {"_path": "Lib/site-packages/setuptools/command/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "d76647f3dafbf30c0f8655eddfdae77cb9af8868e2f05bab8d4dace305aa3eb7", "sha256_in_prefix": "d76647f3dafbf30c0f8655eddfdae77cb9af8868e2f05bab8d4dace305aa3eb7", "size_in_bytes": 603}, {"_path": "Lib/site-packages/setuptools/command/__pycache__/_requirestxt.cpython-311.pyc", "path_type": "hardlink", "sha256": "1e20ba0c1b7991bcc2b18f5909ca81f3feafe6962dd3391336ecdc9a88cd7691", "sha256_in_prefix": "1e20ba0c1b7991bcc2b18f5909ca81f3feafe6962dd3391336ecdc9a88cd7691", "size_in_bytes": 7205}, {"_path": "Lib/site-packages/setuptools/command/__pycache__/alias.cpython-311.pyc", "path_type": "hardlink", "sha256": "475ae88d0e63c6a08c016e1e3c633b9bcd8a4054aa2fd48bf5f50068368fbb0a", "sha256_in_prefix": "475ae88d0e63c6a08c016e1e3c633b9bcd8a4054aa2fd48bf5f50068368fbb0a", "size_in_bytes": 3896}, {"_path": "Lib/site-packages/setuptools/command/__pycache__/bdist_egg.cpython-311.pyc", "path_type": "hardlink", "sha256": "fe4a8d54d04ba58169e41af41d017471d1eb8dda6362620eacffff52fa37c1c4", "sha256_in_prefix": "fe4a8d54d04ba58169e41af41d017471d1eb8dda6362620eacffff52fa37c1c4", "size_in_bytes": 26382}, {"_path": "Lib/site-packages/setuptools/command/__pycache__/bdist_rpm.cpython-311.pyc", "path_type": "hardlink", "sha256": "31195f0c98d85323835661d33ab1bf8ba5ce396ad224ab2e2a58571f73a19ede", "sha256_in_prefix": "31195f0c98d85323835661d33ab1bf8ba5ce396ad224ab2e2a58571f73a19ede", "size_in_bytes": 2409}, {"_path": "Lib/site-packages/setuptools/command/__pycache__/bdist_wheel.cpython-311.pyc", "path_type": "hardlink", "sha256": "80002d65ef12bbaa30fa27ebbca37373b6c3acdc5947ea2ccfd6e9b97f868500", "sha256_in_prefix": "80002d65ef12bbaa30fa27ebbca37373b6c3acdc5947ea2ccfd6e9b97f868500", "size_in_bytes": 28316}, {"_path": "Lib/site-packages/setuptools/command/__pycache__/build.cpython-311.pyc", "path_type": "hardlink", "sha256": "130fbb4caeb074647b54b2f0df6701b795b97ddc5c90f1b15a8ddd9f5f786a08", "sha256_in_prefix": "130fbb4caeb074647b54b2f0df6701b795b97ddc5c90f1b15a8ddd9f5f786a08", "size_in_bytes": 5744}, {"_path": "Lib/site-packages/setuptools/command/__pycache__/build_clib.cpython-311.pyc", "path_type": "hardlink", "sha256": "9abbba1f3a668b4abc364312cff65bbbe15a3d90c55a403cb1989c5f3f086a90", "sha256_in_prefix": "9abbba1f3a668b4abc364312cff65bbbe15a3d90c55a403cb1989c5f3f086a90", "size_in_bytes": 4269}, {"_path": "Lib/site-packages/setuptools/command/__pycache__/build_ext.cpython-311.pyc", "path_type": "hardlink", "sha256": "f6aa350d8aabd07cfdced5af69b7ec228d8bb44f6c06c4e57d022022436176dd", "sha256_in_prefix": "f6aa350d8aabd07cfdced5af69b7ec228d8bb44f6c06c4e57d022022436176dd", "size_in_bytes": 25016}, {"_path": "Lib/site-packages/setuptools/command/__pycache__/build_py.cpython-311.pyc", "path_type": "hardlink", "sha256": "999282a47d1c3e7ff6ede0cf99724a00bded127cfffcab5bbded3ced563b5930", "sha256_in_prefix": "999282a47d1c3e7ff6ede0cf99724a00bded127cfffcab5bbded3ced563b5930", "size_in_bytes": 23896}, {"_path": "Lib/site-packages/setuptools/command/__pycache__/develop.cpython-311.pyc", "path_type": "hardlink", "sha256": "483df10852d651012fd1c9d09c34f0fcb8bcdbff79d8fc9ef31a5650e5afe115", "sha256_in_prefix": "483df10852d651012fd1c9d09c34f0fcb8bcdbff79d8fc9ef31a5650e5afe115", "size_in_bytes": 10642}, {"_path": "Lib/site-packages/setuptools/command/__pycache__/dist_info.cpython-311.pyc", "path_type": "hardlink", "sha256": "e4bc07a5b5f91cef7a15512f8c3dee22491c2afec0e1d4cb07f5c406c8bde0c7", "sha256_in_prefix": "e4bc07a5b5f91cef7a15512f8c3dee22491c2afec0e1d4cb07f5c406c8bde0c7", "size_in_bytes": 5566}, {"_path": "Lib/site-packages/setuptools/command/__pycache__/easy_install.cpython-311.pyc", "path_type": "hardlink", "sha256": "d2eb6bbee3c17e3711fa78edcbf5c9a9c7f676a9cdda2c98b57a16459c60e728", "sha256_in_prefix": "d2eb6bbee3c17e3711fa78edcbf5c9a9c7f676a9cdda2c98b57a16459c60e728", "size_in_bytes": 120853}, {"_path": "Lib/site-packages/setuptools/command/__pycache__/editable_wheel.cpython-311.pyc", "path_type": "hardlink", "sha256": "a6c53f662c45117ba91a3b7d542111d1c8c71a6a1ae551e7d2859d355bf9d9e4", "sha256_in_prefix": "a6c53f662c45117ba91a3b7d542111d1c8c71a6a1ae551e7d2859d355bf9d9e4", "size_in_bytes": 54902}, {"_path": "Lib/site-packages/setuptools/command/__pycache__/egg_info.cpython-311.pyc", "path_type": "hardlink", "sha256": "c047afe825707ed4bf3e3550aefbd19f3fc9be5235a8cd561cf0137ed55d1649", "sha256_in_prefix": "c047afe825707ed4bf3e3550aefbd19f3fc9be5235a8cd561cf0137ed55d1649", "size_in_bytes": 37474}, {"_path": "Lib/site-packages/setuptools/command/__pycache__/install.cpython-311.pyc", "path_type": "hardlink", "sha256": "136e191f3d9313d4d68b4a5ae829e5b3ad8135dae0c9e9e9cbdc9b648ee5b5a8", "sha256_in_prefix": "136e191f3d9313d4d68b4a5ae829e5b3ad8135dae0c9e9e9cbdc9b648ee5b5a8", "size_in_bytes": 8427}, {"_path": "Lib/site-packages/setuptools/command/__pycache__/install_egg_info.cpython-311.pyc", "path_type": "hardlink", "sha256": "cadda7ae3d411dfa04bfcf63958e5d08b1c59099732a508faa7e6343f3c8c8c4", "sha256_in_prefix": "cadda7ae3d411dfa04bfcf63958e5d08b1c59099732a508faa7e6343f3c8c8c4", "size_in_bytes": 3948}, {"_path": "Lib/site-packages/setuptools/command/__pycache__/install_lib.cpython-311.pyc", "path_type": "hardlink", "sha256": "82be874549b00794d62aefd89bd6b511fc1da365e54f0483f62d41d2d270148d", "sha256_in_prefix": "82be874549b00794d62aefd89bd6b511fc1da365e54f0483f62d41d2d270148d", "size_in_bytes": 6830}, {"_path": "Lib/site-packages/setuptools/command/__pycache__/install_scripts.cpython-311.pyc", "path_type": "hardlink", "sha256": "b2e4fb440b485ff99c51048d10113454820c6cac273c89b45599bac493f4a452", "sha256_in_prefix": "b2e4fb440b485ff99c51048d10113454820c6cac273c89b45599bac493f4a452", "size_in_bytes": 4394}, {"_path": "Lib/site-packages/setuptools/command/__pycache__/rotate.cpython-311.pyc", "path_type": "hardlink", "sha256": "ca95e60a463dbc043d8b563a8c5a60eee1f6559bbc73d23528aab053e636629d", "sha256_in_prefix": "ca95e60a463dbc043d8b563a8c5a60eee1f6559bbc73d23528aab053e636629d", "size_in_bytes": 4345}, {"_path": "Lib/site-packages/setuptools/command/__pycache__/saveopts.cpython-311.pyc", "path_type": "hardlink", "sha256": "f82c2b06d9ed20b197829bd86d14dc71a658bd042a3465b01fbe8484600be54b", "sha256_in_prefix": "f82c2b06d9ed20b197829bd86d14dc71a658bd042a3465b01fbe8484600be54b", "size_in_bytes": 1352}, {"_path": "Lib/site-packages/setuptools/command/__pycache__/sdist.cpython-311.pyc", "path_type": "hardlink", "sha256": "44ebf944c4d6da1f7e7d03100f380a8c561a307dfe12508fc52397e9b6ab6c74", "sha256_in_prefix": "44ebf944c4d6da1f7e7d03100f380a8c561a307dfe12508fc52397e9b6ab6c74", "size_in_bytes": 13639}, {"_path": "Lib/site-packages/setuptools/command/__pycache__/setopt.cpython-311.pyc", "path_type": "hardlink", "sha256": "1b2cb68d698499d11fec60c79647881c47312e95c2c95ce09bbebb0b9cca20f6", "sha256_in_prefix": "1b2cb68d698499d11fec60c79647881c47312e95c2c95ce09bbebb0b9cca20f6", "size_in_bytes": 7758}, {"_path": "Lib/site-packages/setuptools/command/__pycache__/test.cpython-311.pyc", "path_type": "hardlink", "sha256": "935351e762a6a87fb23d30673058b953e6151511a4b72be17e7c0a71d490202c", "sha256_in_prefix": "935351e762a6a87fb23d30673058b953e6151511a4b72be17e7c0a71d490202c", "size_in_bytes": 2095}, {"_path": "Lib/site-packages/setuptools/command/_requirestxt.py", "path_type": "hardlink", "sha256": "22d60c4c91a1fe2e53950b2d5ff9c5a29a848640b83c915a7412f665ddd5ebbd", "sha256_in_prefix": "22d60c4c91a1fe2e53950b2d5ff9c5a29a848640b83c915a7412f665ddd5ebbd", "size_in_bytes": 4228}, {"_path": "Lib/site-packages/setuptools/command/alias.py", "path_type": "hardlink", "sha256": "ac376b32ddf60d2eaa7f72bbb63659c870ff74c2ab9bbec05dc02dc7e9c14342", "sha256_in_prefix": "ac376b32ddf60d2eaa7f72bbb63659c870ff74c2ab9bbec05dc02dc7e9c14342", "size_in_bytes": 2380}, {"_path": "Lib/site-packages/setuptools/command/bdist_egg.py", "path_type": "hardlink", "sha256": "dde0ee710e1f75e60cb0b3bd3e105f63526470c2e1657827008ffd15d14db041", "sha256_in_prefix": "dde0ee710e1f75e60cb0b3bd3e105f63526470c2e1657827008ffd15d14db041", "size_in_bytes": 16972}, {"_path": "Lib/site-packages/setuptools/command/bdist_rpm.py", "path_type": "hardlink", "sha256": "2f2a88e3dc38f122a4d059ae1ec13d30bcd7d52b978cbed830d6d930566a1482", "sha256_in_prefix": "2f2a88e3dc38f122a4d059ae1ec13d30bcd7d52b978cbed830d6d930566a1482", "size_in_bytes": 1435}, {"_path": "Lib/site-packages/setuptools/command/bdist_wheel.py", "path_type": "hardlink", "sha256": "fcb7c61c1ec257fbb29dcaa53934844c48b6823542a0f2ae017732445a2aec2b", "sha256_in_prefix": "fcb7c61c1ec257fbb29dcaa53934844c48b6823542a0f2ae017732445a2aec2b", "size_in_bytes": 22246}, {"_path": "Lib/site-packages/setuptools/command/build.py", "path_type": "hardlink", "sha256": "788ed24cc111186644a73935b6f24df29f483a30005cc7062f3963bf69b02373", "sha256_in_prefix": "788ed24cc111186644a73935b6f24df29f483a30005cc7062f3963bf69b02373", "size_in_bytes": 6052}, {"_path": "Lib/site-packages/setuptools/command/build_clib.py", "path_type": "hardlink", "sha256": "01b8293c817fdea2fc7d9af724879b23e5874cc4c188c7eb164550cfc2b8d06e", "sha256_in_prefix": "01b8293c817fdea2fc7d9af724879b23e5874cc4c188c7eb164550cfc2b8d06e", "size_in_bytes": 4528}, {"_path": "Lib/site-packages/setuptools/command/build_ext.py", "path_type": "hardlink", "sha256": "6d41f8334362cda249aefd74c0af990f7b98d13c42499958403862c30cc7b253", "sha256_in_prefix": "6d41f8334362cda249aefd74c0af990f7b98d13c42499958403862c30cc7b253", "size_in_bytes": 18377}, {"_path": "Lib/site-packages/setuptools/command/build_py.py", "path_type": "hardlink", "sha256": "0c26e3bc1d7c9242fec542b9aef9739b40bab704de3b1361caf243c716bb7c82", "sha256_in_prefix": "0c26e3bc1d7c9242fec542b9aef9739b40bab704de3b1361caf243c716bb7c82", "size_in_bytes": 15539}, {"_path": "Lib/site-packages/setuptools/command/develop.py", "path_type": "hardlink", "sha256": "cd7db6d75f6c2351b581f27580d084e21920db36cb2b1d2e530bcd982e5b22ef", "sha256_in_prefix": "cd7db6d75f6c2351b581f27580d084e21920db36cb2b1d2e530bcd982e5b22ef", "size_in_bytes": 6886}, {"_path": "Lib/site-packages/setuptools/command/dist_info.py", "path_type": "hardlink", "sha256": "1d4ef9da22cb9a660c1dbb03060cf6b9b4639202686ee80ea7c1fbd4bcf30f2b", "sha256_in_prefix": "1d4ef9da22cb9a660c1dbb03060cf6b9b4639202686ee80ea7c1fbd4bcf30f2b", "size_in_bytes": 3450}, {"_path": "Lib/site-packages/setuptools/command/easy_install.py", "path_type": "hardlink", "sha256": "d19e2416513bf007b601f1d7613c591546b6b77aa536a5c2b50bb8275371f220", "sha256_in_prefix": "d19e2416513bf007b601f1d7613c591546b6b77aa536a5c2b50bb8275371f220", "size_in_bytes": 87870}, {"_path": "Lib/site-packages/setuptools/command/editable_wheel.py", "path_type": "hardlink", "sha256": "ddb062a51640dc4e29a10f0b11684c6048c78c2cea53fa4874ef3a0b7b7ba0d6", "sha256_in_prefix": "ddb062a51640dc4e29a10f0b11684c6048c78c2cea53fa4874ef3a0b7b7ba0d6", "size_in_bytes": 35624}, {"_path": "Lib/site-packages/setuptools/command/egg_info.py", "path_type": "hardlink", "sha256": "596528cd1dc3642ad6b134211d73b280c88451cae32d6a61113d3e66ca1cb26e", "sha256_in_prefix": "596528cd1dc3642ad6b134211d73b280c88451cae32d6a61113d3e66ca1cb26e", "size_in_bytes": 25982}, {"_path": "Lib/site-packages/setuptools/command/install.py", "path_type": "hardlink", "sha256": "3264c66fc9b547c7c9d1c73915358217abaafacd59266be9626f8dfc2b6a11a2", "sha256_in_prefix": "3264c66fc9b547c7c9d1c73915358217abaafacd59266be9626f8dfc2b6a11a2", "size_in_bytes": 7046}, {"_path": "Lib/site-packages/setuptools/command/install_egg_info.py", "path_type": "hardlink", "sha256": "dc8f483c21fb0f9f5287ee9a558cfe87ac30cb1abec24c6b2b02a0de70dd26ab", "sha256_in_prefix": "dc8f483c21fb0f9f5287ee9a558cfe87ac30cb1abec24c6b2b02a0de70dd26ab", "size_in_bytes": 2075}, {"_path": "Lib/site-packages/setuptools/command/install_lib.py", "path_type": "hardlink", "sha256": "f67d7f53cdde1dc1112ff6bfaeffcb8470a485794b76ac99e12741a30fbda9c1", "sha256_in_prefix": "f67d7f53cdde1dc1112ff6bfaeffcb8470a485794b76ac99e12741a30fbda9c1", "size_in_bytes": 4319}, {"_path": "Lib/site-packages/setuptools/command/install_scripts.py", "path_type": "hardlink", "sha256": "b553828f77bc39322b9282ff6c66d3e693a4b1dc597d06e51ff6dd2380ed555e", "sha256_in_prefix": "b553828f77bc39322b9282ff6c66d3e693a4b1dc597d06e51ff6dd2380ed555e", "size_in_bytes": 2637}, {"_path": "Lib/site-packages/setuptools/command/launcher manifest.xml", "path_type": "hardlink", "sha256": "c652db8d6ac1d35b4a0b4fa195590e2a48923dbccc9a5d9e38fb49fee7029db1", "sha256_in_prefix": "c652db8d6ac1d35b4a0b4fa195590e2a48923dbccc9a5d9e38fb49fee7029db1", "size_in_bytes": 628}, {"_path": "Lib/site-packages/setuptools/command/rotate.py", "path_type": "hardlink", "sha256": "5cd77f04410e5802475b515c2d3314596978401eb302e93b6fc556420dc51e8b", "sha256_in_prefix": "5cd77f04410e5802475b515c2d3314596978401eb302e93b6fc556420dc51e8b", "size_in_bytes": 2187}, {"_path": "Lib/site-packages/setuptools/command/saveopts.py", "path_type": "hardlink", "sha256": "369d0f55bed20fba136eef59f6ca2c4bb0fe0a4908914ef1e2096ee44b35b630", "sha256_in_prefix": "369d0f55bed20fba136eef59f6ca2c4bb0fe0a4908914ef1e2096ee44b35b630", "size_in_bytes": 692}, {"_path": "Lib/site-packages/setuptools/command/sdist.py", "path_type": "hardlink", "sha256": "25a426dbe79b5c8da4bf2ac18c928ff3234b3dae5e31b31e8acf3c09704c6259", "sha256_in_prefix": "25a426dbe79b5c8da4bf2ac18c928ff3234b3dae5e31b31e8acf3c09704c6259", "size_in_bytes": 7374}, {"_path": "Lib/site-packages/setuptools/command/setopt.py", "path_type": "hardlink", "sha256": "c59176442738001bc4f5e1c7033179d3e7e4420ddabbc7dc45455519de7c9375", "sha256_in_prefix": "c59176442738001bc4f5e1c7033179d3e7e4420ddabbc7dc45455519de7c9375", "size_in_bytes": 5100}, {"_path": "Lib/site-packages/setuptools/command/test.py", "path_type": "hardlink", "sha256": "93bc5cabb0fb6c47a18316ab6f0f9d5b702d98664e46acfc1e3291e85189de39", "sha256_in_prefix": "93bc5cabb0fb6c47a18316ab6f0f9d5b702d98664e46acfc1e3291e85189de39", "size_in_bytes": 1343}, {"_path": "Lib/site-packages/setuptools/compat/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/compat/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "f8a05949b86c0b61f09af659feca4e8740d6a8eead52e77bd56066e39a576e0e", "sha256_in_prefix": "f8a05949b86c0b61f09af659feca4e8740d6a8eead52e77bd56066e39a576e0e", "size_in_bytes": 156}, {"_path": "Lib/site-packages/setuptools/compat/__pycache__/py310.cpython-311.pyc", "path_type": "hardlink", "sha256": "f22b7300bfb06d9d59f6bcf4bb679a020d86079eede7bca4bb44c1007745cd3d", "sha256_in_prefix": "f22b7300bfb06d9d59f6bcf4bb679a020d86079eede7bca4bb44c1007745cd3d", "size_in_bytes": 325}, {"_path": "Lib/site-packages/setuptools/compat/__pycache__/py311.cpython-311.pyc", "path_type": "hardlink", "sha256": "5e8c2eb9508a0b78e5daaaafbab090a01bb827c9d8583f2cb8977b68c9d1d163", "sha256_in_prefix": "5e8c2eb9508a0b78e5daaaafbab090a01bb827c9d8583f2cb8977b68c9d1d163", "size_in_bytes": 1629}, {"_path": "Lib/site-packages/setuptools/compat/__pycache__/py312.cpython-311.pyc", "path_type": "hardlink", "sha256": "a426ce2d60f515cb1e12e8482702c70d83d0af589741a16e3ae136ea2f53a444", "sha256_in_prefix": "a426ce2d60f515cb1e12e8482702c70d83d0af589741a16e3ae136ea2f53a444", "size_in_bytes": 479}, {"_path": "Lib/site-packages/setuptools/compat/__pycache__/py39.cpython-311.pyc", "path_type": "hardlink", "sha256": "9bb79396facb0379305ebeb0c61b06f25b20f351fa6f2a3901d12f4a1525438f", "sha256_in_prefix": "9bb79396facb0379305ebeb0c61b06f25b20f351fa6f2a3901d12f4a1525438f", "size_in_bytes": 275}, {"_path": "Lib/site-packages/setuptools/compat/py310.py", "path_type": "hardlink", "sha256": "f2cab059ccc872b9337806e16a29b8a4a55de2d5d975caa679b81dbf38e2d2b7", "sha256_in_prefix": "f2cab059ccc872b9337806e16a29b8a4a55de2d5d975caa679b81dbf38e2d2b7", "size_in_bytes": 141}, {"_path": "Lib/site-packages/setuptools/compat/py311.py", "path_type": "hardlink", "sha256": "7bab49005c1910ff36866301975d0761e4b2a5e968fd38b6c138ca65528bc0e1", "sha256_in_prefix": "7bab49005c1910ff36866301975d0761e4b2a5e968fd38b6c138ca65528bc0e1", "size_in_bytes": 790}, {"_path": "Lib/site-packages/setuptools/compat/py312.py", "path_type": "hardlink", "sha256": "bd8295b5dadd393b0efd1f747499045ec1707cc245b881497e5848807ae327e6", "sha256_in_prefix": "bd8295b5dadd393b0efd1f747499045ec1707cc245b881497e5848807ae327e6", "size_in_bytes": 366}, {"_path": "Lib/site-packages/setuptools/compat/py39.py", "path_type": "hardlink", "sha256": "04932d9e47dcab24df71caa3610c5fa11b54da74e759a104481564b214e25ea6", "sha256_in_prefix": "04932d9e47dcab24df71caa3610c5fa11b54da74e759a104481564b214e25ea6", "size_in_bytes": 493}, {"_path": "Lib/site-packages/setuptools/config/NOTICE", "path_type": "hardlink", "sha256": "2dddf08818297a3b89d43d95ff659d8da85741108c9136dfa3a4d856c0623bd8", "sha256_in_prefix": "2dddf08818297a3b89d43d95ff659d8da85741108c9136dfa3a4d856c0623bd8", "size_in_bytes": 493}, {"_path": "Lib/site-packages/setuptools/config/__init__.py", "path_type": "hardlink", "sha256": "6a23e72fd0499f53ba31f9ae357ca7f16d8ba7cbbdaa2cd156ac0f88e74f2236", "sha256_in_prefix": "6a23e72fd0499f53ba31f9ae357ca7f16d8ba7cbbdaa2cd156ac0f88e74f2236", "size_in_bytes": 1499}, {"_path": "Lib/site-packages/setuptools/config/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "31a021fb939efdd64e6ee6114e24e240a94dc43336983ca0e5db61031acf34e3", "sha256_in_prefix": "31a021fb939efdd64e6ee6114e24e240a94dc43336983ca0e5db61031acf34e3", "size_in_bytes": 2126}, {"_path": "Lib/site-packages/setuptools/config/__pycache__/_apply_pyprojecttoml.cpython-311.pyc", "path_type": "hardlink", "sha256": "54904dc80598db50e59770124f64aa5fb0abc662df5d98e08985a1f4c2902e11", "sha256_in_prefix": "54904dc80598db50e59770124f64aa5fb0abc662df5d98e08985a1f4c2902e11", "size_in_bytes": 29444}, {"_path": "Lib/site-packages/setuptools/config/__pycache__/expand.cpython-311.pyc", "path_type": "hardlink", "sha256": "f52e08c2cfd25d8460f6d7ffcbda29fa8fbf91f6cd24a55711c8a74bc68b8786", "sha256_in_prefix": "f52e08c2cfd25d8460f6d7ffcbda29fa8fbf91f6cd24a55711c8a74bc68b8786", "size_in_bytes": 27351}, {"_path": "Lib/site-packages/setuptools/config/__pycache__/pyprojecttoml.cpython-311.pyc", "path_type": "hardlink", "sha256": "18b99d14206b6a93aea3b613169a52dc568d227cc25ab12de1defb0bfee9ad3f", "sha256_in_prefix": "18b99d14206b6a93aea3b613169a52dc568d227cc25ab12de1defb0bfee9ad3f", "size_in_bytes": 26470}, {"_path": "Lib/site-packages/setuptools/config/__pycache__/setupcfg.cpython-311.pyc", "path_type": "hardlink", "sha256": "b2bcb0a473d378e6fd9efaebf6197061a2bc14cf3ca9a6768eb68c06473f6986", "sha256_in_prefix": "b2bcb0a473d378e6fd9efaebf6197061a2bc14cf3ca9a6768eb68c06473f6986", "size_in_bytes": 36135}, {"_path": "Lib/site-packages/setuptools/config/_apply_pyprojecttoml.py", "path_type": "hardlink", "sha256": "494c93c3b0366ed675941b9628de68e36f838b2bfde5e193898277ad93a71927", "sha256_in_prefix": "494c93c3b0366ed675941b9628de68e36f838b2bfde5e193898277ad93a71927", "size_in_bytes": 19120}, {"_path": "Lib/site-packages/setuptools/config/_validate_pyproject/NOTICE", "path_type": "hardlink", "sha256": "5d300dbfa643138b013b75ac9caeee591f951b8b0ee24288c34ccd926c4780c8", "sha256_in_prefix": "5d300dbfa643138b013b75ac9caeee591f951b8b0ee24288c34ccd926c4780c8", "size_in_bytes": 18737}, {"_path": "Lib/site-packages/setuptools/config/_validate_pyproject/__init__.py", "path_type": "hardlink", "sha256": "767a7a4fb78f3f5479cf83ae0bb15dd9d905948aed21f8b351fbe91893fa9f3d", "sha256_in_prefix": "767a7a4fb78f3f5479cf83ae0bb15dd9d905948aed21f8b351fbe91893fa9f3d", "size_in_bytes": 1042}, {"_path": "Lib/site-packages/setuptools/config/_validate_pyproject/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "965f8ac568af59df6790ef7245e736d99c49528f629ce00a74cb1ff49aa1d1c3", "sha256_in_prefix": "965f8ac568af59df6790ef7245e736d99c49528f629ce00a74cb1ff49aa1d1c3", "size_in_bytes": 2305}, {"_path": "Lib/site-packages/setuptools/config/_validate_pyproject/__pycache__/error_reporting.cpython-311.pyc", "path_type": "hardlink", "sha256": "c7c6134b494a66f52a08ba42697260f3cee1f83761e74091caf890a96e38febe", "sha256_in_prefix": "c7c6134b494a66f52a08ba42697260f3cee1f83761e74091caf890a96e38febe", "size_in_bytes": 20889}, {"_path": "Lib/site-packages/setuptools/config/_validate_pyproject/__pycache__/extra_validations.cpython-311.pyc", "path_type": "hardlink", "sha256": "21eb22cba46aacbfbffb0468e68530992526e019636b9d94ccb15059015d0068", "sha256_in_prefix": "21eb22cba46aacbfbffb0468e68530992526e019636b9d94ccb15059015d0068", "size_in_bytes": 3426}, {"_path": "Lib/site-packages/setuptools/config/_validate_pyproject/__pycache__/fastjsonschema_exceptions.cpython-311.pyc", "path_type": "hardlink", "sha256": "2bc159b9f877ba9e006c813e17c81b05d6845dd952bdeb3132ddb4fdac9973b0", "sha256_in_prefix": "2bc159b9f877ba9e006c813e17c81b05d6845dd952bdeb3132ddb4fdac9973b0", "size_in_bytes": 3205}, {"_path": "Lib/site-packages/setuptools/config/_validate_pyproject/__pycache__/fastjsonschema_validations.cpython-311.pyc", "path_type": "hardlink", "sha256": "1f401025cddca4ea1bb725685d265a2adf959d7782c918e3c6f4b4d48eec291e", "sha256_in_prefix": "1f401025cddca4ea1bb725685d265a2adf959d7782c918e3c6f4b4d48eec291e", "size_in_bytes": 265720}, {"_path": "Lib/site-packages/setuptools/config/_validate_pyproject/__pycache__/formats.cpython-311.pyc", "path_type": "hardlink", "sha256": "9627526de1a580ef2cff2c585a2456943b3a6b42cd4a31502955a6eeec1fbc0a", "sha256_in_prefix": "9627526de1a580ef2cff2c585a2456943b3a6b42cd4a31502955a6eeec1fbc0a", "size_in_bytes": 20389}, {"_path": "Lib/site-packages/setuptools/config/_validate_pyproject/error_reporting.py", "path_type": "hardlink", "sha256": "99e95d0fb9c141da25421bc6fb8debd547be814d67ece440251f3abe1dd1aef9", "sha256_in_prefix": "99e95d0fb9c141da25421bc6fb8debd547be814d67ece440251f3abe1dd1aef9", "size_in_bytes": 11813}, {"_path": "Lib/site-packages/setuptools/config/_validate_pyproject/extra_validations.py", "path_type": "hardlink", "sha256": "f86506e52fbe8a363c59f5db7573e81e5eb2c06b32105f5db43a5e9d2a093c78", "sha256_in_prefix": "f86506e52fbe8a363c59f5db7573e81e5eb2c06b32105f5db43a5e9d2a093c78", "size_in_bytes": 2858}, {"_path": "Lib/site-packages/setuptools/config/_validate_pyproject/fastjsonschema_exceptions.py", "path_type": "hardlink", "sha256": "c3be3d260a8a8bc72504570e6dd71b655aac985e2827f401ca16754866d414dc", "sha256_in_prefix": "c3be3d260a8a8bc72504570e6dd71b655aac985e2827f401ca16754866d414dc", "size_in_bytes": 1612}, {"_path": "Lib/site-packages/setuptools/config/_validate_pyproject/fastjsonschema_validations.py", "path_type": "hardlink", "sha256": "162843e5970cea9efb04f674e021aa877044c153683cc289649032b89a64014d", "sha256_in_prefix": "162843e5970cea9efb04f674e021aa877044c153683cc289649032b89a64014d", "size_in_bytes": 354682}, {"_path": "Lib/site-packages/setuptools/config/_validate_pyproject/formats.py", "path_type": "hardlink", "sha256": "4c44e890904af618e5f9c560d6896ca23909c0bc5f3fbfdc860250366cc007df", "sha256_in_prefix": "4c44e890904af618e5f9c560d6896ca23909c0bc5f3fbfdc860250366cc007df", "size_in_bytes": 13564}, {"_path": "Lib/site-packages/setuptools/config/distutils.schema.json", "path_type": "hardlink", "sha256": "4dca77da44678703911b0ffda7a1848b4f258f6875e6d411cce6016f31a67015", "sha256_in_prefix": "4dca77da44678703911b0ffda7a1848b4f258f6875e6d411cce6016f31a67015", "size_in_bytes": 972}, {"_path": "Lib/site-packages/setuptools/config/expand.py", "path_type": "hardlink", "sha256": "24d024b510accb2441fab42875b3e70ae7262d6a9c62fcc20c2f046e7d99ef13", "sha256_in_prefix": "24d024b510accb2441fab42875b3e70ae7262d6a9c62fcc20c2f046e7d99ef13", "size_in_bytes": 16041}, {"_path": "Lib/site-packages/setuptools/config/pyprojecttoml.py", "path_type": "hardlink", "sha256": "60cbb93dd6c9248e5ace9ea447f6e833599f95fe67a8e03e227178b3a2e72e0c", "sha256_in_prefix": "60cbb93dd6c9248e5ace9ea447f6e833599f95fe67a8e03e227178b3a2e72e0c", "size_in_bytes": 18320}, {"_path": "Lib/site-packages/setuptools/config/setupcfg.py", "path_type": "hardlink", "sha256": "5590e4c04ec362fe3949b69d243f02c0aac3b625ef8e09652fc3d84afa110b28", "sha256_in_prefix": "5590e4c04ec362fe3949b69d243f02c0aac3b625ef8e09652fc3d84afa110b28", "size_in_bytes": 26575}, {"_path": "Lib/site-packages/setuptools/config/setuptools.schema.json", "path_type": "hardlink", "sha256": "759051b921276646ada1596dd645701bca1c4de45d3bb043d31bce58a1f9e0f6", "sha256_in_prefix": "759051b921276646ada1596dd645701bca1c4de45d3bb043d31bce58a1f9e0f6", "size_in_bytes": 16071}, {"_path": "Lib/site-packages/setuptools/depends.py", "path_type": "hardlink", "sha256": "8ca61f8e6b7fd9941856085bf0bf5b53b2c9e9eac7279cdef8afdf295d413179", "sha256_in_prefix": "8ca61f8e6b7fd9941856085bf0bf5b53b2c9e9eac7279cdef8afdf295d413179", "size_in_bytes": 5965}, {"_path": "Lib/site-packages/setuptools/discovery.py", "path_type": "hardlink", "sha256": "fb8d9cdd7870ce47e874328a3f9d02d98073af5d5f9dc020994cc174145bd3e4", "sha256_in_prefix": "fb8d9cdd7870ce47e874328a3f9d02d98073af5d5f9dc020994cc174145bd3e4", "size_in_bytes": 21258}, {"_path": "Lib/site-packages/setuptools/dist.py", "path_type": "hardlink", "sha256": "459cfb6a3f51c6a498ae2aa016864ebbeeca215f3672695a305c7da3066b0294", "sha256_in_prefix": "459cfb6a3f51c6a498ae2aa016864ebbeeca215f3672695a305c7da3066b0294", "size_in_bytes": 44897}, {"_path": "Lib/site-packages/setuptools/errors.py", "path_type": "hardlink", "sha256": "818db1d8f21a220cb4d724403510becdc0b0c430aa09272026808e6457b4ca2a", "sha256_in_prefix": "818db1d8f21a220cb4d724403510becdc0b0c430aa09272026808e6457b4ca2a", "size_in_bytes": 3024}, {"_path": "Lib/site-packages/setuptools/extension.py", "path_type": "hardlink", "sha256": "2829eff69ded826d1956ab60138e757f220bb26e210b2bce894b4ebbbf3b0fee", "sha256_in_prefix": "2829eff69ded826d1956ab60138e757f220bb26e210b2bce894b4ebbbf3b0fee", "size_in_bytes": 6683}, {"_path": "Lib/site-packages/setuptools/glob.py", "path_type": "hardlink", "sha256": "002fc1df70d8f20f821c42f10ec26bb7016ba62b9c48066c6a43c5752390ce17", "sha256_in_prefix": "002fc1df70d8f20f821c42f10ec26bb7016ba62b9c48066c6a43c5752390ce17", "size_in_bytes": 6062}, {"_path": "Lib/site-packages/setuptools/gui-32.exe", "path_type": "hardlink", "sha256": "85dae1e95d77845f2cb59bcac3d4afe74bbe4c91a9bcc5bf4a71cd43104dbe7c", "sha256_in_prefix": "85dae1e95d77845f2cb59bcac3d4afe74bbe4c91a9bcc5bf4a71cd43104dbe7c", "size_in_bytes": 11776}, {"_path": "Lib/site-packages/setuptools/gui-64.exe", "path_type": "hardlink", "sha256": "3471b6140eadc6412277dbbefe3fef8c345a0f1a59776086b80a3618c3a83e3b", "sha256_in_prefix": "3471b6140eadc6412277dbbefe3fef8c345a0f1a59776086b80a3618c3a83e3b", "size_in_bytes": 14336}, {"_path": "Lib/site-packages/setuptools/gui-arm64.exe", "path_type": "hardlink", "sha256": "e694f4743405c8b5926ff457db6fe7f1a12dec7c16a9c3864784d3f4e07ae097", "sha256_in_prefix": "e694f4743405c8b5926ff457db6fe7f1a12dec7c16a9c3864784d3f4e07ae097", "size_in_bytes": 13824}, {"_path": "Lib/site-packages/setuptools/gui.exe", "path_type": "hardlink", "sha256": "85dae1e95d77845f2cb59bcac3d4afe74bbe4c91a9bcc5bf4a71cd43104dbe7c", "sha256_in_prefix": "85dae1e95d77845f2cb59bcac3d4afe74bbe4c91a9bcc5bf4a71cd43104dbe7c", "size_in_bytes": 11776}, {"_path": "Lib/site-packages/setuptools/installer.py", "path_type": "hardlink", "sha256": "ff859e831e2bdcbd39b0ca37f8896a169f8ebb19d6c81aa3c8c67b2d64179a1f", "sha256_in_prefix": "ff859e831e2bdcbd39b0ca37f8896a169f8ebb19d6c81aa3c8c67b2d64179a1f", "size_in_bytes": 5110}, {"_path": "Lib/site-packages/setuptools/launch.py", "path_type": "hardlink", "sha256": "2016f9944bfaf42cae67d7b022b98a957875e7891d2e63f6f4b29f4cc9318a61", "sha256_in_prefix": "2016f9944bfaf42cae67d7b022b98a957875e7891d2e63f6f4b29f4cc9318a61", "size_in_bytes": 820}, {"_path": "Lib/site-packages/setuptools/logging.py", "path_type": "hardlink", "sha256": "5b5ea21c9d477025d8434471cab11f27cdc54f8d7be6d0ada1883e13ab92a552", "sha256_in_prefix": "5b5ea21c9d477025d8434471cab11f27cdc54f8d7be6d0ada1883e13ab92a552", "size_in_bytes": 1261}, {"_path": "Lib/site-packages/setuptools/modified.py", "path_type": "hardlink", "sha256": "6706df05f0853fcf25b6f6effdd243cfeb752ec4239ccf895298199e74198e33", "sha256_in_prefix": "6706df05f0853fcf25b6f6effdd243cfeb752ec4239ccf895298199e74198e33", "size_in_bytes": 568}, {"_path": "Lib/site-packages/setuptools/monkey.py", "path_type": "hardlink", "sha256": "1703169769f5bf66c76dea81cbea3d83cc9435a0246056eccc26d77bd77965af", "sha256_in_prefix": "1703169769f5bf66c76dea81cbea3d83cc9435a0246056eccc26d77bd77965af", "size_in_bytes": 3717}, {"_path": "Lib/site-packages/setuptools/msvc.py", "path_type": "hardlink", "sha256": "be6334a8be2b233aed0fda626bd644c2da99e0b6dbae02f4754d0400d558466f", "sha256_in_prefix": "be6334a8be2b233aed0fda626bd644c2da99e0b6dbae02f4754d0400d558466f", "size_in_bytes": 41631}, {"_path": "Lib/site-packages/setuptools/namespaces.py", "path_type": "hardlink", "sha256": "d861aa618d4134312132d05cd6b1d3bfb92582635545d92c25e5be2f57fefb2b", "sha256_in_prefix": "d861aa618d4134312132d05cd6b1d3bfb92582635545d92c25e5be2f57fefb2b", "size_in_bytes": 3171}, {"_path": "Lib/site-packages/setuptools/package_index.py", "path_type": "hardlink", "sha256": "229e1037982820092350ae941e0d34e6ea70c55f1ad948ed1045a3b0ff3174e9", "sha256_in_prefix": "229e1037982820092350ae941e0d34e6ea70c55f1ad948ed1045a3b0ff3174e9", "size_in_bytes": 40519}, {"_path": "Lib/site-packages/setuptools/sandbox.py", "path_type": "hardlink", "sha256": "7ccaad70eba2a473ba44a3e1d58079a3b77df3974b2a8efa5a1a77beb21e8b61", "sha256_in_prefix": "7ccaad70eba2a473ba44a3e1d58079a3b77df3974b2a8efa5a1a77beb21e8b61", "size_in_bytes": 14906}, {"_path": "Lib/site-packages/setuptools/script (dev).tmpl", "path_type": "hardlink", "sha256": "454cd0cc2414697b7074bb581d661b21098e6844b906baaad45bd403fb6efb92", "sha256_in_prefix": "454cd0cc2414697b7074bb581d661b21098e6844b906baaad45bd403fb6efb92", "size_in_bytes": 218}, {"_path": "Lib/site-packages/setuptools/script.tmpl", "path_type": "hardlink", "sha256": "5864ede6989eccedbb73e0dbc7a9794384f715fdb4039cfbf3bda1bf76808586", "sha256_in_prefix": "5864ede6989eccedbb73e0dbc7a9794384f715fdb4039cfbf3bda1bf76808586", "size_in_bytes": 138}, {"_path": "Lib/site-packages/setuptools/tests/__init__.py", "path_type": "hardlink", "sha256": "02705f96cda225b4c343398c29e2d1b7ef65c6168e1d454e644817bfcf54c2fb", "sha256_in_prefix": "02705f96cda225b4c343398c29e2d1b7ef65c6168e1d454e644817bfcf54c2fb", "size_in_bytes": 335}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "222fcf13451f861384d3147f97bc03ce35a5a1e10b7150e3c9a85736d5d0bc69", "sha256_in_prefix": "222fcf13451f861384d3147f97bc03ce35a5a1e10b7150e3c9a85736d5d0bc69", "size_in_bytes": 663}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/contexts.cpython-311.pyc", "path_type": "hardlink", "sha256": "8fa4119b6c7b5484c335c1064e5712382369d08f88b518babfbcccbc2b676f19", "sha256_in_prefix": "8fa4119b6c7b5484c335c1064e5712382369d08f88b518babfbcccbc2b676f19", "size_in_bytes": 7712}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/environment.cpython-311.pyc", "path_type": "hardlink", "sha256": "3307e8086190ad4ebb434bf33089e1b9571d5ad049f4adc510fa886364b36322", "sha256_in_prefix": "3307e8086190ad4ebb434bf33089e1b9571d5ad049f4adc510fa886364b36322", "size_in_bytes": 3822}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/fixtures.cpython-311.pyc", "path_type": "hardlink", "sha256": "700b652197ffe7c0517165e40ab6f5d4c86bdc0fc16978779cbd97335124d378", "sha256_in_prefix": "700b652197ffe7c0517165e40ab6f5d4c86bdc0fc16978779cbd97335124d378", "size_in_bytes": 8265}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/mod_with_constant.cpython-311.pyc", "path_type": "hardlink", "sha256": "841f5b0b60d9fa82fd50f992cd66510aaf9c4a42489e18bb10f07d64bcbb7843", "sha256_in_prefix": "841f5b0b60d9fa82fd50f992cd66510aaf9c4a42489e18bb10f07d64bcbb7843", "size_in_bytes": 184}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/namespaces.cpython-311.pyc", "path_type": "hardlink", "sha256": "8becc6da793ac9765cd03c79899fc89b505d39ac7c1db457b0d222964e27f911", "sha256_in_prefix": "8becc6da793ac9765cd03c79899fc89b505d39ac7c1db457b0d222964e27f911", "size_in_bytes": 4637}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/script-with-bom.cpython-311.pyc", "path_type": "hardlink", "sha256": "132231a4b78ad734bd7e6d6e7fd8a80cc827c513a77713dda7384319b52473b6", "sha256_in_prefix": "132231a4b78ad734bd7e6d6e7fd8a80cc827c513a77713dda7384319b52473b6", "size_in_bytes": 178}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/server.cpython-311.pyc", "path_type": "hardlink", "sha256": "0c2bbaa36e281d06d46dbccd61e4a351e5c655990c0f5f310862f51f5757ead2", "sha256_in_prefix": "0c2bbaa36e281d06d46dbccd61e4a351e5c655990c0f5f310862f51f5757ead2", "size_in_bytes": 5246}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_archive_util.cpython-311.pyc", "path_type": "hardlink", "sha256": "b053b64aa4cd764d5cac7ba848d3c1ac374a1db38eb559c8f83322b0d8eed289", "sha256_in_prefix": "b053b64aa4cd764d5cac7ba848d3c1ac374a1db38eb559c8f83322b0d8eed289", "size_in_bytes": 2125}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_bdist_deprecations.cpython-311.pyc", "path_type": "hardlink", "sha256": "b4b2487bc1918405bae7641479d4bb1a916db53dc3b4d213754e00bf2bd18921", "sha256_in_prefix": "b4b2487bc1918405bae7641479d4bb1a916db53dc3b4d213754e00bf2bd18921", "size_in_bytes": 1771}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_bdist_egg.cpython-311.pyc", "path_type": "hardlink", "sha256": "12f0116cfbcaa914134b1836070aacb358bd2ef6d085f2f78409dedce7ad56be", "sha256_in_prefix": "12f0116cfbcaa914134b1836070aacb358bd2ef6d085f2f78409dedce7ad56be", "size_in_bytes": 4871}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_bdist_wheel.cpython-311.pyc", "path_type": "hardlink", "sha256": "204b94a91d370090cf0ea4c1a34f8a01237764ec68b1ab8548f9f46858328996", "sha256_in_prefix": "204b94a91d370090cf0ea4c1a34f8a01237764ec68b1ab8548f9f46858328996", "size_in_bytes": 39308}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_build.cpython-311.pyc", "path_type": "hardlink", "sha256": "849db3ccc27eb52902eaecefbb567d49ef602fa85a727039faa4f2e12fcbba7a", "sha256_in_prefix": "849db3ccc27eb52902eaecefbb567d49ef602fa85a727039faa4f2e12fcbba7a", "size_in_bytes": 1808}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_build_clib.cpython-311.pyc", "path_type": "hardlink", "sha256": "164ca9a15381ddae9b7851a399384bfdb5c35b7c2f9f94b4cee9942d49744940", "sha256_in_prefix": "164ca9a15381ddae9b7851a399384bfdb5c35b7c2f9f94b4cee9942d49744940", "size_in_bytes": 4664}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_build_ext.cpython-311.pyc", "path_type": "hardlink", "sha256": "fac367d5beea182ce20334cee2c981961897fe28dc5241cca3414b857df839a9", "sha256_in_prefix": "fac367d5beea182ce20334cee2c981961897fe28dc5241cca3414b857df839a9", "size_in_bytes": 15194}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_build_meta.cpython-311.pyc", "path_type": "hardlink", "sha256": "4ceba7ea03d6a8b097a52a484490374401442b071b4746afe05bacc0b225a6fc", "sha256_in_prefix": "4ceba7ea03d6a8b097a52a484490374401442b071b4746afe05bacc0b225a6fc", "size_in_bytes": 50013}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_build_py.cpython-311.pyc", "path_type": "hardlink", "sha256": "4ceff34029209b21cec08bbd6ea3c2cb0c63c9135067f39a24381696e2321987", "sha256_in_prefix": "4ceff34029209b21cec08bbd6ea3c2cb0c63c9135067f39a24381696e2321987", "size_in_bytes": 19237}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_config_discovery.cpython-311.pyc", "path_type": "hardlink", "sha256": "558b5a376f1856bc38aa631d7398a5465673e2b39ed0f21e77b39a633a93d07f", "sha256_in_prefix": "558b5a376f1856bc38aa631d7398a5465673e2b39ed0f21e77b39a633a93d07f", "size_in_bytes": 35320}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_core_metadata.cpython-311.pyc", "path_type": "hardlink", "sha256": "65a2885ba8f1521db8b4a34ba75e310c47e676eb56e9303fc058481b86eae038", "sha256_in_prefix": "65a2885ba8f1521db8b4a34ba75e310c47e676eb56e9303fc058481b86eae038", "size_in_bytes": 26372}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_depends.cpython-311.pyc", "path_type": "hardlink", "sha256": "755eea52aae54f9ffdc44029a3063552083371ebd32218458ade8cfb8fb14830", "sha256_in_prefix": "755eea52aae54f9ffdc44029a3063552083371ebd32218458ade8cfb8fb14830", "size_in_bytes": 1000}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_develop.cpython-311.pyc", "path_type": "hardlink", "sha256": "28892905d884a33af5a992f00e2a5928132aae74bb95e697933e0190164dd877", "sha256_in_prefix": "28892905d884a33af5a992f00e2a5928132aae74bb95e697933e0190164dd877", "size_in_bytes": 10112}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_dist.cpython-311.pyc", "path_type": "hardlink", "sha256": "2cf0b4e776a89103760b8866db41a9ac27b37b8955c3a3dcf4e5ae29c60b5f88", "sha256_in_prefix": "2cf0b4e776a89103760b8866db41a9ac27b37b8955c3a3dcf4e5ae29c60b5f88", "size_in_bytes": 12981}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_dist_info.cpython-311.pyc", "path_type": "hardlink", "sha256": "43e2499bea958b1cee29c777b5752147925a2c2c4cc10c387c1fc002fc0fd060", "sha256_in_prefix": "43e2499bea958b1cee29c777b5752147925a2c2c4cc10c387c1fc002fc0fd060", "size_in_bytes": 12331}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_distutils_adoption.cpython-311.pyc", "path_type": "hardlink", "sha256": "0c488406bf11c5cb2c55b4f7dc1d4376e2e5e9c97f2e603a6d0b7b002819f320", "sha256_in_prefix": "0c488406bf11c5cb2c55b4f7dc1d4376e2e5e9c97f2e603a6d0b7b002819f320", "size_in_bytes": 8733}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_easy_install.cpython-311.pyc", "path_type": "hardlink", "sha256": "671ef9de6e3a5c9ef79899803d27f6fb12e4352666b6f7079915f76f3092bdcf", "sha256_in_prefix": "671ef9de6e3a5c9ef79899803d27f6fb12e4352666b6f7079915f76f3092bdcf", "size_in_bytes": 81593}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_editable_install.cpython-311.pyc", "path_type": "hardlink", "sha256": "5b94fbf543ba47fa0792bb042685d4a38603441a63e2866391f6284106c105ae", "sha256_in_prefix": "5b94fbf543ba47fa0792bb042685d4a38603441a63e2866391f6284106c105ae", "size_in_bytes": 68000}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_egg_info.cpython-311.pyc", "path_type": "hardlink", "sha256": "d12729f022b5e9a640cdd6fc7b22eec6089814a2719cc323de28b4476f6a295a", "sha256_in_prefix": "d12729f022b5e9a640cdd6fc7b22eec6089814a2719cc323de28b4476f6a295a", "size_in_bytes": 54657}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_extern.cpython-311.pyc", "path_type": "hardlink", "sha256": "ce7812c41e7edf6063ecf51c892c1048b1dfccf37418a829ec4896a93f67638c", "sha256_in_prefix": "ce7812c41e7edf6063ecf51c892c1048b1dfccf37418a829ec4896a93f67638c", "size_in_bytes": 890}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_find_packages.cpython-311.pyc", "path_type": "hardlink", "sha256": "f203e7526ae0bd6926d78ed7b15a25342734ad0a1adc0e80f444375eac338a35", "sha256_in_prefix": "f203e7526ae0bd6926d78ed7b15a25342734ad0a1adc0e80f444375eac338a35", "size_in_bytes": 13108}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_find_py_modules.cpython-311.pyc", "path_type": "hardlink", "sha256": "8555cff7b41b7ab34880393adf97439fabd06613512b89ad5e0b74ecd60b18d4", "sha256_in_prefix": "8555cff7b41b7ab34880393adf97439fabd06613512b89ad5e0b74ecd60b18d4", "size_in_bytes": 4204}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_glob.cpython-311.pyc", "path_type": "hardlink", "sha256": "8fc1397ed3dace722e9faed7d6aaad10bb628a9e90c9bd2e90aa94817bbce1dd", "sha256_in_prefix": "8fc1397ed3dace722e9faed7d6aaad10bb628a9e90c9bd2e90aa94817bbce1dd", "size_in_bytes": 1496}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_install_scripts.cpython-311.pyc", "path_type": "hardlink", "sha256": "40b046880ab5a5d0ed9d60453eb8254ff9b970d2166c9d55d41313c092d7d4f3", "sha256_in_prefix": "40b046880ab5a5d0ed9d60453eb8254ff9b970d2166c9d55d41313c092d7d4f3", "size_in_bytes": 7417}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_logging.cpython-311.pyc", "path_type": "hardlink", "sha256": "dc5ac12d57a55e903cac0dc7d732a9916179a645d11ab2c0a056b277ea998705", "sha256_in_prefix": "dc5ac12d57a55e903cac0dc7d732a9916179a645d11ab2c0a056b277ea998705", "size_in_bytes": 3408}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_manifest.cpython-311.pyc", "path_type": "hardlink", "sha256": "e9918908c5f304e318c8640a858a22f87778044c7bd2ce165ad6d0845be9ab14", "sha256_in_prefix": "e9918908c5f304e318c8640a858a22f87778044c7bd2ce165ad6d0845be9ab14", "size_in_bytes": 30779}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_namespaces.cpython-311.pyc", "path_type": "hardlink", "sha256": "3d373729507d554e0047de2c5ba73200dd978b6e41bd4749246c4af0fb459fc4", "sha256_in_prefix": "3d373729507d554e0047de2c5ba73200dd978b6e41bd4749246c4af0fb459fc4", "size_in_bytes": 6347}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_packageindex.cpython-311.pyc", "path_type": "hardlink", "sha256": "a9ddbf14c53249bcb4ce93f8b188ae64e0b60f45df0d1861451d0ce831b33d55", "sha256_in_prefix": "a9ddbf14c53249bcb4ce93f8b188ae64e0b60f45df0d1861451d0ce831b33d55", "size_in_bytes": 20463}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_sandbox.cpython-311.pyc", "path_type": "hardlink", "sha256": "fd38ca4281b1a3b576dc3482a886ad3da870d4627193517d75c71dbf31d10f17", "sha256_in_prefix": "fd38ca4281b1a3b576dc3482a886ad3da870d4627193517d75c71dbf31d10f17", "size_in_bytes": 11442}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_sdist.cpython-311.pyc", "path_type": "hardlink", "sha256": "e09d0a88e79026fbfc9ea18425303dbb5715abe1f6f4e6416665d73530d2c9d8", "sha256_in_prefix": "e09d0a88e79026fbfc9ea18425303dbb5715abe1f6f4e6416665d73530d2c9d8", "size_in_bytes": 53425}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_setopt.cpython-311.pyc", "path_type": "hardlink", "sha256": "3ae8d2602def2e71462e623fbff2237b823d231519981a6baeb0749825dc2424", "sha256_in_prefix": "3ae8d2602def2e71462e623fbff2237b823d231519981a6baeb0749825dc2424", "size_in_bytes": 3435}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_setuptools.cpython-311.pyc", "path_type": "hardlink", "sha256": "a70915f92af686ce1831287afd5232ee153cb19c7cf1a909fc4601260daa8c81", "sha256_in_prefix": "a70915f92af686ce1831287afd5232ee153cb19c7cf1a909fc4601260daa8c81", "size_in_bytes": 20383}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_shutil_wrapper.cpython-311.pyc", "path_type": "hardlink", "sha256": "3f527e25f49445807464931052550051725ae44e18c546362d44d57c0137aea4", "sha256_in_prefix": "3f527e25f49445807464931052550051725ae44e18c546362d44d57c0137aea4", "size_in_bytes": 1450}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_unicode_utils.cpython-311.pyc", "path_type": "hardlink", "sha256": "5b5a9558e23942f8ace120e6e087f25d6028be3b59e158d0a5fb906e20205063", "sha256_in_prefix": "5b5a9558e23942f8ace120e6e087f25d6028be3b59e158d0a5fb906e20205063", "size_in_bytes": 829}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_virtualenv.cpython-311.pyc", "path_type": "hardlink", "sha256": "c1e523f50ed02b49d872f85e8d511b1b94726ea367735c4b7310633e664bb2a8", "sha256_in_prefix": "c1e523f50ed02b49d872f85e8d511b1b94726ea367735c4b7310633e664bb2a8", "size_in_bytes": 4903}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_warnings.cpython-311.pyc", "path_type": "hardlink", "sha256": "cdeb84d9637314a2087f5330d95f9e1ec7caaacff0fa4566953f681cbc2ba1c5", "sha256_in_prefix": "cdeb84d9637314a2087f5330d95f9e1ec7caaacff0fa4566953f681cbc2ba1c5", "size_in_bytes": 4622}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_wheel.cpython-311.pyc", "path_type": "hardlink", "sha256": "4be53d8b0548cfd090cd86c03133d113b327300eea5c56ba39c9feeda67d42f0", "sha256_in_prefix": "4be53d8b0548cfd090cd86c03133d113b327300eea5c56ba39c9feeda67d42f0", "size_in_bytes": 23392}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_windows_wrappers.cpython-311.pyc", "path_type": "hardlink", "sha256": "2afc5f721ffd42c021e000bc9a8ee6206eb2cddbaf80321f58f7b98adccf357f", "sha256_in_prefix": "2afc5f721ffd42c021e000bc9a8ee6206eb2cddbaf80321f58f7b98adccf357f", "size_in_bytes": 11415}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/text.cpython-311.pyc", "path_type": "hardlink", "sha256": "99b4b180fb34c53356622cd83d4ec06831c8cbe4815c8afe38982bf37f1e861f", "sha256_in_prefix": "99b4b180fb34c53356622cd83d4ec06831c8cbe4815c8afe38982bf37f1e861f", "size_in_bytes": 563}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/textwrap.cpython-311.pyc", "path_type": "hardlink", "sha256": "e94fe8b99dcc5b042bdf9ce1bda0dc66c3f62ca6c852d6d8acad8e91e219c538", "sha256_in_prefix": "e94fe8b99dcc5b042bdf9ce1bda0dc66c3f62ca6c852d6d8acad8e91e219c538", "size_in_bytes": 433}, {"_path": "Lib/site-packages/setuptools/tests/compat/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/tests/compat/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "b080aeba969a79cecd7b0b1f225336f9b55e125b9d20bce64155a3778b96745d", "sha256_in_prefix": "b080aeba969a79cecd7b0b1f225336f9b55e125b9d20bce64155a3778b96745d", "size_in_bytes": 162}, {"_path": "Lib/site-packages/setuptools/tests/compat/__pycache__/py39.cpython-311.pyc", "path_type": "hardlink", "sha256": "e149442d3746d9752b81d1e056420361adbc36be4ac186a67f07662d96d7c0a3", "sha256_in_prefix": "e149442d3746d9752b81d1e056420361adbc36be4ac186a67f07662d96d7c0a3", "size_in_bytes": 363}, {"_path": "Lib/site-packages/setuptools/tests/compat/py39.py", "path_type": "hardlink", "sha256": "794cbbfc5fba2914ce20a97ebdeb2152ee88d0475349d059321d04574959d7e8", "sha256_in_prefix": "794cbbfc5fba2914ce20a97ebdeb2152ee88d0475349d059321d04574959d7e8", "size_in_bytes": 135}, {"_path": "Lib/site-packages/setuptools/tests/config/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/tests/config/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "97f62d91e32b2faa0ae0451e50d95a69afb2129d010002ded44b4e011e892317", "sha256_in_prefix": "97f62d91e32b2faa0ae0451e50d95a69afb2129d010002ded44b4e011e892317", "size_in_bytes": 162}, {"_path": "Lib/site-packages/setuptools/tests/config/__pycache__/test_apply_pyprojecttoml.cpython-311.pyc", "path_type": "hardlink", "sha256": "3e41c819b78568d4ef93b080fd7d6199bab2193985a2c589ce5f9eae527a987d", "sha256_in_prefix": "3e41c819b78568d4ef93b080fd7d6199bab2193985a2c589ce5f9eae527a987d", "size_in_bytes": 46604}, {"_path": "Lib/site-packages/setuptools/tests/config/__pycache__/test_expand.cpython-311.pyc", "path_type": "hardlink", "sha256": "abfcd1729ef86fe35ef87741bd42c4faf3c77c3cc8f33a6d33c1de6060f0de88", "sha256_in_prefix": "abfcd1729ef86fe35ef87741bd42c4faf3c77c3cc8f33a6d33c1de6060f0de88", "size_in_bytes": 13766}, {"_path": "Lib/site-packages/setuptools/tests/config/__pycache__/test_pyprojecttoml.cpython-311.pyc", "path_type": "hardlink", "sha256": "6ace978c7467caf04f4d3d8da40ff4cd22070f02e9597be180cf94ff5227f7cd", "sha256_in_prefix": "6ace978c7467caf04f4d3d8da40ff4cd22070f02e9597be180cf94ff5227f7cd", "size_in_bytes": 18429}, {"_path": "Lib/site-packages/setuptools/tests/config/__pycache__/test_pyprojecttoml_dynamic_deps.cpython-311.pyc", "path_type": "hardlink", "sha256": "96effe39adc85cf3002186e30d1e95affd2e55a3aa53c0ff7a884a585aa1a947", "sha256_in_prefix": "96effe39adc85cf3002186e30d1e95affd2e55a3aa53c0ff7a884a585aa1a947", "size_in_bytes": 4722}, {"_path": "Lib/site-packages/setuptools/tests/config/__pycache__/test_setupcfg.cpython-311.pyc", "path_type": "hardlink", "sha256": "9b507c3952cbff02a927eddc7b93158207e7c053b16bae6abe0f79f2a2006ef2", "sha256_in_prefix": "9b507c3952cbff02a927eddc7b93158207e7c053b16bae6abe0f79f2a2006ef2", "size_in_bytes": 53118}, {"_path": "Lib/site-packages/setuptools/tests/config/downloads/__init__.py", "path_type": "hardlink", "sha256": "f62c670c47722ff6ab29b5337ee8897ed023f5b1b12b3f0cf5a94e159323c7d6", "sha256_in_prefix": "f62c670c47722ff6ab29b5337ee8897ed023f5b1b12b3f0cf5a94e159323c7d6", "size_in_bytes": 1827}, {"_path": "Lib/site-packages/setuptools/tests/config/downloads/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "a652eaa6bb8d35e0fc09713f1ca2c5b185a5029eec9deeb2020d719daf360968", "sha256_in_prefix": "a652eaa6bb8d35e0fc09713f1ca2c5b185a5029eec9deeb2020d719daf360968", "size_in_bytes": 3861}, {"_path": "Lib/site-packages/setuptools/tests/config/downloads/__pycache__/preload.cpython-311.pyc", "path_type": "hardlink", "sha256": "7780474229f727141e73fa92fea2732ff6d4b950a0f906dd5f6c6f9c9a61c8ff", "sha256_in_prefix": "7780474229f727141e73fa92fea2732ff6d4b950a0f906dd5f6c6f9c9a61c8ff", "size_in_bytes": 854}, {"_path": "Lib/site-packages/setuptools/tests/config/downloads/preload.py", "path_type": "hardlink", "sha256": "b081866696377263293308896186181c6da27d9264bc9804a2d445b62ba55752", "sha256_in_prefix": "b081866696377263293308896186181c6da27d9264bc9804a2d445b62ba55752", "size_in_bytes": 450}, {"_path": "Lib/site-packages/setuptools/tests/config/setupcfg_examples.txt", "path_type": "hardlink", "sha256": "7006d5bc26e4159b9350beb1451cd182ac81d2b2ef2537efc370f7d20968d8e1", "sha256_in_prefix": "7006d5bc26e4159b9350beb1451cd182ac81d2b2ef2537efc370f7d20968d8e1", "size_in_bytes": 1912}, {"_path": "Lib/site-packages/setuptools/tests/config/test_apply_pyprojecttoml.py", "path_type": "hardlink", "sha256": "97a9c4e1df162d4fde49646273b552a2a78abfd062ec26461dc12e0767a1936c", "sha256_in_prefix": "97a9c4e1df162d4fde49646273b552a2a78abfd062ec26461dc12e0767a1936c", "size_in_bytes": 28807}, {"_path": "Lib/site-packages/setuptools/tests/config/test_expand.py", "path_type": "hardlink", "sha256": "4b4a13e89be003fa2e8d1e184b8454b9fe6098eb75093415eba4500f357cc5de", "sha256_in_prefix": "4b4a13e89be003fa2e8d1e184b8454b9fe6098eb75093415eba4500f357cc5de", "size_in_bytes": 8933}, {"_path": "Lib/site-packages/setuptools/tests/config/test_pyprojecttoml.py", "path_type": "hardlink", "sha256": "d0b79f4a58d4840e8caad279015ccb8689aa65c62214a76eff57240de313d4b6", "sha256_in_prefix": "d0b79f4a58d4840e8caad279015ccb8689aa65c62214a76eff57240de313d4b6", "size_in_bytes": 12406}, {"_path": "Lib/site-packages/setuptools/tests/config/test_pyprojecttoml_dynamic_deps.py", "path_type": "hardlink", "sha256": "f56ef7fb22e16499af0a23b8ad3890a01a594f9c0d03dd176dde67d870ac85de", "sha256_in_prefix": "f56ef7fb22e16499af0a23b8ad3890a01a594f9c0d03dd176dde67d870ac85de", "size_in_bytes": 3271}, {"_path": "Lib/site-packages/setuptools/tests/config/test_setupcfg.py", "path_type": "hardlink", "sha256": "66f37e3bed838289f569da7aa0cea297c2567604fdcb5f7a7d1bea11253910b2", "sha256_in_prefix": "66f37e3bed838289f569da7aa0cea297c2567604fdcb5f7a7d1bea11253910b2", "size_in_bytes": 33427}, {"_path": "Lib/site-packages/setuptools/tests/contexts.py", "path_type": "hardlink", "sha256": "4c07592b19a6a1dc75131315a34d68e10a518e9229a385f72162aafc19e3c695", "sha256_in_prefix": "4c07592b19a6a1dc75131315a34d68e10a518e9229a385f72162aafc19e3c695", "size_in_bytes": 3480}, {"_path": "Lib/site-packages/setuptools/tests/environment.py", "path_type": "hardlink", "sha256": "f79fd4b536918aebf0602f5e5ca1076e7d36903b59cacbd9ab75192663d48f52", "sha256_in_prefix": "f79fd4b536918aebf0602f5e5ca1076e7d36903b59cacbd9ab75192663d48f52", "size_in_bytes": 3102}, {"_path": "Lib/site-packages/setuptools/tests/fixtures.py", "path_type": "hardlink", "sha256": "f95ee20fa05e136134470e9d56f4ce0a6dfa246f194d39eb5e13741884a582b8", "sha256_in_prefix": "f95ee20fa05e136134470e9d56f4ce0a6dfa246f194d39eb5e13741884a582b8", "size_in_bytes": 5197}, {"_path": "Lib/site-packages/setuptools/tests/indexes/test_links_priority/external.html", "path_type": "hardlink", "sha256": "78bf5eb8eb84f7724a65daa55f104e9476cac08b8db8876aec6051a6c68f31c5", "sha256_in_prefix": "78bf5eb8eb84f7724a65daa55f104e9476cac08b8db8876aec6051a6c68f31c5", "size_in_bytes": 92}, {"_path": "Lib/site-packages/setuptools/tests/indexes/test_links_priority/simple/foobar/index.html", "path_type": "hardlink", "sha256": "0c3f932abed4538cc08c71f3e157b1603352033476ee57af4a1d5cfa4dd974b1", "sha256_in_prefix": "0c3f932abed4538cc08c71f3e157b1603352033476ee57af4a1d5cfa4dd974b1", "size_in_bytes": 174}, {"_path": "Lib/site-packages/setuptools/tests/integration/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/tests/integration/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "41eaf5faa1e88e25eefecf109d11220b936b28aa7ce9539990a07fa8d046c4f4", "sha256_in_prefix": "41eaf5faa1e88e25eefecf109d11220b936b28aa7ce9539990a07fa8d046c4f4", "size_in_bytes": 167}, {"_path": "Lib/site-packages/setuptools/tests/integration/__pycache__/helpers.cpython-311.pyc", "path_type": "hardlink", "sha256": "2135ba5f3b7505ee5cdd035c81282aa290f3a70c3dcb0f90f381a1b76b574e28", "sha256_in_prefix": "2135ba5f3b7505ee5cdd035c81282aa290f3a70c3dcb0f90f381a1b76b574e28", "size_in_bytes": 5332}, {"_path": "Lib/site-packages/setuptools/tests/integration/__pycache__/test_pip_install_sdist.cpython-311.pyc", "path_type": "hardlink", "sha256": "9868e83e10e76f9492c2b83810f5e728912ed7546bd01a69183a817abd53b5e5", "sha256_in_prefix": "9868e83e10e76f9492c2b83810f5e728912ed7546bd01a69183a817abd53b5e5", "size_in_bytes": 10518}, {"_path": "Lib/site-packages/setuptools/tests/integration/helpers.py", "path_type": "hardlink", "sha256": "dcf1dc4bd48203e7f05499943f669de4d40eb6d240113239367a1cff1ae83b99", "sha256_in_prefix": "dcf1dc4bd48203e7f05499943f669de4d40eb6d240113239367a1cff1ae83b99", "size_in_bytes": 2522}, {"_path": "Lib/site-packages/setuptools/tests/integration/test_pip_install_sdist.py", "path_type": "hardlink", "sha256": "4856efb9817f843cede8eb6c4391a314d9f19a827f78495fbe962c8b2c8627e8", "sha256_in_prefix": "4856efb9817f843cede8eb6c4391a314d9f19a827f78495fbe962c8b2c8627e8", "size_in_bytes": 8256}, {"_path": "Lib/site-packages/setuptools/tests/mod_with_constant.py", "path_type": "hardlink", "sha256": "5ff2a3f34339e70d6d990e1feee658f7565300ba3884a553e841f1818a1c50c4", "sha256_in_prefix": "5ff2a3f34339e70d6d990e1feee658f7565300ba3884a553e841f1818a1c50c4", "size_in_bytes": 22}, {"_path": "Lib/site-packages/setuptools/tests/namespaces.py", "path_type": "hardlink", "sha256": "1cf708de74793021565e96800c82757f02b1ca671080192ec3cec87393d44804", "sha256_in_prefix": "1cf708de74793021565e96800c82757f02b1ca671080192ec3cec87393d44804", "size_in_bytes": 2774}, {"_path": "Lib/site-packages/setuptools/tests/script-with-bom.py", "path_type": "hardlink", "sha256": "851460222cc450b1a21bf653368318e3a1e12a1c6959fcb9146703e906e1e5f7", "sha256_in_prefix": "851460222cc450b1a21bf653368318e3a1e12a1c6959fcb9146703e906e1e5f7", "size_in_bytes": 18}, {"_path": "Lib/site-packages/setuptools/tests/server.py", "path_type": "hardlink", "sha256": "d050d97f471222708fe67d6168aec0c47a378c3dbad512bb0f7f918cff85e779", "sha256_in_prefix": "d050d97f471222708fe67d6168aec0c47a378c3dbad512bb0f7f918cff85e779", "size_in_bytes": 2397}, {"_path": "Lib/site-packages/setuptools/tests/test_archive_util.py", "path_type": "hardlink", "sha256": "6eeb8a758f17916dba3dedc8280a014461c6d0c0ee9a7b8da0f8365ac010cc88", "sha256_in_prefix": "6eeb8a758f17916dba3dedc8280a014461c6d0c0ee9a7b8da0f8365ac010cc88", "size_in_bytes": 845}, {"_path": "Lib/site-packages/setuptools/tests/test_bdist_deprecations.py", "path_type": "hardlink", "sha256": "ef95eade0627efd2c8232bac125b5b1da9f46c4800b767bf09a2fb28b4bcf8a4", "sha256_in_prefix": "ef95eade0627efd2c8232bac125b5b1da9f46c4800b767bf09a2fb28b4bcf8a4", "size_in_bytes": 775}, {"_path": "Lib/site-packages/setuptools/tests/test_bdist_egg.py", "path_type": "hardlink", "sha256": "e8f6983751772436c8875b8ad2eaefef2245731f7ccf9767f52389f0cbfdd65f", "sha256_in_prefix": "e8f6983751772436c8875b8ad2eaefef2245731f7ccf9767f52389f0cbfdd65f", "size_in_bytes": 1957}, {"_path": "Lib/site-packages/setuptools/tests/test_bdist_wheel.py", "path_type": "hardlink", "sha256": "759f5aece4ff53246f2e7a028b62861086edce11108ccdd8bad60c03a6427b3b", "sha256_in_prefix": "759f5aece4ff53246f2e7a028b62861086edce11108ccdd8bad60c03a6427b3b", "size_in_bytes": 23083}, {"_path": "Lib/site-packages/setuptools/tests/test_build.py", "path_type": "hardlink", "sha256": "c0980ccf68701c00dc2c583e9d7af045586eb3b8639807841a0ae9210c021a70", "sha256_in_prefix": "c0980ccf68701c00dc2c583e9d7af045586eb3b8639807841a0ae9210c021a70", "size_in_bytes": 798}, {"_path": "Lib/site-packages/setuptools/tests/test_build_clib.py", "path_type": "hardlink", "sha256": "6d7e755d101fe2e3bb22e1c5a6378f9e82bc984ef837682ca1e12a17ea1f830b", "sha256_in_prefix": "6d7e755d101fe2e3bb22e1c5a6378f9e82bc984ef837682ca1e12a17ea1f830b", "size_in_bytes": 3123}, {"_path": "Lib/site-packages/setuptools/tests/test_build_ext.py", "path_type": "hardlink", "sha256": "7b8652c6c60f079cead4a4aa184b804d9d2dd0f250ccc8638e4289fa12237207", "sha256_in_prefix": "7b8652c6c60f079cead4a4aa184b804d9d2dd0f250ccc8638e4289fa12237207", "size_in_bytes": 10099}, {"_path": "Lib/site-packages/setuptools/tests/test_build_meta.py", "path_type": "hardlink", "sha256": "21a929a7d32272f8718bdfc5d913f2636367081d46f746b7f2ce0ee40dc2ba21", "sha256_in_prefix": "21a929a7d32272f8718bdfc5d913f2636367081d46f746b7f2ce0ee40dc2ba21", "size_in_bytes": 34118}, {"_path": "Lib/site-packages/setuptools/tests/test_build_py.py", "path_type": "hardlink", "sha256": "8286cc13f0afcdfe94831abbd2259f5de91bff1cb24fad648708c5abcce4c1fc", "sha256_in_prefix": "8286cc13f0afcdfe94831abbd2259f5de91bff1cb24fad648708c5abcce4c1fc", "size_in_bytes": 14186}, {"_path": "Lib/site-packages/setuptools/tests/test_config_discovery.py", "path_type": "hardlink", "sha256": "16a57e94eb64a9a23e6b2cd4db3a1c49d0f94da4408026678b13438a5280e854", "sha256_in_prefix": "16a57e94eb64a9a23e6b2cd4db3a1c49d0f94da4408026678b13438a5280e854", "size_in_bytes": 22580}, {"_path": "Lib/site-packages/setuptools/tests/test_core_metadata.py", "path_type": "hardlink", "sha256": "bdb549e7f2ecc7f86c3bf19d07a9d01172518c0db2771ebfa926ebe4ba617800", "sha256_in_prefix": "bdb549e7f2ecc7f86c3bf19d07a9d01172518c0db2771ebfa926ebe4ba617800", "size_in_bytes": 20881}, {"_path": "Lib/site-packages/setuptools/tests/test_depends.py", "path_type": "hardlink", "sha256": "c90057a106cd425262b7a99b455a33e816f9e777f7b0daead369598a6373e576", "sha256_in_prefix": "c90057a106cd425262b7a99b455a33e816f9e777f7b0daead369598a6373e576", "size_in_bytes": 424}, {"_path": "Lib/site-packages/setuptools/tests/test_develop.py", "path_type": "hardlink", "sha256": "08bcd767cf9be7e5454ee6aee0fe325c474bc7551dc9315c39fad5d2ac9421d1", "sha256_in_prefix": "08bcd767cf9be7e5454ee6aee0fe325c474bc7551dc9315c39fad5d2ac9421d1", "size_in_bytes": 5142}, {"_path": "Lib/site-packages/setuptools/tests/test_dist.py", "path_type": "hardlink", "sha256": "1858f22f67ad031bd5337abb36114419c5d2e60c8a8fc5736ea71b2b3a6a6ce9", "sha256_in_prefix": "1858f22f67ad031bd5337abb36114419c5d2e60c8a8fc5736ea71b2b3a6a6ce9", "size_in_bytes": 8901}, {"_path": "Lib/site-packages/setuptools/tests/test_dist_info.py", "path_type": "hardlink", "sha256": "e640518fdb6e06c56b781b18db61f67de30efc9419b12a0e64c53f3097d47af6", "sha256_in_prefix": "e640518fdb6e06c56b781b18db61f67de30efc9419b12a0e64c53f3097d47af6", "size_in_bytes": 7077}, {"_path": "Lib/site-packages/setuptools/tests/test_distutils_adoption.py", "path_type": "hardlink", "sha256": "fdeca7ace7f212a5c51268d4261ce97bc1973f24d43ef35239bb38a80026072f", "sha256_in_prefix": "fdeca7ace7f212a5c51268d4261ce97bc1973f24d43ef35239bb38a80026072f", "size_in_bytes": 5987}, {"_path": "Lib/site-packages/setuptools/tests/test_easy_install.py", "path_type": "hardlink", "sha256": "8f1e25a45c9e7b41b8df671d9f0068c370242f889bc3ed1020bc25770bf94822", "sha256_in_prefix": "8f1e25a45c9e7b41b8df671d9f0068c370242f889bc3ed1020bc25770bf94822", "size_in_bytes": 53534}, {"_path": "Lib/site-packages/setuptools/tests/test_editable_install.py", "path_type": "hardlink", "sha256": "ede4c4b694f493b41e572660eb87a1de4667f928dc92e07d2dca243ae577ec32", "sha256_in_prefix": "ede4c4b694f493b41e572660eb87a1de4667f928dc92e07d2dca243ae577ec32", "size_in_bytes": 43383}, {"_path": "Lib/site-packages/setuptools/tests/test_egg_info.py", "path_type": "hardlink", "sha256": "402ce850e905a1c99b9304ba5d4ec5f16373284f02184311c5806a28b81f52b7", "sha256_in_prefix": "402ce850e905a1c99b9304ba5d4ec5f16373284f02184311c5806a28b81f52b7", "size_in_bytes": 44866}, {"_path": "Lib/site-packages/setuptools/tests/test_extern.py", "path_type": "hardlink", "sha256": "ae9294ea809c92cba62f07f94de2a50e5b854344d47db3f04cb41ba71705ac25", "sha256_in_prefix": "ae9294ea809c92cba62f07f94de2a50e5b854344d47db3f04cb41ba71705ac25", "size_in_bytes": 296}, {"_path": "Lib/site-packages/setuptools/tests/test_find_packages.py", "path_type": "hardlink", "sha256": "0932c0713cd619604b09c776680b14564bcede26eb96a7b114174328e58fa2af", "sha256_in_prefix": "0932c0713cd619604b09c776680b14564bcede26eb96a7b114174328e58fa2af", "size_in_bytes": 7819}, {"_path": "Lib/site-packages/setuptools/tests/test_find_py_modules.py", "path_type": "hardlink", "sha256": "cd08ee8481b94d03764893e2c7d011a380cbff0f382e7f10b070d48e36ebb404", "sha256_in_prefix": "cd08ee8481b94d03764893e2c7d011a380cbff0f382e7f10b070d48e36ebb404", "size_in_bytes": 2404}, {"_path": "Lib/site-packages/setuptools/tests/test_glob.py", "path_type": "hardlink", "sha256": "3f726fa47fa45d0e01677cef445fb32b13a0c325b3c08690233d161ddc52d249", "sha256_in_prefix": "3f726fa47fa45d0e01677cef445fb32b13a0c325b3c08690233d161ddc52d249", "size_in_bytes": 887}, {"_path": "Lib/site-packages/setuptools/tests/test_install_scripts.py", "path_type": "hardlink", "sha256": "b1c22b27a6bfb2c2aa838bc804d6948e600a1c460b51467d58a9cf78a9c4ea07", "sha256_in_prefix": "b1c22b27a6bfb2c2aa838bc804d6948e600a1c460b51467d58a9cf78a9c4ea07", "size_in_bytes": 3433}, {"_path": "Lib/site-packages/setuptools/tests/test_logging.py", "path_type": "hardlink", "sha256": "ce51390e595dba40bb25ce7814dbc357feeec7712b024adfacde424ac9cd3944", "sha256_in_prefix": "ce51390e595dba40bb25ce7814dbc357feeec7712b024adfacde424ac9cd3944", "size_in_bytes": 2099}, {"_path": "Lib/site-packages/setuptools/tests/test_manifest.py", "path_type": "hardlink", "sha256": "78c83ae69200e760e2cc1ea6a64b5253e6fc0a3c1a3424b931280bfd5d4bac52", "sha256_in_prefix": "78c83ae69200e760e2cc1ea6a64b5253e6fc0a3c1a3424b931280bfd5d4bac52", "size_in_bytes": 18562}, {"_path": "Lib/site-packages/setuptools/tests/test_namespaces.py", "path_type": "hardlink", "sha256": "63abada1ee4f1c7a8bfc39606b0a81f45f17a6c5033efbf0d6c40c7a72b4e1ed", "sha256_in_prefix": "63abada1ee4f1c7a8bfc39606b0a81f45f17a6c5033efbf0d6c40c7a72b4e1ed", "size_in_bytes": 4515}, {"_path": "Lib/site-packages/setuptools/tests/test_packageindex.py", "path_type": "hardlink", "sha256": "a848cb1e94aeda00247a0c04b2dcc7413f8e9b5b902188c0f3378dcc45fbf6ea", "sha256_in_prefix": "a848cb1e94aeda00247a0c04b2dcc7413f8e9b5b902188c0f3378dcc45fbf6ea", "size_in_bytes": 8775}, {"_path": "Lib/site-packages/setuptools/tests/test_sandbox.py", "path_type": "hardlink", "sha256": "b2151613b7cb4d67bb27375f8ba36178159ab86de852e91b515e3a700ac3d2ed", "sha256_in_prefix": "b2151613b7cb4d67bb27375f8ba36178159ab86de852e91b515e3a700ac3d2ed", "size_in_bytes": 4330}, {"_path": "Lib/site-packages/setuptools/tests/test_sdist.py", "path_type": "hardlink", "sha256": "4582ef3dafe77f20b5666a229f3a8ccc9ca74c31b846d3d80b5f7fd0b53aa6fb", "sha256_in_prefix": "4582ef3dafe77f20b5666a229f3a8ccc9ca74c31b846d3d80b5f7fd0b53aa6fb", "size_in_bytes": 32872}, {"_path": "Lib/site-packages/setuptools/tests/test_setopt.py", "path_type": "hardlink", "sha256": "dd5c713380137cff8fe001a70e3a160a71ebe7e8bd0921104c5614d7e1539ef2", "sha256_in_prefix": "dd5c713380137cff8fe001a70e3a160a71ebe7e8bd0921104c5614d7e1539ef2", "size_in_bytes": 1365}, {"_path": "Lib/site-packages/setuptools/tests/test_setuptools.py", "path_type": "hardlink", "sha256": "fde221a8a7f8e7e3ad1eac517f6d0a9dd39926525d4b43ee14b5c13b733e2cdf", "sha256_in_prefix": "fde221a8a7f8e7e3ad1eac517f6d0a9dd39926525d4b43ee14b5c13b733e2cdf", "size_in_bytes": 9008}, {"_path": "Lib/site-packages/setuptools/tests/test_shutil_wrapper.py", "path_type": "hardlink", "sha256": "835e44d753ed6711be227076056345c87facbce6d7c765dc32180c2c93ee1677", "sha256_in_prefix": "835e44d753ed6711be227076056345c87facbce6d7c765dc32180c2c93ee1677", "size_in_bytes": 641}, {"_path": "Lib/site-packages/setuptools/tests/test_unicode_utils.py", "path_type": "hardlink", "sha256": "c567c4125f239100adf68b615135c97c599dc804c0160809b36b53c636ee99bc", "sha256_in_prefix": "c567c4125f239100adf68b615135c97c599dc804c0160809b36b53c636ee99bc", "size_in_bytes": 316}, {"_path": "Lib/site-packages/setuptools/tests/test_virtualenv.py", "path_type": "hardlink", "sha256": "83e9e30bff494c0b35615c7fd5d189fd0e919489cee2a295bbdf9702035be936", "sha256_in_prefix": "83e9e30bff494c0b35615c7fd5d189fd0e919489cee2a295bbdf9702035be936", "size_in_bytes": 3730}, {"_path": "Lib/site-packages/setuptools/tests/test_warnings.py", "path_type": "hardlink", "sha256": "cf0476cdc9c2782783a882d994938f01cbb23c7a03bc6bb53ad3956222cc93be", "sha256_in_prefix": "cf0476cdc9c2782783a882d994938f01cbb23c7a03bc6bb53ad3956222cc93be", "size_in_bytes": 3347}, {"_path": "Lib/site-packages/setuptools/tests/test_wheel.py", "path_type": "hardlink", "sha256": "27ef375b529d5d38008c5644dc7fb2b68861bc31358aa75b139605e632d09464", "sha256_in_prefix": "27ef375b529d5d38008c5644dc7fb2b68861bc31358aa75b139605e632d09464", "size_in_bytes": 19370}, {"_path": "Lib/site-packages/setuptools/tests/test_windows_wrappers.py", "path_type": "hardlink", "sha256": "685e944e8c0ddf2cc281d061f670d056f6087d262882b4caefbe931325c406a8", "sha256_in_prefix": "685e944e8c0ddf2cc281d061f670d056f6087d262882b4caefbe931325c406a8", "size_in_bytes": 7881}, {"_path": "Lib/site-packages/setuptools/tests/text.py", "path_type": "hardlink", "sha256": "6b5db5f7ba4c553bc1e85016434ba34fc7c84222c8589945025d5409a0d40df8", "sha256_in_prefix": "6b5db5f7ba4c553bc1e85016434ba34fc7c84222c8589945025d5409a0d40df8", "size_in_bytes": 123}, {"_path": "Lib/site-packages/setuptools/tests/textwrap.py", "path_type": "hardlink", "sha256": "14d34dabf322684271f3c3e7b1b250211c668f5aa681c00e0975d1b0e0cf24de", "sha256_in_prefix": "14d34dabf322684271f3c3e7b1b250211c668f5aa681c00e0975d1b0e0cf24de", "size_in_bytes": 98}, {"_path": "Lib/site-packages/setuptools/unicode_utils.py", "path_type": "hardlink", "sha256": "ba430687ca44030e85fc4cdbf8ae43ddcfb4efc46003f19c174a16ea5838952b", "sha256_in_prefix": "ba430687ca44030e85fc4cdbf8ae43ddcfb4efc46003f19c174a16ea5838952b", "size_in_bytes": 3189}, {"_path": "Lib/site-packages/setuptools/version.py", "path_type": "hardlink", "sha256": "58909e52ecaaef80289364de2bdf8e7b164ebbc5eb950cbbfb2d0112e58da2f4", "sha256_in_prefix": "58909e52ecaaef80289364de2bdf8e7b164ebbc5eb950cbbfb2d0112e58da2f4", "size_in_bytes": 161}, {"_path": "Lib/site-packages/setuptools/warnings.py", "path_type": "hardlink", "sha256": "a18d127b978eaa37bf144ca34e0a2751cd171b082cac8e5c826d64930ba5cffc", "sha256_in_prefix": "a18d127b978eaa37bf144ca34e0a2751cd171b082cac8e5c826d64930ba5cffc", "size_in_bytes": 3796}, {"_path": "Lib/site-packages/setuptools/wheel.py", "path_type": "hardlink", "sha256": "c6402dbe09bbb8f4f2615db3a95990d3003c90bc0ec914f625eb35cc0cb4ecab", "sha256_in_prefix": "c6402dbe09bbb8f4f2615db3a95990d3003c90bc0ec914f625eb35cc0cb4ecab", "size_in_bytes": 8624}, {"_path": "Lib/site-packages/setuptools/windows_support.py", "path_type": "hardlink", "sha256": "c16e0860b33506fed9d4c69ab8fdb198f8f2cbec249909d7772bd7b1c01ff5fc", "sha256_in_prefix": "c16e0860b33506fed9d4c69ab8fdb198f8f2cbec249909d7772bd7b1c01ff5fc", "size_in_bytes": 726}], "paths_version": 1}, "requested_spec": "None", "sha256": "b67d70b98141eb394fbc6a2e62f831fa110b014b1abd261437d66a0b2ffcf1fb", "size": 2367676, "subdir": "win-64", "timestamp": 1746025142000, "url": "https://repo.anaconda.com/pkgs/main/win-64/setuptools-78.1.1-py311haa95532_0.conda", "version": "78.1.1"}