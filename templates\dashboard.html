{% extends "base.html" %}
{% block content %}
<h1>📊 Lottery Data Dashboard</h1>

{% if db_info %}
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card stat-card">
            <div class="card-body text-center">
                <h3>{{ db_info.total_records }}</h3>
                <p>Total Records</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card stat-card">
            <div class="card-body text-center">
                <h3>{{ db_info.total_numbers }}</h3>
                <p>Total Numbers</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card stat-card">
            <div class="card-body text-center">
                <h3>{{ db_info.unique_animals }}</h3>
                <p>Unique Animals</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card stat-card">
            <div class="card-body text-center">
                <h3>{{ db_info.unique_elements }}</h3>
                <p>Unique Elements</p>
            </div>
        </div>
    </div>
</div>
{% endif %}

<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5>Recent Records</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>Date</th>
                                <th>Period</th>
                                <th>Numbers</th>
                                <th>Total</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for record in recent_records %}
                            <tr>
                                <td>{{ record.date }}</td>
                                <td>{{ record.period }}</td>
                                <td>{{ record.raw_numbers }}</td>
                                <td>{{ record.total_numbers }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}