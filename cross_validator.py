#!/usr/bin/env python3
"""
Cross-Validation System for Lottery Prediction Models.
"""

import os
import pandas as pd
import numpy as np
import json
import logging
from datetime import datetime
from sklearn.model_selection import TimeSeriesSplit, cross_val_score, KFold
from sklearn.metrics import mean_squared_error, mean_absolute_error, accuracy_score
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.linear_model import LinearRegression
from sklearn.preprocessing import StandardScaler
import matplotlib.pyplot as plt
import seaborn as sns
from collections import defaultdict
import warnings
warnings.filterwarnings('ignore')

# Set Chinese font for matplotlib
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

class LotteryCrossValidator:
    """Cross-validation system for lottery prediction models."""
    
    def __init__(self, data_file=None):
        """Initialize the cross-validator."""
        self.setup_logging()
        self.data = None
        self.processed_data = None
        self.validation_results = {}
        self.prediction_accuracy = {}
        
        if data_file:
            self.load_and_preprocess_data(data_file)
    
    def setup_logging(self):
        """Setup logging configuration."""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('logs/cross_validator.log', encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger('CrossValidator')
        
        # Create logs directory
        os.makedirs('logs', exist_ok=True)
    
    def load_and_preprocess_data(self, data_file):
        """Load and preprocess lottery data."""
        try:
            if data_file.endswith('.csv'):
                self.data = pd.read_csv(data_file)
            elif data_file.endswith('.json'):
                with open(data_file, 'r', encoding='utf-8') as f:
                    json_data = json.load(f)
                self.data = pd.DataFrame(json_data)
            
            self.logger.info(f"✅ Loaded {len(self.data)} records from {data_file}")
            self.preprocess_data()
            
        except Exception as e:
            self.logger.error(f"❌ Error loading data: {e}")
            raise
    
    def preprocess_data(self):
        """Preprocess data for cross-validation."""
        try:
            # Extract individual numbers and features
            numbers_data = []
            for _, row in self.data.iterrows():
                # Extract period number
                period_str = str(row.get('period', row.get('期数', '')))
                period_num = int(''.join(filter(str.isdigit, period_str))) if period_str else 0
                
                # Extract date
                date_str = str(row.get('date', row.get('日期', '')))
                
                # Extract numbers
                numbers = []
                for i in range(1, 8):
                    num_col = f'number_{i}'
                    if num_col in row and pd.notna(row[num_col]):
                        numbers.append(int(row[num_col]))
                
                if numbers and len(numbers) >= 6:
                    numbers_data.append({
                        'period': period_num,
                        'date': date_str,
                        'numbers': numbers[:6],
                        'special': numbers[6] if len(numbers) > 6 else numbers[-1],
                        'sum': sum(numbers[:6]),
                        'avg': np.mean(numbers[:6]),
                        'range': max(numbers[:6]) - min(numbers[:6]),
                        'even_count': sum(1 for n in numbers[:6] if n % 2 == 0),
                        'odd_count': sum(1 for n in numbers[:6] if n % 2 == 1),
                        'min_num': min(numbers[:6]),
                        'max_num': max(numbers[:6])
                    })
            
            self.processed_data = pd.DataFrame(numbers_data)
            self.processed_data = self.processed_data.sort_values('period').reset_index(drop=True)
            
            self.logger.info(f"✅ Preprocessed {len(self.processed_data)} records")
            
        except Exception as e:
            self.logger.error(f"❌ Error preprocessing data: {e}")
            raise
    
    def create_features(self):
        """Create features for machine learning models."""
        try:
            features_df = self.processed_data.copy()
            
            # Create lag features
            for lag in [1, 2, 3]:
                features_df[f'sum_lag_{lag}'] = features_df['sum'].shift(lag)
                features_df[f'avg_lag_{lag}'] = features_df['avg'].shift(lag)
                features_df[f'range_lag_{lag}'] = features_df['range'].shift(lag)
                features_df[f'even_count_lag_{lag}'] = features_df['even_count'].shift(lag)
                features_df[f'min_num_lag_{lag}'] = features_df['min_num'].shift(lag)
                features_df[f'max_num_lag_{lag}'] = features_df['max_num'].shift(lag)
            
            # Create moving averages
            for window in [3, 5]:
                features_df[f'sum_ma_{window}'] = features_df['sum'].rolling(window=window).mean()
                features_df[f'avg_ma_{window}'] = features_df['avg'].rolling(window=window).mean()
                features_df[f'range_ma_{window}'] = features_df['range'].rolling(window=window).mean()
            
            # Create trend features
            features_df['sum_trend'] = features_df['sum'].diff()
            features_df['avg_trend'] = features_df['avg'].diff()
            
            # Drop rows with NaN values
            features_df = features_df.dropna()
            
            return features_df
            
        except Exception as e:
            self.logger.error(f"❌ Error creating features: {e}")
            return None
    
    def time_series_cross_validation(self):
        """Perform time series cross-validation."""
        try:
            self.logger.info("📈 Performing time series cross-validation...")
            
            features_df = self.create_features()
            if features_df is None or len(features_df) < 10:
                self.logger.warning("⚠️ Insufficient data for time series CV")
                return
            
            # Define features and targets
            feature_columns = [col for col in features_df.columns 
                             if any(x in col for x in ['lag', 'ma', 'trend']) or col == 'period']
            X = features_df[feature_columns]
            
            targets = {
                'sum': features_df['sum'],
                'avg': features_df['avg'],
                'range': features_df['range'],
                'even_count': features_df['even_count']
            }
            
            models = {
                'RandomForest': RandomForestRegressor(n_estimators=50, random_state=42),
                'GradientBoosting': GradientBoostingRegressor(n_estimators=50, random_state=42),
                'LinearRegression': LinearRegression()
            }
            
            # Time series split
            tscv = TimeSeriesSplit(n_splits=3)
            
            ts_results = {}
            
            for target_name, y in targets.items():
                ts_results[target_name] = {}
                
                for model_name, model in models.items():
                    try:
                        scores = []
                        predictions = []
                        actuals = []
                        
                        for train_idx, test_idx in tscv.split(X):
                            X_train, X_test = X.iloc[train_idx], X.iloc[test_idx]
                            y_train, y_test = y.iloc[train_idx], y.iloc[test_idx]
                            
                            # Scale features for linear regression
                            if model_name == 'LinearRegression':
                                scaler = StandardScaler()
                                X_train_scaled = scaler.fit_transform(X_train)
                                X_test_scaled = scaler.transform(X_test)
                                
                                model.fit(X_train_scaled, y_train)
                                y_pred = model.predict(X_test_scaled)
                            else:
                                model.fit(X_train, y_train)
                                y_pred = model.predict(X_test)
                            
                            # Calculate metrics
                            mse = mean_squared_error(y_test, y_pred)
                            mae = mean_absolute_error(y_test, y_pred)
                            
                            scores.append({'mse': mse, 'mae': mae, 'rmse': np.sqrt(mse)})
                            predictions.extend(y_pred.tolist())
                            actuals.extend(y_test.tolist())
                        
                        # Aggregate results
                        avg_mse = np.mean([s['mse'] for s in scores])
                        avg_mae = np.mean([s['mae'] for s in scores])
                        avg_rmse = np.mean([s['rmse'] for s in scores])
                        
                        ts_results[target_name][model_name] = {
                            'avg_mse': avg_mse,
                            'avg_mae': avg_mae,
                            'avg_rmse': avg_rmse,
                            'fold_scores': scores,
                            'predictions': predictions,
                            'actuals': actuals
                        }
                        
                    except Exception as e:
                        self.logger.warning(f"⚠️ Time series CV failed for {model_name} on {target_name}: {e}")
                        continue
            
            self.validation_results['time_series_cv'] = ts_results
            self.logger.info("✅ Time series cross-validation completed")
            
        except Exception as e:
            self.logger.error(f"❌ Error in time series cross-validation: {e}")
    
    def k_fold_cross_validation(self):
        """Perform K-fold cross-validation."""
        try:
            self.logger.info("🔄 Performing K-fold cross-validation...")
            
            features_df = self.create_features()
            if features_df is None or len(features_df) < 10:
                self.logger.warning("⚠️ Insufficient data for K-fold CV")
                return
            
            # Define features and targets
            feature_columns = [col for col in features_df.columns 
                             if any(x in col for x in ['lag', 'ma', 'trend']) or col == 'period']
            X = features_df[feature_columns]
            
            targets = {
                'sum': features_df['sum'],
                'avg': features_df['avg'],
                'range': features_df['range'],
                'even_count': features_df['even_count']
            }
            
            models = {
                'RandomForest': RandomForestRegressor(n_estimators=50, random_state=42),
                'GradientBoosting': GradientBoostingRegressor(n_estimators=50, random_state=42),
                'LinearRegression': LinearRegression()
            }
            
            # K-fold split
            kfold = KFold(n_splits=5, shuffle=True, random_state=42)
            
            kfold_results = {}
            
            for target_name, y in targets.items():
                kfold_results[target_name] = {}
                
                for model_name, model in models.items():
                    try:
                        # Perform cross-validation
                        cv_scores = cross_val_score(model, X, y, cv=kfold, 
                                                  scoring='neg_mean_squared_error')
                        
                        kfold_results[target_name][model_name] = {
                            'cv_scores': (-cv_scores).tolist(),
                            'mean_mse': -cv_scores.mean(),
                            'std_mse': cv_scores.std(),
                            'mean_rmse': np.sqrt(-cv_scores.mean())
                        }
                        
                    except Exception as e:
                        self.logger.warning(f"⚠️ K-fold CV failed for {model_name} on {target_name}: {e}")
                        continue
            
            self.validation_results['k_fold_cv'] = kfold_results
            self.logger.info("✅ K-fold cross-validation completed")
            
        except Exception as e:
            self.logger.error(f"❌ Error in K-fold cross-validation: {e}")
    
    def walk_forward_validation(self):
        """Perform walk-forward validation."""
        try:
            self.logger.info("🚶 Performing walk-forward validation...")
            
            features_df = self.create_features()
            if features_df is None or len(features_df) < 10:
                self.logger.warning("⚠️ Insufficient data for walk-forward validation")
                return
            
            # Define features and targets
            feature_columns = [col for col in features_df.columns 
                             if any(x in col for x in ['lag', 'ma', 'trend']) or col == 'period']
            X = features_df[feature_columns]
            
            targets = {
                'sum': features_df['sum'],
                'avg': features_df['avg'],
                'range': features_df['range'],
                'even_count': features_df['even_count']
            }
            
            models = {
                'RandomForest': RandomForestRegressor(n_estimators=50, random_state=42),
                'GradientBoosting': GradientBoostingRegressor(n_estimators=50, random_state=42),
                'LinearRegression': LinearRegression()
            }
            
            # Walk-forward validation
            min_train_size = max(5, len(X) // 3)  # Minimum training size
            wf_results = {}
            
            for target_name, y in targets.items():
                wf_results[target_name] = {}
                
                for model_name, model in models.items():
                    try:
                        predictions = []
                        actuals = []
                        errors = []
                        
                        for i in range(min_train_size, len(X)):
                            # Training data: from start to current point
                            X_train = X.iloc[:i]
                            y_train = y.iloc[:i]
                            
                            # Test data: next point
                            X_test = X.iloc[i:i+1]
                            y_test = y.iloc[i:i+1]
                            
                            # Train and predict
                            if model_name == 'LinearRegression':
                                scaler = StandardScaler()
                                X_train_scaled = scaler.fit_transform(X_train)
                                X_test_scaled = scaler.transform(X_test)
                                
                                model.fit(X_train_scaled, y_train)
                                y_pred = model.predict(X_test_scaled)
                            else:
                                model.fit(X_train, y_train)
                                y_pred = model.predict(X_test)
                            
                            predictions.append(y_pred[0])
                            actuals.append(y_test.iloc[0])
                            errors.append(abs(y_pred[0] - y_test.iloc[0]))
                        
                        # Calculate metrics
                        mse = mean_squared_error(actuals, predictions)
                        mae = mean_absolute_error(actuals, predictions)
                        
                        wf_results[target_name][model_name] = {
                            'mse': mse,
                            'mae': mae,
                            'rmse': np.sqrt(mse),
                            'predictions': predictions,
                            'actuals': actuals,
                            'errors': errors,
                            'mean_error': np.mean(errors),
                            'std_error': np.std(errors)
                        }
                        
                    except Exception as e:
                        self.logger.warning(f"⚠️ Walk-forward validation failed for {model_name} on {target_name}: {e}")
                        continue
            
            self.validation_results['walk_forward'] = wf_results
            self.logger.info("✅ Walk-forward validation completed")
            
        except Exception as e:
            self.logger.error(f"❌ Error in walk-forward validation: {e}")
    
    def number_prediction_accuracy(self):
        """Evaluate accuracy of individual number predictions."""
        try:
            self.logger.info("🎯 Evaluating number prediction accuracy...")
            
            # Simple frequency-based prediction
            all_numbers = []
            for _, row in self.processed_data.iterrows():
                all_numbers.extend(row['numbers'])
            
            from collections import Counter
            number_freq = Counter(all_numbers)
            hot_numbers = [num for num, _ in number_freq.most_common(10)]
            
            # Test prediction accuracy on last few periods
            test_periods = min(5, len(self.processed_data) // 2)
            train_data = self.processed_data[:-test_periods]
            test_data = self.processed_data[-test_periods:]
            
            accuracy_results = {
                'frequency_based': {
                    'hot_numbers': hot_numbers,
                    'predictions': [],
                    'actuals': [],
                    'hit_rates': []
                }
            }
            
            for _, test_row in test_data.iterrows():
                actual_numbers = set(test_row['numbers'])
                predicted_numbers = set(hot_numbers[:6])  # Predict top 6 hot numbers
                
                hits = len(actual_numbers.intersection(predicted_numbers))
                hit_rate = hits / 6
                
                accuracy_results['frequency_based']['predictions'].append(list(predicted_numbers))
                accuracy_results['frequency_based']['actuals'].append(list(actual_numbers))
                accuracy_results['frequency_based']['hit_rates'].append(hit_rate)
            
            avg_hit_rate = np.mean(accuracy_results['frequency_based']['hit_rates'])
            accuracy_results['frequency_based']['average_hit_rate'] = avg_hit_rate
            
            self.prediction_accuracy = accuracy_results
            self.logger.info(f"✅ Number prediction accuracy: {avg_hit_rate:.2%}")
            
        except Exception as e:
            self.logger.error(f"❌ Error in number prediction accuracy: {e}")
    
    def run_comprehensive_validation(self):
        """Run all validation methods."""
        try:
            self.logger.info("🚀 Starting comprehensive cross-validation...")
            
            if self.processed_data is None or len(self.processed_data) == 0:
                self.logger.error("❌ No data available for validation")
                return False
            
            # Run all validation methods
            self.time_series_cross_validation()
            self.k_fold_cross_validation()
            self.walk_forward_validation()
            self.number_prediction_accuracy()
            
            # Generate validation report
            self.generate_validation_report()
            
            self.logger.info("🎉 Comprehensive cross-validation completed!")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Error in comprehensive validation: {e}")
            return False
    
    def generate_validation_report(self):
        """Generate comprehensive validation report."""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            
            # Create output directory
            os.makedirs('output/validation', exist_ok=True)
            
            # Combine all results
            full_results = {
                'validation_timestamp': timestamp,
                'data_records': len(self.processed_data),
                'validation_methods': list(self.validation_results.keys()),
                'cross_validation_results': self.validation_results,
                'prediction_accuracy': self.prediction_accuracy,
                'model_rankings': self.rank_models()
            }
            
            # Save detailed results
            report_file = f"output/validation/cross_validation_report_{timestamp}.json"
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(full_results, f, ensure_ascii=False, indent=2, default=str)
            
            # Generate summary
            summary = self.generate_validation_summary(full_results)
            summary_file = f"output/validation/validation_summary_{timestamp}.json"
            with open(summary_file, 'w', encoding='utf-8') as f:
                json.dump(summary, f, ensure_ascii=False, indent=2)
            
            self.logger.info(f"✅ Validation report saved:")
            self.logger.info(f"   📄 Detailed: {report_file}")
            self.logger.info(f"   📋 Summary: {summary_file}")
            
        except Exception as e:
            self.logger.error(f"❌ Error generating validation report: {e}")
    
    def rank_models(self):
        """Rank models based on validation performance."""
        rankings = {}
        
        try:
            for method_name, method_results in self.validation_results.items():
                rankings[method_name] = {}
                
                for target_name, target_results in method_results.items():
                    model_scores = []
                    
                    for model_name, model_results in target_results.items():
                        # Use MSE as primary ranking metric
                        if 'avg_mse' in model_results:
                            score = model_results['avg_mse']
                        elif 'mean_mse' in model_results:
                            score = model_results['mean_mse']
                        elif 'mse' in model_results:
                            score = model_results['mse']
                        else:
                            continue
                        
                        model_scores.append((model_name, score))
                    
                    # Sort by score (lower is better for MSE)
                    model_scores.sort(key=lambda x: x[1])
                    rankings[method_name][target_name] = [
                        {'model': name, 'score': score, 'rank': i+1}
                        for i, (name, score) in enumerate(model_scores)
                    ]
        
        except Exception as e:
            self.logger.warning(f"⚠️ Error ranking models: {e}")
        
        return rankings
    
    def generate_validation_summary(self, full_results):
        """Generate validation summary."""
        summary = {
            'validation_timestamp': full_results['validation_timestamp'],
            'data_records': full_results['data_records'],
            'validation_methods_used': full_results['validation_methods'],
            'best_models_by_target': {},
            'overall_insights': {}
        }
        
        try:
            # Find best models for each target
            rankings = full_results.get('model_rankings', {})
            
            for method_name, method_rankings in rankings.items():
                for target_name, target_rankings in method_rankings.items():
                    if target_rankings:
                        best_model = target_rankings[0]  # First is best (lowest score)
                        
                        if target_name not in summary['best_models_by_target']:
                            summary['best_models_by_target'][target_name] = {}
                        
                        summary['best_models_by_target'][target_name][method_name] = {
                            'model': best_model['model'],
                            'score': best_model['score']
                        }
            
            # Overall insights
            if 'prediction_accuracy' in full_results:
                pred_acc = full_results['prediction_accuracy']
                if 'frequency_based' in pred_acc:
                    summary['overall_insights']['number_prediction_hit_rate'] = pred_acc['frequency_based'].get('average_hit_rate', 0)
            
        except Exception as e:
            self.logger.warning(f"⚠️ Error generating summary: {e}")
        
        return summary

def main():
    """Main function to run cross-validation."""
    # Use existing data file
    data_file = "output/lottery_data_20250704_181955.csv"
    
    if not os.path.exists(data_file):
        print(f"❌ Data file not found: {data_file}")
        return False
    
    # Create cross-validator
    validator = LotteryCrossValidator(data_file)
    
    # Run comprehensive validation
    success = validator.run_comprehensive_validation()
    
    if success:
        print("✅ Cross-validation completed successfully!")
        print("📊 Check output/validation/ directory for detailed results")
    else:
        print("❌ Cross-validation failed!")
    
    return success

if __name__ == "__main__":
    main()
