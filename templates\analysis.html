{% extends "base.html" %}
{% block content %}
<h1>🔍 Analysis</h1>
<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5>Comprehensive Analysis</h5>
                <button class="btn btn-primary" onclick="runAnalysis()">Run Analysis</button>
            </div>
            <div class="card-body">
                <div id="analysisResults"></div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
{% block scripts %}
<script>
function runAnalysis() {
    document.getElementById('analysisResults').innerHTML = '<div class="text-center"><div class="spinner-border" role="status"></div><p>Running analysis...</p></div>';

    fetch('/api/analysis/run')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                let html = '<div class="row">';

                // Basic Statistics
                if (data.results.basic_stats) {
                    html += '<div class="col-md-6"><div class="card mb-3"><div class="card-header"><h6>Basic Statistics</h6></div><div class="card-body">';
                    html += `<p><strong>Total Records:</strong> ${data.results.basic_stats.total_records}</p>`;
                    html += `<p><strong>Total Numbers:</strong> ${data.results.basic_stats.total_numbers}</p>`;
                    html += `<p><strong>Unique Numbers:</strong> ${data.results.basic_stats.unique_numbers}</p>`;
                    html += `<p><strong>Average Frequency:</strong> ${data.results.basic_stats.average_frequency}</p>`;
                    html += '</div></div></div>';
                }

                // Number Frequency
                if (data.results.number_frequency) {
                    html += '<div class="col-md-6"><div class="card mb-3"><div class="card-header"><h6>Top Numbers</h6></div><div class="card-body">';
                    data.results.number_frequency.most_common.forEach(item => {
                        html += `<p>Number ${item[0]}: ${item[1]} times</p>`;
                    });
                    html += '</div></div></div>';
                }

                html += '</div>';
                document.getElementById('analysisResults').innerHTML = html;
            } else {
                document.getElementById('analysisResults').innerHTML = '<div class="alert alert-danger">Error: ' + data.error + '</div>';
            }
        })
        .catch(error => {
            document.getElementById('analysisResults').innerHTML = '<div class="alert alert-danger">Error running analysis</div>';
        });
}
</script>
{% endblock %}