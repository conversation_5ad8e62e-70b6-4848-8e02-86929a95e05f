#!/usr/bin/env python3
"""
Simple runner script for the lottery scraper.
This script provides an easy way to run the scraper with different options.
"""

import sys
import os
import argparse
from lottery_scraper import LotteryScraper
import config

def main():
    parser = argparse.ArgumentParser(description='Lottery Records Web Scraper')
    parser.add_argument('--url', default=config.TARGET_URL, help='URL to scrape')
    parser.add_argument('--output', default=config.OUTPUT_DIR, help='Output directory')
    parser.add_argument('--headless', action='store_true', default=config.HEADLESS_MODE, 
                       help='Run browser in headless mode')
    parser.add_argument('--max-pages', type=int, default=config.MAX_PAGES, 
                       help='Maximum number of pages to scrape')
    parser.add_argument('--delay', type=float, default=config.DELAY_BETWEEN_PAGES,
                       help='Delay between page requests (seconds)')
    
    args = parser.parse_args()
    
    print("🚀 Starting Lottery Data Scraper")
    print(f"📍 Target URL: {args.url}")
    print(f"📁 Output Directory: {args.output}")
    print(f"🔧 Headless Mode: {args.headless}")
    print(f"📄 Max Pages: {args.max_pages}")
    print("-" * 50)
    
    # Create scraper instance
    scraper = LotteryScraper(args.url, args.output)
    
    # Update configuration based on arguments
    scraper.headless_mode = args.headless
    scraper.max_pages = args.max_pages
    scraper.delay_between_pages = args.delay
    
    # Run the scraping process
    try:
        success = scraper.run()
        
        if success:
            print("\n✅ Scraping completed successfully!")
            print("📊 Check the output directory for your data files.")
        else:
            print("\n❌ Scraping failed.")
            print("📋 Check the log files in the output directory for details.")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n⏹️  Scraping interrupted by user.")
        sys.exit(0)
    except Exception as e:
        print(f"\n💥 Unexpected error: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
