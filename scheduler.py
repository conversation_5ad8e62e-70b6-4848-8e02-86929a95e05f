#!/usr/bin/env python3
"""
Automated Scheduler for Lottery Data Scraping - APScheduler implementation.
"""

import os
import sys
import json
import logging
from datetime import datetime, timedelta
from pathlib import Path
from apscheduler.schedulers.blocking import BlockingScheduler
from apscheduler.schedulers.background import BackgroundScheduler
from apscheduler.triggers.cron import CronTrigger
from apscheduler.triggers.interval import IntervalTrigger
# from apscheduler.jobstores.sqlalchemy import SQLAlchemyJobStore  # Not used to avoid serialization issues
from apscheduler.executors.pool import ThreadPoolExecutor

# Import our modules
from lottery_scraper import LotteryScraper
from database_manager import LotteryDatabaseManager
from data_analyzer import LotteryDataAnalyzer

class LotteryScheduler:
    """Automated scheduler for lottery data scraping and analysis."""
    
    def __init__(self, config_file="scheduler_config.json"):
        """Initialize the scheduler."""
        self.config_file = config_file
        self.config = self.load_config()
        self.setup_logging()
        self.setup_scheduler()
        
        # Initialize components
        self.scraper = LotteryScraper()
        self.db_manager = LotteryDatabaseManager()
        self.analyzer = LotteryDataAnalyzer()
        
        self.logger.info("🕐 Lottery Scheduler initialized")
    
    def load_config(self):
        """Load scheduler configuration."""
        default_config = {
            "scraping": {
                "enabled": True,
                "interval_minutes": 60,
                "cron_schedule": "0 */2 * * *",  # Every 2 hours
                "max_retries": 3,
                "retry_delay_minutes": 5
            },
            "analysis": {
                "enabled": True,
                "daily_time": "23:30",  # Daily at 23:30
                "generate_reports": True,
                "create_visualizations": True
            },
            "notifications": {
                "enabled": False,
                "email": "",
                "webhook_url": ""
            },
            "database": {
                "cleanup_enabled": True,
                "cleanup_days": 365,  # Keep data for 1 year
                "backup_enabled": True,
                "backup_interval_days": 7
            },
            "logging": {
                "level": "INFO",
                "file": "logs/scheduler.log",
                "max_size_mb": 10,
                "backup_count": 5
            }
        }
        
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    user_config = json.load(f)
                # Merge with defaults
                default_config.update(user_config)
            except Exception as e:
                print(f"⚠️ Error loading config, using defaults: {e}")
        else:
            # Save default config
            self.save_config(default_config)
        
        return default_config
    
    def save_config(self, config=None):
        """Save configuration to file."""
        if config is None:
            config = self.config

        config_dir = os.path.dirname(self.config_file)
        if config_dir:  # Only create directory if there is a directory path
            os.makedirs(config_dir, exist_ok=True)

        with open(self.config_file, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
    
    def setup_logging(self):
        """Setup logging configuration."""
        log_config = self.config.get('logging', {})
        log_file = log_config.get('file', 'logs/scheduler.log')
        log_level = getattr(logging, log_config.get('level', 'INFO'))
        
        # Create logs directory
        os.makedirs(os.path.dirname(log_file), exist_ok=True)
        
        # Configure logging
        logging.basicConfig(
            level=log_level,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file, encoding='utf-8'),
                logging.StreamHandler(sys.stdout)
            ]
        )
        
        self.logger = logging.getLogger('LotteryScheduler')
    
    def setup_scheduler(self):
        """Setup APScheduler with memory job store."""
        # Configure executors
        executors = {
            'default': ThreadPoolExecutor(20)
        }

        # Job defaults
        job_defaults = {
            'coalesce': False,
            'max_instances': 1,
            'misfire_grace_time': 300  # 5 minutes
        }

        # Create scheduler with memory job store to avoid serialization issues
        self.scheduler = BackgroundScheduler(
            executors=executors,
            job_defaults=job_defaults,
            timezone='Asia/Shanghai'
        )
    
    def scheduled_scraping_job(self):
        """Scheduled job for data scraping."""
        job_id = f"scraping_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        self.logger.info(f"🕐 Starting scheduled scraping job: {job_id}")
        
        try:
            # Run scraping
            success = self.scraper.run()
            
            if success:
                # Get the latest scraped file
                output_dir = Path("output")
                csv_files = list(output_dir.glob("lottery_data_*.csv"))
                if csv_files:
                    latest_file = max(csv_files, key=os.path.getctime)
                    
                    # Import to database
                    self.db_manager.insert_lottery_data(str(latest_file))
                    
                    self.logger.info(f"✅ Scheduled scraping completed successfully: {job_id}")
                    self.send_notification(f"Scraping completed: {latest_file.name}")
                else:
                    self.logger.warning("⚠️ No scraped files found")
            else:
                self.logger.error(f"❌ Scheduled scraping failed: {job_id}")
                self.send_notification("Scraping failed", is_error=True)
                
        except Exception as e:
            self.logger.error(f"❌ Error in scheduled scraping: {e}")
            self.send_notification(f"Scraping error: {str(e)}", is_error=True)
    
    def scheduled_analysis_job(self):
        """Scheduled job for data analysis."""
        job_id = f"analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        self.logger.info(f"🕐 Starting scheduled analysis job: {job_id}")
        
        try:
            # Export data for analysis
            export_file = self.db_manager.export_to_csv()
            if export_file:
                # Run analysis
                if self.analyzer.load_data(export_file):
                    results = self.analyzer.generate_comprehensive_report()
                    
                    if self.config['analysis']['generate_reports']:
                        self.analyzer.save_analysis_report()
                    
                    if self.config['analysis']['create_visualizations']:
                        self.analyzer.create_visualizations()
                    
                    self.logger.info(f"✅ Scheduled analysis completed: {job_id}")
                    self.send_notification("Daily analysis completed")
                else:
                    self.logger.error("❌ Failed to load data for analysis")
            else:
                self.logger.error("❌ Failed to export data for analysis")
                
        except Exception as e:
            self.logger.error(f"❌ Error in scheduled analysis: {e}")
            self.send_notification(f"Analysis error: {str(e)}", is_error=True)
    
    def scheduled_cleanup_job(self):
        """Scheduled job for database cleanup."""
        if not self.config['database']['cleanup_enabled']:
            return
        
        self.logger.info("🕐 Starting scheduled cleanup job")
        
        try:
            cleanup_days = self.config['database']['cleanup_days']
            cutoff_date = datetime.now() - timedelta(days=cleanup_days)
            
            # This would implement actual cleanup logic
            self.logger.info(f"✅ Cleanup completed (would remove data older than {cutoff_date})")
            
        except Exception as e:
            self.logger.error(f"❌ Error in cleanup job: {e}")
    
    def scheduled_backup_job(self):
        """Scheduled job for database backup."""
        if not self.config['database']['backup_enabled']:
            return
        
        self.logger.info("🕐 Starting scheduled backup job")
        
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_file = f"backups/lottery_backup_{timestamp}.csv"
            
            # Create backup directory
            os.makedirs("backups", exist_ok=True)
            
            # Export data as backup
            export_file = self.db_manager.export_to_csv(backup_file)
            if export_file:
                self.logger.info(f"✅ Backup completed: {backup_file}")
                self.send_notification(f"Database backup created: {backup_file}")
            else:
                self.logger.error("❌ Backup failed")
                
        except Exception as e:
            self.logger.error(f"❌ Error in backup job: {e}")
    
    def send_notification(self, message, is_error=False):
        """Send notification if enabled."""
        if not self.config['notifications']['enabled']:
            return
        
        try:
            # This would implement actual notification logic
            # For now, just log the notification
            level = "ERROR" if is_error else "INFO"
            self.logger.info(f"📧 NOTIFICATION [{level}]: {message}")
            
        except Exception as e:
            self.logger.error(f"❌ Error sending notification: {e}")
    
    def add_jobs(self):
        """Add all scheduled jobs."""
        scraping_config = self.config['scraping']
        analysis_config = self.config['analysis']
        
        # Add scraping job
        if scraping_config['enabled']:
            if 'cron_schedule' in scraping_config:
                # Parse cron schedule (minute hour day month day_of_week)
                cron_parts = scraping_config['cron_schedule'].split()
                if len(cron_parts) == 5:
                    self.scheduler.add_job(
                        self.scheduled_scraping_job,
                        CronTrigger(
                            minute=cron_parts[0],
                            hour=cron_parts[1],
                            day=cron_parts[2],
                            month=cron_parts[3],
                            day_of_week=cron_parts[4]
                        ),
                        id='scraping_job',
                        replace_existing=True
                    )
                    self.logger.info(f"📅 Scraping job scheduled: {scraping_config['cron_schedule']}")
            else:
                # Use interval
                self.scheduler.add_job(
                    self.scheduled_scraping_job,
                    IntervalTrigger(minutes=scraping_config['interval_minutes']),
                    id='scraping_job',
                    replace_existing=True
                )
                self.logger.info(f"📅 Scraping job scheduled: every {scraping_config['interval_minutes']} minutes")
        
        # Add analysis job
        if analysis_config['enabled']:
            daily_time = analysis_config['daily_time'].split(':')
            if len(daily_time) == 2:
                self.scheduler.add_job(
                    self.scheduled_analysis_job,
                    CronTrigger(hour=int(daily_time[0]), minute=int(daily_time[1])),
                    id='analysis_job',
                    replace_existing=True
                )
                self.logger.info(f"📅 Analysis job scheduled: daily at {analysis_config['daily_time']}")
        
        # Add cleanup job (weekly)
        if self.config['database']['cleanup_enabled']:
            self.scheduler.add_job(
                self.scheduled_cleanup_job,
                CronTrigger(day_of_week=0, hour=2, minute=0),  # Sunday 2:00 AM
                id='cleanup_job',
                replace_existing=True
            )
            self.logger.info("📅 Cleanup job scheduled: weekly on Sunday 2:00 AM")
        
        # Add backup job
        if self.config['database']['backup_enabled']:
            interval_days = self.config['database']['backup_interval_days']
            self.scheduler.add_job(
                self.scheduled_backup_job,
                IntervalTrigger(days=interval_days),
                id='backup_job',
                replace_existing=True
            )
            self.logger.info(f"📅 Backup job scheduled: every {interval_days} days")
    
    def start(self):
        """Start the scheduler."""
        try:
            self.add_jobs()
            self.scheduler.start()
            self.logger.info("🚀 Scheduler started successfully")
            
            # Print job status
            jobs = self.scheduler.get_jobs()
            if jobs:
                self.logger.info("📋 Active jobs:")
                for job in jobs:
                    self.logger.info(f"  - {job.id}: {job.next_run_time}")
            else:
                self.logger.warning("⚠️ No jobs scheduled")
                
        except Exception as e:
            self.logger.error(f"❌ Error starting scheduler: {e}")
            raise
    
    def stop(self):
        """Stop the scheduler."""
        try:
            self.scheduler.shutdown()
            self.logger.info("🛑 Scheduler stopped")
        except Exception as e:
            self.logger.error(f"❌ Error stopping scheduler: {e}")
    
    def get_job_status(self):
        """Get status of all jobs."""
        jobs = self.scheduler.get_jobs()
        status = []
        
        for job in jobs:
            status.append({
                'id': job.id,
                'name': job.name or job.id,
                'next_run': job.next_run_time.isoformat() if job.next_run_time else None,
                'trigger': str(job.trigger)
            })
        
        return status
    
    def run_job_now(self, job_id):
        """Run a specific job immediately."""
        try:
            job = self.scheduler.get_job(job_id)
            if job:
                job.func()
                self.logger.info(f"✅ Job {job_id} executed manually")
                return True
            else:
                self.logger.error(f"❌ Job {job_id} not found")
                return False
        except Exception as e:
            self.logger.error(f"❌ Error running job {job_id}: {e}")
            return False

def main():
    """Main function for command-line usage."""
    import argparse
    
    parser = argparse.ArgumentParser(description='Lottery Data Scheduler')
    parser.add_argument('--config', default='scheduler_config.json', help='Configuration file path')
    parser.add_argument('--daemon', action='store_true', help='Run as daemon')
    parser.add_argument('--run-job', help='Run specific job immediately')
    parser.add_argument('--status', action='store_true', help='Show job status')
    
    args = parser.parse_args()
    
    scheduler = LotteryScheduler(args.config)
    
    if args.status:
        status = scheduler.get_job_status()
        print("📋 Job Status:")
        for job in status:
            print(f"  {job['id']}: Next run at {job['next_run']}")
        return
    
    if args.run_job:
        success = scheduler.run_job_now(args.run_job)
        if success:
            print(f"✅ Job {args.run_job} completed")
        else:
            print(f"❌ Job {args.run_job} failed")
        return
    
    try:
        scheduler.start()
        
        if args.daemon:
            # Keep running
            import time
            while True:
                time.sleep(60)
        else:
            # Run for a short time for testing
            import time
            print("🕐 Scheduler running... Press Ctrl+C to stop")
            time.sleep(10)
            scheduler.stop()
            
    except KeyboardInterrupt:
        print("\n🛑 Stopping scheduler...")
        scheduler.stop()

if __name__ == "__main__":
    main()
