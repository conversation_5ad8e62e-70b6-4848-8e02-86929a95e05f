{"frequency": {"number_frequency": {"7": 3, "45": 3, "3": 3, "30": 2, "1": 3, "10": 5, "44": 2, "4": 2, "47": 5, "49": 2, "14": 4, "15": 5, "43": 5, "6": 6, "16": 2, "9": 3, "46": 3, "35": 4, "41": 4, "48": 4, "17": 5, "34": 2, "5": 3, "31": 3, "13": 3, "33": 3, "19": 3, "36": 1, "40": 2, "11": 3, "37": 2, "8": 2, "39": 5, "18": 1}, "special_frequency": {"6": 1, "34": 1, "45": 1, "13": 1, "40": 1, "43": 1, "10": 2, "11": 2, "44": 2, "31": 1, "15": 1, "33": 1, "3": 1, "38": 1, "36": 1}, "position_frequency": {"position_1": {"7": 2, "44": 1, "15": 2, "14": 2, "41": 1, "49": 1, "33": 1, "47": 2, "10": 1, "48": 1, "35": 1, "6": 1, "19": 1, "18": 1}, "position_2": {"45": 1, "4": 1, "43": 2, "46": 1, "48": 2, "5": 1, "6": 1, "36": 1, "8": 1, "40": 1, "35": 1, "11": 1, "16": 1, "41": 2, "15": 1}, "position_3": {"3": 3, "47": 1, "6": 2, "10": 1, "1": 1, "17": 2, "5": 2, "40": 1, "39": 2, "31": 1, "8": 1, "9": 1}, "position_4": {"30": 2, "1": 1, "35": 1, "17": 2, "13": 2, "19": 1, "43": 1, "33": 1, "48": 1, "10": 1, "37": 1, "41": 1, "46": 1, "39": 1, "14": 1}, "position_5": {"1": 1, "49": 1, "16": 1, "43": 1, "34": 2, "31": 1, "13": 1, "11": 1, "47": 2, "45": 2, "15": 2, "6": 1, "46": 1, "7": 1}, "position_6": {"10": 2, "14": 1, "9": 2, "43": 1, "6": 1, "31": 1, "17": 1, "37": 1, "44": 1, "4": 1, "11": 1, "33": 1, "39": 2, "19": 1, "35": 1}}, "pair_frequency": {"(6, 15)": 3, "(13, 17)": 3, "(15, 35)": 3, "(15, 39)": 3, "(35, 39)": 3, "(10, 45)": 2, "(3, 10)": 2, "(44, 47)": 2, "(4, 47)": 2, "(1, 49)": 2, "(15, 43)": 2, "(9, 15)": 2, "(6, 16)": 2, "(6, 9)": 2, "(10, 14)": 2, "(35, 46)": 2, "(35, 43)": 2, "(10, 43)": 2, "(17, 48)": 2, "(10, 17)": 2}, "triple_frequency": {"(15, 35, 39)": 3, "(6, 9, 15)": 2, "(6, 17, 31)": 2, "(11, 39, 45)": 2, "(6, 15, 46)": 2, "(3, 7, 45)": 1, "(7, 30, 45)": 1, "(1, 7, 45)": 1, "(7, 10, 45)": 1, "(3, 7, 30)": 1}, "hot_numbers": [6, 10, 47, 15, 43, 17, 39, 14, 35, 41], "cold_numbers": [44, 4, 49, 16, 34, 40, 37, 8, 36, 18]}, "markov": {"first_order_transitions": {"1": {"1": 0.05555555555555555, "4": 0.05555555555555555, "44": 0.05555555555555555, "14": 0.05555555555555555, "47": 0.05555555555555555, "49": 0.05555555555555555, "6": 0.1111111111111111, "9": 0.05555555555555555, "43": 0.05555555555555555, "15": 0.05555555555555555, "16": 0.05555555555555555, "30": 0.05555555555555555, "34": 0.05555555555555555, "7": 0.05555555555555555, "13": 0.05555555555555555, "17": 0.05555555555555555, "31": 0.05555555555555555}, "3": {"1": 0.05555555555555555, "4": 0.05555555555555555, "44": 0.05555555555555555, "14": 0.05555555555555555, "47": 0.05555555555555555, "49": 0.05555555555555555, "34": 0.05555555555555555, "41": 0.05555555555555555, "10": 0.05555555555555555, "43": 0.05555555555555555, "48": 0.1111111111111111, "17": 0.05555555555555555, "35": 0.05555555555555555, "39": 0.05555555555555555, "11": 0.05555555555555555, "45": 0.05555555555555555, "15": 0.05555555555555555}, "7": {"1": 0.08333333333333333, "4": 0.08333333333333333, "44": 0.08333333333333333, "14": 0.08333333333333333, "47": 0.08333333333333333, "49": 0.08333333333333333, "33": 0.08333333333333333, "5": 0.08333333333333333, "13": 0.08333333333333333, "48": 0.08333333333333333, "17": 0.08333333333333333, "19": 0.08333333333333333}, "10": {"1": 0.06666666666666667, "4": 0.06666666666666667, "44": 0.03333333333333333, "14": 0.03333333333333333, "47": 0.06666666666666667, "49": 0.06666666666666667, "34": 0.03333333333333333, "41": 0.03333333333333333, "10": 0.03333333333333333, "43": 0.06666666666666667, "48": 0.06666666666666667, "17": 0.06666666666666667, "5": 0.03333333333333333, "6": 0.03333333333333333, "31": 0.06666666666666667, "33": 0.03333333333333333, "3": 0.03333333333333333, "40": 0.03333333333333333, "35": 0.03333333333333333, "37": 0.03333333333333333, "39": 0.03333333333333333, "15": 0.03333333333333333}, "45": {"1": 0.05555555555555555, "4": 0.05555555555555555, "44": 0.05555555555555555, "14": 0.1111111111111111, "47": 0.05555555555555555, "49": 0.05555555555555555, "33": 0.05555555555555555, "39": 0.1111111111111111, "10": 0.05555555555555555, "11": 0.05555555555555555, "45": 0.05555555555555555, "35": 0.05555555555555555, "37": 0.05555555555555555, "43": 0.05555555555555555, "15": 0.05555555555555555, "31": 0.05555555555555555}, "30": {"1": 0.08333333333333333, "4": 0.08333333333333333, "44": 0.08333333333333333, "14": 0.16666666666666666, "47": 0.08333333333333333, "49": 0.08333333333333333, "35": 0.08333333333333333, "3": 0.08333333333333333, "10": 0.08333333333333333, "43": 0.08333333333333333, "46": 0.08333333333333333}, "4": {"6": 0.08333333333333333, "9": 0.08333333333333333, "43": 0.08333333333333333, "15": 0.16666666666666666, "16": 0.08333333333333333, "30": 0.08333333333333333, "35": 0.08333333333333333, "39": 0.08333333333333333, "11": 0.08333333333333333, "45": 0.08333333333333333, "48": 0.08333333333333333}, "44": {"6": 0.08333333333333333, "9": 0.08333333333333333, "43": 0.08333333333333333, "15": 0.08333333333333333, "16": 0.08333333333333333, "30": 0.08333333333333333, "33": 0.08333333333333333, "3": 0.08333333333333333, "4": 0.08333333333333333, "40": 0.08333333333333333, "47": 0.08333333333333333, "48": 0.08333333333333333}, "14": {"6": 0.05555555555555555, "9": 0.05555555555555555, "43": 0.16666666666666666, "15": 0.1111111111111111, "16": 0.05555555555555555, "30": 0.05555555555555555, "34": 0.05555555555555555, "41": 0.05555555555555555, "10": 0.05555555555555555, "48": 0.05555555555555555, "17": 0.05555555555555555, "35": 0.05555555555555555, "37": 0.05555555555555555, "39": 0.05555555555555555, "31": 0.05555555555555555}, "47": {"6": 0.06666666666666667, "9": 0.06666666666666667, "43": 0.03333333333333333, "15": 0.1, "16": 0.03333333333333333, "30": 0.03333333333333333, "8": 0.03333333333333333, "10": 0.03333333333333333, "44": 0.03333333333333333, "13": 0.03333333333333333, "47": 0.06666666666666667, "17": 0.03333333333333333, "33": 0.03333333333333333, "3": 0.03333333333333333, "4": 0.03333333333333333, "40": 0.03333333333333333, "48": 0.06666666666666667, "35": 0.03333333333333333, "39": 0.03333333333333333, "11": 0.03333333333333333, "45": 0.03333333333333333, "5": 0.03333333333333333, "41": 0.03333333333333333, "46": 0.03333333333333333}, "49": {"6": 0.16666666666666666, "9": 0.08333333333333333, "43": 0.08333333333333333, "15": 0.08333333333333333, "16": 0.08333333333333333, "30": 0.08333333333333333, "34": 0.08333333333333333, "7": 0.08333333333333333, "13": 0.08333333333333333, "17": 0.08333333333333333, "31": 0.08333333333333333}, "6": {"35": 0.05555555555555555, "3": 0.027777777777777776, "10": 0.027777777777777776, "43": 0.027777777777777776, "46": 0.08333333333333333, "14": 0.05555555555555555, "34": 0.027777777777777776, "6": 0.08333333333333333, "7": 0.05555555555555555, "13": 0.05555555555555555, "17": 0.05555555555555555, "31": 0.027777777777777776, "33": 0.027777777777777776, "5": 0.05555555555555555, "48": 0.027777777777777776, "19": 0.05555555555555555, "41": 0.05555555555555555, "9": 0.05555555555555555, "15": 0.05555555555555555, "39": 0.05555555555555555, "18": 0.027777777777777776}, "9": {"35": 0.16666666666666666, "3": 0.08333333333333333, "10": 0.08333333333333333, "43": 0.08333333333333333, "46": 0.16666666666666666, "14": 0.08333333333333333, "6": 0.08333333333333333, "39": 0.08333333333333333, "15": 0.08333333333333333, "19": 0.08333333333333333}, "43": {"35": 0.03333333333333333, "3": 0.03333333333333333, "10": 0.1, "43": 0.06666666666666667, "46": 0.03333333333333333, "14": 0.03333333333333333, "34": 0.03333333333333333, "41": 0.06666666666666667, "48": 0.03333333333333333, "17": 0.1, "1": 0.03333333333333333, "5": 0.03333333333333333, "6": 0.06666666666666667, "49": 0.03333333333333333, "31": 0.03333333333333333, "8": 0.06666666666666667, "44": 0.03333333333333333, "13": 0.03333333333333333, "47": 0.06666666666666667, "16": 0.03333333333333333, "19": 0.03333333333333333}, "15": {"35": 0.06666666666666667, "3": 0.03333333333333333, "10": 0.06666666666666667, "43": 0.03333333333333333, "46": 0.06666666666666667, "14": 0.1, "33": 0.03333333333333333, "39": 0.1, "11": 0.03333333333333333, "45": 0.03333333333333333, "6": 0.06666666666666667, "8": 0.03333333333333333, "41": 0.06666666666666667, "47": 0.03333333333333333, "16": 0.03333333333333333, "19": 0.06666666666666667, "15": 0.03333333333333333, "7": 0.03333333333333333, "9": 0.03333333333333333, "18": 0.03333333333333333}, "16": {"35": 0.08333333333333333, "3": 0.08333333333333333, "10": 0.08333333333333333, "43": 0.08333333333333333, "46": 0.16666666666666666, "14": 0.08333333333333333, "5": 0.08333333333333333, "6": 0.08333333333333333, "41": 0.08333333333333333, "9": 0.08333333333333333, "15": 0.08333333333333333}, "35": {"34": 0.041666666666666664, "41": 0.125, "10": 0.08333333333333333, "43": 0.041666666666666664, "48": 0.041666666666666664, "17": 0.041666666666666664, "33": 0.041666666666666664, "39": 0.08333333333333333, "11": 0.041666666666666664, "45": 0.041666666666666664, "14": 0.08333333333333333, "6": 0.041666666666666664, "8": 0.041666666666666664, "47": 0.041666666666666664, "16": 0.041666666666666664, "19": 0.041666666666666664, "7": 0.041666666666666664, "9": 0.041666666666666664, "18": 0.041666666666666664}, "46": {"34": 0.05555555555555555, "41": 0.1111111111111111, "10": 0.05555555555555555, "43": 0.05555555555555555, "48": 0.05555555555555555, "17": 0.05555555555555555, "35": 0.05555555555555555, "6": 0.05555555555555555, "39": 0.1111111111111111, "46": 0.05555555555555555, "15": 0.05555555555555555, "19": 0.05555555555555555, "7": 0.05555555555555555, "9": 0.05555555555555555, "14": 0.05555555555555555, "18": 0.05555555555555555}, "34": {"1": 0.08333333333333333, "5": 0.16666666666666666, "6": 0.08333333333333333, "49": 0.08333333333333333, "17": 0.16666666666666666, "31": 0.08333333333333333, "33": 0.08333333333333333, "13": 0.08333333333333333, "48": 0.08333333333333333, "19": 0.08333333333333333}, "41": {"1": 0.05555555555555555, "5": 0.1111111111111111, "6": 0.16666666666666666, "49": 0.05555555555555555, "17": 0.05555555555555555, "31": 0.05555555555555555, "41": 0.05555555555555555, "9": 0.05555555555555555, "46": 0.1111111111111111, "15": 0.1111111111111111, "35": 0.05555555555555555, "39": 0.05555555555555555, "19": 0.05555555555555555}, "48": {"1": 0.041666666666666664, "5": 0.041666666666666664, "6": 0.041666666666666664, "49": 0.041666666666666664, "17": 0.041666666666666664, "31": 0.041666666666666664, "36": 0.041666666666666664, "37": 0.041666666666666664, "40": 0.041666666666666664, "11": 0.125, "43": 0.041666666666666664, "47": 0.041666666666666664, "35": 0.041666666666666664, "39": 0.08333333333333333, "45": 0.08333333333333333, "15": 0.041666666666666664, "48": 0.041666666666666664, "33": 0.041666666666666664, "10": 0.041666666666666664, "14": 0.041666666666666664}, "17": {"1": 0.03333333333333333, "5": 0.06666666666666667, "6": 0.06666666666666667, "49": 0.03333333333333333, "17": 0.1, "31": 0.06666666666666667, "34": 0.03333333333333333, "7": 0.03333333333333333, "13": 0.06666666666666667, "33": 0.06666666666666667, "48": 0.06666666666666667, "19": 0.03333333333333333, "36": 0.03333333333333333, "37": 0.03333333333333333, "40": 0.06666666666666667, "11": 0.03333333333333333, "43": 0.03333333333333333, "47": 0.06666666666666667, "3": 0.03333333333333333, "4": 0.03333333333333333}, "5": {"34": 0.05555555555555555, "6": 0.1111111111111111, "7": 0.05555555555555555, "13": 0.05555555555555555, "17": 0.05555555555555555, "31": 0.05555555555555555, "36": 0.05555555555555555, "37": 0.05555555555555555, "40": 0.05555555555555555, "11": 0.05555555555555555, "43": 0.05555555555555555, "47": 0.05555555555555555, "35": 0.05555555555555555, "39": 0.05555555555555555, "46": 0.05555555555555555, "15": 0.05555555555555555, "19": 0.05555555555555555}, "31": {"34": 0.05555555555555555, "6": 0.1111111111111111, "7": 0.05555555555555555, "13": 0.1111111111111111, "17": 0.1111111111111111, "31": 0.05555555555555555, "33": 0.05555555555555555, "5": 0.05555555555555555, "48": 0.05555555555555555, "19": 0.1111111111111111, "8": 0.05555555555555555, "41": 0.05555555555555555, "47": 0.05555555555555555, "16": 0.05555555555555555}, "13": {"33": 0.1111111111111111, "5": 0.05555555555555555, "13": 0.05555555555555555, "48": 0.1111111111111111, "17": 0.05555555555555555, "19": 0.05555555555555555, "36": 0.05555555555555555, "37": 0.05555555555555555, "40": 0.1111111111111111, "11": 0.05555555555555555, "43": 0.05555555555555555, "47": 0.1111111111111111, "3": 0.05555555555555555, "4": 0.05555555555555555}, "33": {"36": 0.05555555555555555, "37": 0.1111111111111111, "40": 0.05555555555555555, "11": 0.1111111111111111, "43": 0.1111111111111111, "47": 0.05555555555555555, "35": 0.1111111111111111, "39": 0.1111111111111111, "45": 0.05555555555555555, "15": 0.1111111111111111, "48": 0.05555555555555555, "31": 0.05555555555555555}, "19": {"36": 0.05555555555555555, "37": 0.05555555555555555, "40": 0.05555555555555555, "11": 0.05555555555555555, "43": 0.05555555555555555, "47": 0.05555555555555555, "5": 0.05555555555555555, "6": 0.05555555555555555, "41": 0.1111111111111111, "9": 0.1111111111111111, "46": 0.05555555555555555, "15": 0.05555555555555555, "7": 0.05555555555555555, "39": 0.05555555555555555, "14": 0.05555555555555555, "18": 0.05555555555555555}, "36": {"8": 0.16666666666666666, "10": 0.16666666666666666, "44": 0.16666666666666666, "13": 0.16666666666666666, "47": 0.16666666666666666, "17": 0.16666666666666666}, "37": {"8": 0.16666666666666666, "10": 0.08333333333333333, "44": 0.08333333333333333, "13": 0.08333333333333333, "47": 0.16666666666666666, "17": 0.08333333333333333, "6": 0.08333333333333333, "41": 0.08333333333333333, "16": 0.08333333333333333, "19": 0.08333333333333333}, "40": {"8": 0.08333333333333333, "10": 0.08333333333333333, "44": 0.08333333333333333, "13": 0.08333333333333333, "47": 0.08333333333333333, "17": 0.08333333333333333, "35": 0.08333333333333333, "39": 0.08333333333333333, "11": 0.08333333333333333, "45": 0.08333333333333333, "15": 0.08333333333333333, "48": 0.08333333333333333}, "11": {"8": 0.05555555555555555, "10": 0.1111111111111111, "44": 0.05555555555555555, "13": 0.05555555555555555, "47": 0.05555555555555555, "17": 0.05555555555555555, "33": 0.05555555555555555, "39": 0.1111111111111111, "11": 0.05555555555555555, "45": 0.05555555555555555, "14": 0.05555555555555555, "35": 0.05555555555555555, "37": 0.05555555555555555, "43": 0.05555555555555555, "15": 0.05555555555555555, "31": 0.05555555555555555}, "8": {"33": 0.08333333333333333, "3": 0.08333333333333333, "4": 0.08333333333333333, "40": 0.08333333333333333, "47": 0.08333333333333333, "48": 0.08333333333333333, "5": 0.08333333333333333, "6": 0.08333333333333333, "41": 0.08333333333333333, "9": 0.08333333333333333, "46": 0.08333333333333333, "15": 0.08333333333333333}, "39": {"33": 0.041666666666666664, "39": 0.125, "10": 0.041666666666666664, "11": 0.041666666666666664, "45": 0.041666666666666664, "14": 0.08333333333333333, "35": 0.041666666666666664, "37": 0.041666666666666664, "43": 0.041666666666666664, "15": 0.041666666666666664, "31": 0.041666666666666664, "6": 0.041666666666666664, "8": 0.041666666666666664, "41": 0.08333333333333333, "47": 0.041666666666666664, "16": 0.041666666666666664, "19": 0.041666666666666664, "7": 0.041666666666666664, "9": 0.041666666666666664, "18": 0.041666666666666664}}, "second_order_transitions": {"((1, 3, 7, 10, 30, 45), (1, 4, 14, 44, 47, 49))": {"6": 1, "9": 1, "43": 1, "15": 1, "16": 1, "30": 1}, "((1, 4, 14, 44, 47, 49), (6, 9, 15, 16, 30, 43))": {"35": 1, "3": 1, "10": 1, "43": 1, "46": 1, "14": 1}, "((6, 9, 15, 16, 30, 43), (3, 10, 14, 35, 43, 46))": {"34": 1, "41": 1, "10": 1, "43": 1, "48": 1, "17": 1}, "((3, 10, 14, 35, 43, 46), (10, 17, 34, 41, 43, 48))": {"1": 1, "5": 1, "6": 1, "49": 1, "17": 1, "31": 1}, "((10, 17, 34, 41, 43, 48), (1, 5, 6, 17, 31, 49))": {"34": 1, "6": 1, "7": 1, "13": 1, "17": 1, "31": 1}, "((1, 5, 6, 17, 31, 49), (6, 7, 13, 17, 31, 34))": {"33": 1, "5": 1, "13": 1, "48": 1, "17": 1, "19": 1}, "((6, 7, 13, 17, 31, 34), (5, 13, 17, 19, 33, 48))": {"36": 1, "37": 1, "40": 1, "11": 1, "43": 1, "47": 1}, "((5, 13, 17, 19, 33, 48), (11, 36, 37, 40, 43, 47))": {"8": 1, "10": 1, "44": 1, "13": 1, "47": 1, "17": 1}, "((11, 36, 37, 40, 43, 47), (8, 10, 13, 17, 44, 47))": {"33": 1, "3": 1, "4": 1, "40": 1, "47": 1, "48": 1}, "((8, 10, 13, 17, 44, 47), (3, 4, 33, 40, 47, 48))": {"35": 1, "39": 1, "11": 1, "45": 1, "15": 1, "48": 1}, "((3, 4, 33, 40, 47, 48), (11, 15, 35, 39, 45, 48))": {"33": 1, "39": 1, "10": 1, "11": 1, "45": 1, "14": 1}, "((11, 15, 35, 39, 45, 48), (10, 11, 14, 33, 39, 45))": {"35": 1, "37": 1, "39": 1, "43": 1, "15": 1, "31": 1}, "((10, 11, 14, 33, 39, 45), (15, 31, 35, 37, 39, 43))": {"6": 1, "8": 1, "41": 1, "47": 1, "16": 1, "19": 1}, "((15, 31, 35, 37, 39, 43), (6, 8, 16, 19, 41, 47))": {"5": 1, "6": 1, "41": 1, "9": 1, "46": 1, "15": 1}, "((6, 8, 16, 19, 41, 47), (5, 6, 9, 15, 41, 46))": {"35": 1, "6": 1, "39": 1, "46": 1, "15": 1, "19": 1}, "((5, 6, 9, 15, 41, 46), (6, 15, 19, 35, 39, 46))": {"7": 1, "39": 1, "41": 1, "9": 1, "14": 1, "18": 1}}, "most_likely_transitions": {"1": "(6, 0.1111111111111111)", "3": "(48, 0.1111111111111111)", "7": "(1, 0.08333333333333333)", "10": "(1, 0.06666666666666667)", "45": "(14, 0.1111111111111111)", "30": "(14, 0.16666666666666666)", "4": "(15, 0.16666666666666666)", "44": "(6, 0.08333333333333333)", "14": "(43, 0.16666666666666666)", "47": "(15, 0.1)", "49": "(6, 0.16666666666666666)", "6": "(46, 0.08333333333333333)", "9": "(35, 0.16666666666666666)", "43": "(10, 0.1)", "15": "(14, 0.1)", "16": "(46, 0.16666666666666666)", "35": "(41, 0.125)", "46": "(41, 0.1111111111111111)", "34": "(5, 0.16666666666666666)", "41": "(6, 0.16666666666666666)", "48": "(11, 0.125)", "17": "(17, 0.1)", "5": "(6, 0.1111111111111111)", "31": "(6, 0.1111111111111111)", "13": "(33, 0.1111111111111111)", "33": "(37, 0.1111111111111111)", "19": "(41, 0.1111111111111111)", "36": "(8, 0.16666666666666666)", "37": "(8, 0.16666666666666666)", "40": "(8, 0.08333333333333333)", "11": "(10, 0.1111111111111111)", "8": "(33, 0.08333333333333333)", "39": "(39, 0.125)"}}, "time_series": {"seasonal_patterns": {"weekly": {"('sum', 'mean')": {"0": 109.0, "1": 163.0, "2": 120.5, "3": 154.0, "4": 149.29, "5": 128.0, "6": 177.67}, "('sum', 'std')": {"0": NaN, "1": 16.97, "2": 2.12, "3": 65.05, "4": 34.47, "5": NaN, "6": 31.47}, "('avg', 'mean')": {"0": 18.17, "1": 27.17, "2": 20.08, "3": 25.67, "4": 24.88, "5": 21.33, "6": 29.61}, "('avg', 'std')": {"0": NaN, "1": 2.83, "2": 0.35, "3": 10.84, "4": 5.75, "5": NaN, "6": 5.24}, "('even_count', 'mean')": {"0": 1.0, "1": 3.0, "2": 2.5, "3": 1.0, "4": 2.14, "5": 2.0, "6": 2.33}, "('odd_count', 'mean')": {"0": 5.0, "1": 3.0, "2": 3.5, "3": 5.0, "4": 3.86, "5": 4.0, "6": 3.67}}}, "trend_analysis": {"sum_trend_slope": 0.10763348262211055, "sum_trend_r_squared": 0.012254969336824554, "sum_trend_p_value": 0.6618939660915868, "trend_direction": "increasing"}, "rolling_statistics": [{"period": 11, "sum_rolling_mean": NaN, "avg_rolling_mean": NaN, "range_rolling_mean": NaN}, {"period": 20, "sum_rolling_mean": NaN, "avg_rolling_mean": NaN, "range_rolling_mean": NaN}, {"period": 23, "sum_rolling_mean": NaN, "avg_rolling_mean": NaN, "range_rolling_mean": NaN}, {"period": 29, "sum_rolling_mean": NaN, "avg_rolling_mean": NaN, "range_rolling_mean": NaN}, {"period": 39, "sum_rolling_mean": 143.6, "avg_rolling_mean": 23.93333333333333, "range_rolling_mean": 42.0}, {"period": 49, "sum_rolling_mean": 146.2, "avg_rolling_mean": 24.366666666666667, "range_rolling_mean": 42.8}, {"period": 52, "sum_rolling_mean": 136.0, "avg_rolling_mean": 22.666666666666664, "range_rolling_mean": 38.8}, {"period": 53, "sum_rolling_mean": 139.2, "avg_rolling_mean": 23.2, "range_rolling_mean": 40.0}, {"period": 55, "sum_rolling_mean": 151.8, "avg_rolling_mean": 25.3, "range_rolling_mean": 38.6}, {"period": 60, "sum_rolling_mean": 141.0, "avg_rolling_mean": 23.500000000000004, "range_rolling_mean": 38.8}, {"period": 64, "sum_rolling_mean": 154.2, "avg_rolling_mean": 25.7, "range_rolling_mean": 38.2}, {"period": 67, "sum_rolling_mean": 171.2, "avg_rolling_mean": 28.53333333333334, "range_rolling_mean": 40.0}, {"period": 88, "sum_rolling_mean": 174.6, "avg_rolling_mean": 29.1, "range_rolling_mean": 38.4}, {"period": 94, "sum_rolling_mean": 171.8, "avg_rolling_mean": 28.633333333333336, "range_rolling_mean": 36.8}, {"period": 109, "sum_rolling_mean": 171.4, "avg_rolling_mean": 28.56666666666667, "range_rolling_mean": 37.2}, {"period": 114, "sum_rolling_mean": 160.8, "avg_rolling_mean": 26.8, "range_rolling_mean": 36.4}, {"period": 118, "sum_rolling_mean": 154.2, "avg_rolling_mean": 25.7, "range_rolling_mean": 37.0}, {"period": 124, "sum_rolling_mean": 149.4, "avg_rolling_mean": 24.9, "range_rolling_mean": 36.8}]}, "machine_learning": {"sum": {"RandomForest": {"mse": 2682.0122749999996, "mae": 44.0425, "rmse": 51.78814801670359, "cv_score_mean": 1290.1080111111112, "cv_score_std": 354.14835490879364}, "GradientBoosting": {"mse": 4075.4931830566134, "mae": 58.70972061224192, "rmse": 63.83958946497552, "cv_score_mean": 1077.7432730752555, "cv_score_std": 320.948585926276}, "LinearRegression": {"mse": 2012.0211081887091, "mae": 37.99413798981837, "rmse": 44.855558275298606, "cv_score_mean": 1756.59323813408, "cv_score_std": 1603.9359585488155}}, "avg": {"RandomForest": {"mse": 75.22789097222208, "mae": 7.4070833333333255, "rmse": 8.673401349656435, "cv_score_mean": 36.233302314814814, "cv_score_std": 8.857419724377458}, "GradientBoosting": {"mse": 113.20814397379523, "mae": 9.784953435373666, "rmse": 10.63993157749594, "cv_score_mean": 29.937313140979285, "cv_score_std": 8.915238497952139}, "LinearRegression": {"mse": 55.889475227464175, "mae": 6.332356331636396, "rmse": 7.475926379216436, "cv_score_mean": 51.21070365789107, "cv_score_std": 48.40535513380392}}, "range": {"RandomForest": {"mse": 51.6189, "mae": 6.75, "rmse": 7.1846294267693445, "cv_score_mean": 24.972538888888888, "cv_score_std": 21.938933213853254}, "GradientBoosting": {"mse": 86.25840056942596, "mae": 9.280413312589799, "rmse": 9.28754007094591, "cv_score_mean": 33.94878488528893, "cv_score_std": 13.183946585998191}, "LinearRegression": {"mse": 194.39898093387512, "mae": 11.591294017026325, "rmse": 13.942703501612415, "cv_score_mean": 201.38882839899267, "cv_score_std": 153.93328749244}}, "even_count": {"RandomForest": {"mse": 1.1471, "mae": 0.7750000000000001, "rmse": 1.0710275439968853, "cv_score_mean": 0.945961111111111, "cv_score_std": 0.3747025858457693}, "GradientBoosting": {"mse": 1.6722729250655772, "mae": 0.956646307166583, "rmse": 1.2931639204159608, "cv_score_mean": 0.5473972052500687, "cv_score_std": 0.21388700712438666}, "LinearRegression": {"mse": 2.918233363911928, "mae": 1.3561098348028442, "rmse": 1.7082837480676119, "cv_score_mean": 4.954675127285189, "cv_score_std": 2.8159640325282846}}}, "patterns": {"consecutive": {"average_consecutive": 0.7777777777777778, "max_consecutive_seen": 2, "consecutive_distribution": {"0": 11, "2": 7}}, "sum_ranges": {"low": 1, "medium": 8, "high": 9}, "even_odd": {"2E4O": 7, "3E3O": 7, "1E5O": 3, "0E6O": 1}, "gaps": {"average_gap": 7.833333333333333, "gap_distribution": {"4": 14, "3": 14, "2": 10, "1": 8, "6": 7, "7": 6, "14": 4, "8": 3, "5": 3, "20": 2}}}}