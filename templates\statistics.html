{% extends "base.html" %}
{% block content %}
<h1>📈 Statistics</h1>
<div class="row">
    <div class="col-md-4">
        <div class="chart-container">
            <h5>Number Frequency</h5>
            <div id="numberChart"></div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="chart-container">
            <h5>Animal Distribution</h5>
            <div id="animalChart"></div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="chart-container">
            <h5>Element Distribution</h5>
            <div id="elementChart"></div>
        </div>
    </div>
</div>
{% endblock %}
{% block scripts %}
<script>
// Load charts
fetch('/api/chart/number_frequency')
    .then(response => response.json())
    .then(data => {
        if (data.image) {
            document.getElementById('numberChart').innerHTML = '<img src="' + data.image + '" class="img-fluid">';
        }
    });

fetch('/api/chart/animal_distribution')
    .then(response => response.json())
    .then(data => {
        if (data.image) {
            document.getElementById('animalChart').innerHTML = '<img src="' + data.image + '" class="img-fluid">';
        }
    });

fetch('/api/chart/element_distribution')
    .then(response => response.json())
    .then(data => {
        if (data.image) {
            document.getElementById('elementChart').innerHTML = '<img src="' + data.image + '" class="img-fluid">';
        }
    });
</script>
{% endblock %}