#!/usr/bin/env python3
"""
Generate HTML Table for Lottery Data Display.
"""

import os
import json
import csv
from datetime import datetime

class HTMLTableGenerator:
    """Generate HTML table for lottery data display."""
    
    def __init__(self):
        """Initialize the HTML generator."""
        self.data = []
        self.predictions = None
        self.load_data()
        self.load_predictions()
    
    def load_data(self):
        """Load lottery data from CSV file."""
        try:
            data_file = "output/lottery_data_20250704_181955.csv"
            if os.path.exists(data_file):
                with open(data_file, 'r', encoding='utf-8') as f:
                    reader = csv.DictReader(f)
                    for row in reader:
                        self.data.append(row)
                print(f"✅ Loaded {len(self.data)} lottery records")
            else:
                print("⚠️ No lottery data file found")
                self.data = []
        except Exception as e:
            print(f"❌ Error loading data: {e}")
            self.data = []
    
    def load_predictions(self):
        """Load latest predictions."""
        try:
            predictions_dir = "output/predictions"
            if os.path.exists(predictions_dir):
                # Find latest prediction file
                prediction_files = [f for f in os.listdir(predictions_dir) 
                                  if f.startswith('prediction_report_') and f.endswith('.json')]
                if prediction_files:
                    latest_file = sorted(prediction_files)[-1]
                    with open(os.path.join(predictions_dir, latest_file), 'r', encoding='utf-8') as f:
                        self.predictions = json.load(f)
                    print(f"✅ Loaded predictions from {latest_file}")
                else:
                    print("⚠️ No prediction files found")
                    self.predictions = None
            else:
                print("⚠️ No predictions directory found")
                self.predictions = None
        except Exception as e:
            print(f"❌ Error loading predictions: {e}")
            self.predictions = None
    
    def format_lottery_data(self):
        """Format lottery data for table display."""
        try:
            if not self.data:
                return []
            
            formatted_data = []
            for row in self.data:
                # Extract period number
                period_str = str(row.get('period', row.get('期数', '')))
                period_num = ''.join(filter(str.isdigit, period_str)) if period_str else '0'
                
                # Extract date
                date_str = str(row.get('date', row.get('日期', '')))
                
                # Extract numbers
                numbers = []
                for i in range(1, 8):
                    num_col = f'number_{i}'
                    if num_col in row and row[num_col] and row[num_col].strip():
                        try:
                            numbers.append(f"{int(float(row[num_col])):02d}")
                        except (ValueError, TypeError):
                            continue
                
                # Format regular numbers and special number
                if len(numbers) >= 6:
                    regular_numbers = ', '.join(numbers[:6])
                    special_number = numbers[6] if len(numbers) > 6 else numbers[-1]
                else:
                    regular_numbers = "数据不完整"
                    special_number = "N/A"
                
                formatted_data.append({
                    'period': f"{period_num}期",
                    'date': date_str,
                    'regular_numbers': regular_numbers,
                    'special_number': special_number
                })
            
            # Sort by period number (descending - newest first)
            formatted_data.sort(key=lambda x: int(x['period'].replace('期', '')), reverse=True)
            
            return formatted_data
            
        except Exception as e:
            print(f"❌ Error formatting data: {e}")
            return []
    
    def format_prediction_data(self):
        """Format prediction data for display."""
        try:
            if not self.predictions:
                return None
            
            ensemble = self.predictions.get('ensemble_prediction', {})
            regular_numbers = ensemble.get('regular_numbers', [])
            special_number = ensemble.get('special_number', 0)
            confidence = ensemble.get('confidence_score', 0)
            
            # Format numbers with leading zeros
            formatted_regular = ', '.join([f"{num:02d}" for num in regular_numbers])
            formatted_special = f"{special_number:02d}"
            
            prediction_data = {
                'period': f"{self.predictions.get('next_period', 'N/A')}期",
                'date': self.predictions.get('prediction_date', 'N/A'),
                'regular_numbers': formatted_regular,
                'special_number': formatted_special,
                'confidence': f"{confidence:.1%}"
            }
            
            return prediction_data
            
        except Exception as e:
            print(f"❌ Error formatting predictions: {e}")
            return None
    
    def generate_html(self):
        """Generate complete HTML page."""
        try:
            # Get formatted data
            lottery_data = self.format_lottery_data()
            prediction_data = self.format_prediction_data()
            
            # Calculate statistics
            total_records = len(lottery_data)
            latest_period = lottery_data[0]['period'] if lottery_data else "N/A"
            prediction_confidence = prediction_data['confidence'] if prediction_data else "N/A"
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            
            # Generate HTML content
            html_content = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>彩票数据分析系统 - 表格显示</title>
    <style>
        body {{
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }}
        .container {{
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 30px;
        }}
        h1 {{
            text-align: center;
            color: #333;
            margin-bottom: 30px;
            font-size: 2.5em;
        }}
        h2 {{
            color: #444;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
            margin-top: 40px;
        }}
        .prediction-section {{
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
        }}
        .prediction-section h2 {{
            color: white;
            border-bottom: 2px solid white;
        }}
        .prediction-highlight {{
            font-size: 1.5em;
            font-weight: bold;
            text-align: center;
            margin: 20px 0;
            padding: 15px;
            background-color: rgba(255,255,255,0.2);
            border-radius: 8px;
        }}
        table {{
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            font-size: 14px;
        }}
        th, td {{
            border: 1px solid #ddd;
            padding: 12px;
            text-align: center;
        }}
        th {{
            background-color: #007bff;
            color: white;
            font-weight: bold;
            position: sticky;
            top: 0;
        }}
        tr:nth-child(even) {{
            background-color: #f9f9f9;
        }}
        tr:hover {{
            background-color: #e3f2fd;
        }}
        .period-col {{
            font-weight: bold;
            color: #007bff;
        }}
        .numbers-col {{
            font-family: 'Courier New', monospace;
            font-weight: bold;
            color: #d32f2f;
        }}
        .special-col {{
            font-family: 'Courier New', monospace;
            font-weight: bold;
            color: #ff6f00;
            background-color: #fff3e0;
        }}
        .confidence {{
            color: #4caf50;
            font-weight: bold;
        }}
        .stats {{
            display: flex;
            justify-content: space-around;
            margin: 20px 0;
            padding: 20px;
            background-color: #f8f9fa;
            border-radius: 8px;
        }}
        .stat-item {{
            text-align: center;
        }}
        .stat-number {{
            font-size: 2em;
            font-weight: bold;
            color: #007bff;
        }}
        .stat-label {{
            color: #666;
            margin-top: 5px;
        }}
        .timestamp {{
            text-align: center;
            color: #666;
            font-size: 0.9em;
            margin-top: 20px;
        }}
        .refresh-note {{
            text-align: center;
            color: #666;
            font-style: italic;
            margin: 10px 0;
        }}
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 彩票数据分析系统</h1>
        
        <div class="stats">
            <div class="stat-item">
                <div class="stat-number">{total_records}</div>
                <div class="stat-label">历史记录</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">{latest_period}</div>
                <div class="stat-label">最新期数</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">{prediction_confidence}</div>
                <div class="stat-label">预测信心度</div>
            </div>
        </div>
"""
            
            # Add prediction section if available
            if prediction_data:
                html_content += f"""
        <div class="prediction-section">
            <h2>🔮 下期预测</h2>
            <div class="prediction-highlight">
                <div>{prediction_data['period']} | {prediction_data['date']}</div>
                <div style="margin: 10px 0;">
                    开奖号码(平码): {prediction_data['regular_numbers']} | 
                    特别号码(特码): {prediction_data['special_number']}
                </div>
                <div class="confidence">预测信心度: {prediction_data['confidence']}</div>
            </div>
        </div>
"""
            
            # Add historical data table
            html_content += """
        <h2>📊 历史开奖记录</h2>
        <div class="refresh-note">💡 要刷新数据，请重新运行 generate_html_table.py</div>
        
        <table>
            <thead>
                <tr>
                    <th>期数</th>
                    <th>日期</th>
                    <th>开奖号码(平码)</th>
                    <th>特别号码(特码)</th>
                </tr>
            </thead>
            <tbody>
"""
            
            # Add table rows
            for record in lottery_data:
                html_content += f"""
                <tr>
                    <td class="period-col">{record['period']}</td>
                    <td>{record['date']}</td>
                    <td class="numbers-col">{record['regular_numbers']}</td>
                    <td class="special-col">{record['special_number']}</td>
                </tr>
"""
            
            # Close HTML
            html_content += f"""
            </tbody>
        </table>
        
        <div class="timestamp">
            最后更新时间: {current_time}
        </div>
    </div>
</body>
</html>
"""
            
            return html_content
            
        except Exception as e:
            print(f"❌ Error generating HTML: {e}")
            return f"<h1>Error: {e}</h1>"
    
    def save_html(self, filename="lottery_table.html"):
        """Save HTML to file."""
        try:
            html_content = self.generate_html()
            
            # Create output directory
            os.makedirs('output/html', exist_ok=True)
            
            # Save HTML file
            html_file = os.path.join('output/html', filename)
            with open(html_file, 'w', encoding='utf-8') as f:
                f.write(html_content)
            
            print(f"✅ HTML table saved to: {html_file}")
            return html_file
            
        except Exception as e:
            print(f"❌ Error saving HTML: {e}")
            return None

def main():
    """Main function to generate HTML table."""
    try:
        print("🚀 Generating HTML table for lottery data...")
        
        # Create generator
        generator = HTMLTableGenerator()
        
        # Generate and save HTML
        html_file = generator.save_html()
        
        if html_file:
            print(f"🌐 Open the following file in your browser:")
            print(f"   📄 {os.path.abspath(html_file)}")
            
            # Display summary
            lottery_data = generator.format_lottery_data()
            prediction_data = generator.format_prediction_data()
            
            print(f"\n📊 数据摘要:")
            print(f"   📈 历史记录: {len(lottery_data)}期")
            
            if prediction_data:
                print(f"   🔮 下期预测: {prediction_data['period']} - {prediction_data['regular_numbers']} | {prediction_data['special_number']}")
                print(f"   🎯 预测信心度: {prediction_data['confidence']}")
            else:
                print("   ⚠️ 无预测数据")
        else:
            print("❌ Failed to generate HTML table!")
        
    except Exception as e:
        print(f"❌ Error in main: {e}")

if __name__ == "__main__":
    main()
