#!/usr/bin/env python3
"""
Simple test script to check website accessibility and data structure.
"""

import time
import os
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from webdriver_manager.chrome import ChromeDriverManager
from bs4 import BeautifulSoup

def test_website_access():
    """Test basic website access and content extraction."""
    url = "https://wid8-baidu630.gabd11133ff.com/pages/historyRecord/lotteryRecord/index?route=aomen&"
    
    print("🔍 Testing website access...")
    print(f"Target URL: {url}")
    
    # Setup Chrome options
    chrome_options = Options()
    chrome_options.add_argument("--headless=new")  # Use new headless mode
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--disable-gpu")
    chrome_options.add_argument("--window-size=1920,1080")
    chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
    
    driver = None
    try:
        # Setup WebDriver
        print("🚀 Setting up WebDriver...")
        # Use webdriver-manager to get the correct ChromeDriver
        print("📥 Setting up compatible ChromeDriver...")
        service = Service(ChromeDriverManager().install())
        driver = webdriver.Chrome(service=service, options=chrome_options)
        print("✅ ChromeDriver setup successful")
        driver.implicitly_wait(10)
        driver.set_page_load_timeout(60)
        
        # Load the page
        print("📄 Loading webpage...")
        driver.get(url)
        
        # Wait a bit for JavaScript to execute
        time.sleep(10)
        
        # Get page title
        title = driver.title
        print(f"📋 Page title: {title}")
        
        # Get page source
        page_source = driver.page_source
        print(f"📊 Page source length: {len(page_source)} characters")
        
        # Save page source for inspection
        os.makedirs("output", exist_ok=True)
        with open("output/test_page_source.html", "w", encoding="utf-8") as f:
            f.write(page_source)
        print("💾 Page source saved to output/test_page_source.html")
        
        # Parse with BeautifulSoup
        soup = BeautifulSoup(page_source, 'html.parser')
        
        # Look for common data structures
        print("\n🔍 Analyzing page structure...")
        
        # Check for tables
        tables = soup.find_all('table')
        print(f"📊 Found {len(tables)} table(s)")
        
        # Check for divs with potential data
        divs = soup.find_all('div')
        print(f"📦 Found {len(divs)} div element(s)")
        
        # Check for lists
        lists = soup.find_all(['ul', 'ol', 'li'])
        print(f"📝 Found {len(lists)} list element(s)")
        
        # Look for text content that might contain lottery data
        all_text = soup.get_text()
        with open("output/test_page_text.txt", "w", encoding="utf-8") as f:
            f.write(all_text)
        print("💾 Page text saved to output/test_page_text.txt")
        
        # Look for specific patterns
        lottery_keywords = ['期', '号', '开奖', '结果', '时间', 'lottery', 'result', 'draw']
        found_keywords = []
        for keyword in lottery_keywords:
            if keyword in all_text.lower():
                found_keywords.append(keyword)
        
        if found_keywords:
            print(f"🎯 Found lottery-related keywords: {', '.join(found_keywords)}")
        else:
            print("⚠️  No obvious lottery keywords found")
        
        # Try to find any structured data
        potential_data_elements = []
        
        # Look for elements with IDs or classes that might contain data
        for element in soup.find_all(['div', 'span', 'td', 'li']):
            element_id = element.get('id', '')
            element_class = ' '.join(element.get('class', []))
            element_text = element.get_text(strip=True)
            
            if element_text and len(element_text) > 5:
                if any(keyword in element_text.lower() for keyword in lottery_keywords):
                    potential_data_elements.append({
                        'tag': element.name,
                        'id': element_id,
                        'class': element_class,
                        'text': element_text[:100] + '...' if len(element_text) > 100 else element_text
                    })
        
        print(f"\n📋 Found {len(potential_data_elements)} potential data elements:")
        for i, elem in enumerate(potential_data_elements[:10]):  # Show first 10
            print(f"  {i+1}. {elem['tag']} - {elem['text']}")
        
        if len(potential_data_elements) > 10:
            print(f"  ... and {len(potential_data_elements) - 10} more")
        
        print("\n✅ Website access test completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Error during testing: {str(e)}")
        return False
        
    finally:
        if driver:
            driver.quit()
            print("🔒 WebDriver closed")

if __name__ == "__main__":
    print("🧪 Lottery Scraper - Website Access Test")
    print("=" * 50)
    
    success = test_website_access()
    
    if success:
        print("\n🎉 Test completed! Check the output directory for saved files.")
        print("📋 Next steps:")
        print("1. Review output/test_page_source.html to see the raw HTML")
        print("2. Review output/test_page_text.txt to see extracted text")
        print("3. Run the full scraper if data looks good")
    else:
        print("\n💥 Test failed. Check the error messages above.")
