#!/usr/bin/env python3
"""
Data Validator for Lottery Data - Advanced validation and deduplication.
"""

import os
import pandas as pd
import numpy as np
import re
import hashlib
from datetime import datetime, timedelta
from collections import Counter
import logging
from pathlib import Path

class LotteryDataValidator:
    """Advanced data validation and deduplication for lottery data."""
    
    def __init__(self):
        """Initialize the validator."""
        self.setup_logging()
        self.validation_rules = self.setup_validation_rules()
        self.duplicate_strategies = ['exact', 'fuzzy', 'semantic']
        
    def setup_logging(self):
        """Setup logging for validation operations."""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger('DataValidator')
    
    def setup_validation_rules(self):
        """Define validation rules for lottery data."""
        return {
            'date': {
                'required': True,
                'format': r'^\d{4}-\d{2}-\d{2}$',
                'range': {
                    'min': datetime(2020, 1, 1),
                    'max': datetime.now() + timedelta(days=1)
                }
            },
            'period': {
                'required': True,
                'format': r'^\d{3}期$',
                'range': {'min': 1, 'max': 999}
            },
            'numbers': {
                'required': True,
                'count': {'min': 5, 'max': 8},
                'value_range': {'min': 1, 'max': 49},
                'unique': True
            },
            'animals': {
                'valid_values': ['鼠', '牛', '虎', '兔', '龙', '蛇', '马', '羊', '猴', '鸡', '狗', '猪'],
                'required_with_numbers': True
            },
            'elements': {
                'valid_values': ['金', '木', '水', '火', '土'],
                'required_with_numbers': True
            }
        }
    
    def validate_record(self, record):
        """Validate a single lottery record."""
        errors = []
        warnings = []
        
        # Validate date
        date_errors = self._validate_date(record.get('date'))
        errors.extend(date_errors)
        
        # Validate period
        period_errors = self._validate_period(record.get('period'))
        errors.extend(period_errors)
        
        # Validate numbers
        number_errors, number_warnings = self._validate_numbers(record)
        errors.extend(number_errors)
        warnings.extend(number_warnings)
        
        # Validate animals and elements
        animal_errors = self._validate_animals(record)
        errors.extend(animal_errors)
        
        element_errors = self._validate_elements(record)
        errors.extend(element_errors)
        
        # Cross-validation
        cross_errors = self._cross_validate(record)
        errors.extend(cross_errors)
        
        return {
            'valid': len(errors) == 0,
            'errors': errors,
            'warnings': warnings,
            'score': self._calculate_quality_score(record, errors, warnings)
        }
    
    def _validate_date(self, date_str):
        """Validate date field."""
        errors = []
        
        if not date_str:
            errors.append("Date is required")
            return errors
        
        # Check format
        if not re.match(self.validation_rules['date']['format'], str(date_str)):
            errors.append(f"Invalid date format: {date_str}")
            return errors
        
        try:
            date_obj = datetime.strptime(str(date_str), '%Y-%m-%d')
            
            # Check range
            min_date = self.validation_rules['date']['range']['min']
            max_date = self.validation_rules['date']['range']['max']
            
            if date_obj < min_date or date_obj > max_date:
                errors.append(f"Date out of valid range: {date_str}")
                
        except ValueError:
            errors.append(f"Invalid date value: {date_str}")
        
        return errors
    
    def _validate_period(self, period_str):
        """Validate period field."""
        errors = []
        
        if not period_str:
            errors.append("Period is required")
            return errors
        
        # Check format
        if not re.match(self.validation_rules['period']['format'], str(period_str)):
            errors.append(f"Invalid period format: {period_str}")
            return errors
        
        try:
            # Extract number from period (e.g., "123期" -> 123)
            period_num = int(re.findall(r'\d+', str(period_str))[0])
            
            min_period = self.validation_rules['period']['range']['min']
            max_period = self.validation_rules['period']['range']['max']
            
            if period_num < min_period or period_num > max_period:
                errors.append(f"Period number out of range: {period_num}")
                
        except (ValueError, IndexError):
            errors.append(f"Invalid period value: {period_str}")
        
        return errors
    
    def _validate_numbers(self, record):
        """Validate lottery numbers."""
        errors = []
        warnings = []
        
        # Extract numbers from record
        numbers = []
        for i in range(1, 8):
            num_key = f'number_{i}'
            if num_key in record and pd.notna(record[num_key]):
                try:
                    numbers.append(int(record[num_key]))
                except (ValueError, TypeError):
                    errors.append(f"Invalid number format in {num_key}: {record[num_key]}")
        
        # Check count
        min_count = self.validation_rules['numbers']['count']['min']
        max_count = self.validation_rules['numbers']['count']['max']
        
        if len(numbers) < min_count:
            errors.append(f"Too few numbers: {len(numbers)} (minimum: {min_count})")
        elif len(numbers) > max_count:
            warnings.append(f"Many numbers: {len(numbers)} (typical: {min_count}-{max_count})")
        
        # Check value range
        min_val = self.validation_rules['numbers']['value_range']['min']
        max_val = self.validation_rules['numbers']['value_range']['max']
        
        for num in numbers:
            if num < min_val or num > max_val:
                errors.append(f"Number out of range: {num} (valid: {min_val}-{max_val})")
        
        # Check uniqueness
        if len(numbers) != len(set(numbers)):
            duplicates = [num for num, count in Counter(numbers).items() if count > 1]
            errors.append(f"Duplicate numbers found: {duplicates}")
        
        return errors, warnings
    
    def _validate_animals(self, record):
        """Validate animal fields."""
        errors = []
        valid_animals = self.validation_rules['animals']['valid_values']
        
        for i in range(1, 8):
            animal_key = f'animal_{i}'
            if animal_key in record and pd.notna(record[animal_key]):
                animal = str(record[animal_key]).strip()
                if animal and animal not in valid_animals:
                    errors.append(f"Invalid animal in {animal_key}: {animal}")
        
        return errors
    
    def _validate_elements(self, record):
        """Validate element fields."""
        errors = []
        valid_elements = self.validation_rules['elements']['valid_values']
        
        for i in range(1, 8):
            element_key = f'element_{i}'
            if element_key in record and pd.notna(record[element_key]):
                element = str(record[element_key]).strip()
                if element and element not in valid_elements:
                    errors.append(f"Invalid element in {element_key}: {element}")
        
        return errors
    
    def _cross_validate(self, record):
        """Perform cross-field validation."""
        errors = []
        
        # Check if numbers have corresponding animals and elements
        for i in range(1, 8):
            num_key = f'number_{i}'
            animal_key = f'animal_{i}'
            element_key = f'element_{i}'
            
            has_number = num_key in record and pd.notna(record[num_key])
            has_animal = animal_key in record and pd.notna(record[animal_key])
            has_element = element_key in record and pd.notna(record[element_key])
            
            if has_number and not (has_animal and has_element):
                errors.append(f"Number {i} missing corresponding animal or element")
        
        return errors
    
    def _calculate_quality_score(self, record, errors, warnings):
        """Calculate data quality score (0-100)."""
        base_score = 100
        
        # Deduct points for errors and warnings
        error_penalty = len(errors) * 10
        warning_penalty = len(warnings) * 2
        
        # Bonus for completeness
        completeness_bonus = 0
        total_fields = 0
        filled_fields = 0
        
        for key, value in record.items():
            if key.startswith(('number_', 'animal_', 'element_')):
                total_fields += 1
                if pd.notna(value) and str(value).strip():
                    filled_fields += 1
        
        if total_fields > 0:
            completeness_ratio = filled_fields / total_fields
            completeness_bonus = completeness_ratio * 10
        
        final_score = max(0, base_score - error_penalty - warning_penalty + completeness_bonus)
        return min(100, final_score)
    
    def validate_dataset(self, data):
        """Validate entire dataset."""
        if isinstance(data, str):
            # Load from file
            if data.endswith('.csv'):
                df = pd.read_csv(data)
            elif data.endswith('.json'):
                df = pd.read_json(data)
            else:
                raise ValueError(f"Unsupported file format: {data}")
        else:
            df = data.copy()
        
        validation_results = []
        
        self.logger.info(f"🔍 Validating {len(df)} records...")
        
        for idx, record in df.iterrows():
            result = self.validate_record(record)
            result['index'] = idx
            validation_results.append(result)
        
        # Summary statistics
        valid_count = sum(1 for r in validation_results if r['valid'])
        invalid_count = len(validation_results) - valid_count
        avg_score = np.mean([r['score'] for r in validation_results])
        
        summary = {
            'total_records': len(validation_results),
            'valid_records': valid_count,
            'invalid_records': invalid_count,
            'validation_rate': valid_count / len(validation_results) * 100,
            'average_quality_score': avg_score,
            'results': validation_results
        }
        
        self.logger.info(f"✅ Validation completed: {valid_count}/{len(validation_results)} valid ({summary['validation_rate']:.1f}%)")
        
        return summary
    
    def detect_duplicates(self, data, strategy='exact'):
        """Detect duplicate records using different strategies."""
        if isinstance(data, str):
            if data.endswith('.csv'):
                df = pd.read_csv(data)
            elif data.endswith('.json'):
                df = pd.read_json(data)
            else:
                raise ValueError(f"Unsupported file format: {data}")
        else:
            df = data.copy()
        
        self.logger.info(f"🔍 Detecting duplicates using '{strategy}' strategy...")
        
        if strategy == 'exact':
            return self._detect_exact_duplicates(df)
        elif strategy == 'fuzzy':
            return self._detect_fuzzy_duplicates(df)
        elif strategy == 'semantic':
            return self._detect_semantic_duplicates(df)
        else:
            raise ValueError(f"Unknown duplicate detection strategy: {strategy}")
    
    def _detect_exact_duplicates(self, df):
        """Detect exact duplicates."""
        # Create composite key for comparison
        df['_composite_key'] = df.apply(lambda row: self._create_composite_key(row), axis=1)
        
        # Find duplicates
        duplicate_mask = df.duplicated(subset=['_composite_key'], keep=False)
        duplicates = df[duplicate_mask].copy()
        
        # Group duplicates
        duplicate_groups = []
        for key, group in duplicates.groupby('_composite_key'):
            if len(group) > 1:
                duplicate_groups.append({
                    'key': key,
                    'count': len(group),
                    'indices': group.index.tolist(),
                    'records': group.drop('_composite_key', axis=1).to_dict('records')
                })
        
        return {
            'strategy': 'exact',
            'total_records': len(df),
            'duplicate_records': len(duplicates),
            'duplicate_groups': len(duplicate_groups),
            'groups': duplicate_groups
        }
    
    def _detect_fuzzy_duplicates(self, df):
        """Detect fuzzy duplicates (similar but not identical)."""
        # This would implement fuzzy matching logic
        # For now, return a placeholder
        return {
            'strategy': 'fuzzy',
            'total_records': len(df),
            'duplicate_records': 0,
            'duplicate_groups': 0,
            'groups': []
        }
    
    def _detect_semantic_duplicates(self, df):
        """Detect semantic duplicates (same meaning, different format)."""
        # This would implement semantic matching logic
        # For now, return a placeholder
        return {
            'strategy': 'semantic',
            'total_records': len(df),
            'duplicate_records': 0,
            'duplicate_groups': 0,
            'groups': []
        }
    
    def _create_composite_key(self, record):
        """Create composite key for duplicate detection."""
        key_parts = []
        
        # Add date and period
        key_parts.append(str(record.get('date', '')))
        key_parts.append(str(record.get('period', '')))
        
        # Add numbers (sorted to handle order variations)
        numbers = []
        for i in range(1, 8):
            num_key = f'number_{i}'
            if num_key in record and pd.notna(record[num_key]):
                numbers.append(str(record[num_key]))
        
        key_parts.append('|'.join(sorted(numbers)))
        
        # Create hash for consistent key length
        composite_str = '::'.join(key_parts)
        return hashlib.md5(composite_str.encode()).hexdigest()
    
    def remove_duplicates(self, data, strategy='exact', keep='first'):
        """Remove duplicates from dataset."""
        if isinstance(data, str):
            if data.endswith('.csv'):
                df = pd.read_csv(data)
            elif data.endswith('.json'):
                df = pd.read_json(data)
            else:
                raise ValueError(f"Unsupported file format: {data}")
        else:
            df = data.copy()
        
        original_count = len(df)
        
        if strategy == 'exact':
            df['_composite_key'] = df.apply(lambda row: self._create_composite_key(row), axis=1)
            df_clean = df.drop_duplicates(subset=['_composite_key'], keep=keep)
            df_clean = df_clean.drop('_composite_key', axis=1)
        else:
            # For other strategies, implement specific logic
            df_clean = df.copy()
        
        removed_count = original_count - len(df_clean)
        
        self.logger.info(f"🧹 Removed {removed_count} duplicate records ({removed_count/original_count*100:.1f}%)")
        
        return df_clean, {
            'original_count': original_count,
            'final_count': len(df_clean),
            'removed_count': removed_count,
            'removal_rate': removed_count / original_count * 100
        }
    
    def generate_validation_report(self, validation_results, output_file=None):
        """Generate comprehensive validation report."""
        if not output_file:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = f"output/validation_report_{timestamp}.json"
        
        # Create detailed report
        report = {
            'timestamp': datetime.now().isoformat(),
            'summary': {
                'total_records': validation_results['total_records'],
                'valid_records': validation_results['valid_records'],
                'invalid_records': validation_results['invalid_records'],
                'validation_rate': validation_results['validation_rate'],
                'average_quality_score': validation_results['average_quality_score']
            },
            'error_analysis': self._analyze_errors(validation_results['results']),
            'quality_distribution': self._analyze_quality_distribution(validation_results['results']),
            'recommendations': self._generate_recommendations(validation_results['results'])
        }
        
        # Save report
        os.makedirs(os.path.dirname(output_file), exist_ok=True)
        with open(output_file, 'w', encoding='utf-8') as f:
            import json
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        self.logger.info(f"📊 Validation report saved: {output_file}")
        return report
    
    def _analyze_errors(self, results):
        """Analyze error patterns."""
        error_counts = Counter()
        
        for result in results:
            for error in result['errors']:
                error_counts[error] += 1
        
        return dict(error_counts.most_common())
    
    def _analyze_quality_distribution(self, results):
        """Analyze quality score distribution."""
        scores = [r['score'] for r in results]
        
        return {
            'min': min(scores),
            'max': max(scores),
            'mean': np.mean(scores),
            'median': np.median(scores),
            'std': np.std(scores),
            'quartiles': {
                'q1': np.percentile(scores, 25),
                'q2': np.percentile(scores, 50),
                'q3': np.percentile(scores, 75)
            }
        }
    
    def _generate_recommendations(self, results):
        """Generate improvement recommendations."""
        recommendations = []
        
        # Analyze common issues
        error_counts = Counter()
        for result in results:
            for error in result['errors']:
                error_counts[error] += 1
        
        # Generate specific recommendations
        if error_counts:
            most_common_error = error_counts.most_common(1)[0]
            recommendations.append(f"Most common issue: {most_common_error[0]} ({most_common_error[1]} occurrences)")
        
        # Quality-based recommendations
        low_quality_count = sum(1 for r in results if r['score'] < 70)
        if low_quality_count > 0:
            recommendations.append(f"Consider reviewing {low_quality_count} records with quality scores below 70")
        
        return recommendations

def main():
    """Main function for command-line usage."""
    import sys
    
    if len(sys.argv) < 3:
        print("Usage:")
        print("  python data_validator.py validate <file>     # Validate data file")
        print("  python data_validator.py duplicates <file>   # Detect duplicates")
        print("  python data_validator.py clean <file>        # Remove duplicates")
        return
    
    command = sys.argv[1]
    data_file = sys.argv[2]
    
    validator = LotteryDataValidator()
    
    if command == 'validate':
        results = validator.validate_dataset(data_file)
        report = validator.generate_validation_report(results)
        print(f"✅ Validation completed. Report saved.")
        
    elif command == 'duplicates':
        duplicates = validator.detect_duplicates(data_file)
        print(f"🔍 Found {duplicates['duplicate_groups']} duplicate groups")
        
    elif command == 'clean':
        clean_data, stats = validator.remove_duplicates(data_file)
        output_file = data_file.replace('.csv', '_clean.csv')
        clean_data.to_csv(output_file, index=False)
        print(f"🧹 Cleaned data saved to: {output_file}")
        print(f"   Removed: {stats['removed_count']} records ({stats['removal_rate']:.1f}%)")
    
    else:
        print(f"Unknown command: {command}")

if __name__ == "__main__":
    main()
