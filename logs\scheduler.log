2025-07-04 18:32:53,418 - LotteryScheduler - INFO - 🕐 Lottery Scheduler initialized
2025-07-04 18:33:03,347 - LotteryScheduler - INFO - 🕐 Lottery Scheduler initialized
2025-07-04 18:33:03,348 - apscheduler.scheduler - INFO - Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-07-04 18:33:03,348 - LotteryScheduler - INFO - 📅 Scraping job scheduled: 0 */2 * * *
2025-07-04 18:33:03,348 - apscheduler.scheduler - INFO - Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-07-04 18:33:03,348 - LotteryScheduler - INFO - 📅 Analysis job scheduled: daily at 23:30
2025-07-04 18:33:03,349 - apscheduler.scheduler - INFO - Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-07-04 18:33:03,349 - LotteryScheduler - INFO - 📅 Cleanup job scheduled: weekly on Sunday 2:00 AM
2025-07-04 18:33:03,350 - apscheduler.scheduler - INFO - Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-07-04 18:33:03,350 - LotteryScheduler - INFO - 📅 Backup job scheduled: every 7 days
2025-07-04 18:33:03,371 - LotteryScheduler - ERROR - ❌ Error starting scheduler: Schedulers cannot be serialized. Ensure that you are not passing a scheduler instance as an argument to a job, or scheduling an instance method where the instance contains a scheduler as an attribute.
2025-07-04 18:33:31,288 - LotteryScheduler - INFO - 🕐 Lottery Scheduler initialized
2025-07-04 18:33:31,289 - apscheduler.scheduler - INFO - Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-07-04 18:33:31,290 - LotteryScheduler - INFO - 📅 Scraping job scheduled: 0 */2 * * *
2025-07-04 18:33:31,290 - apscheduler.scheduler - INFO - Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-07-04 18:33:31,290 - LotteryScheduler - INFO - 📅 Analysis job scheduled: daily at 23:30
2025-07-04 18:33:31,291 - apscheduler.scheduler - INFO - Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-07-04 18:33:31,291 - LotteryScheduler - INFO - 📅 Cleanup job scheduled: weekly on Sunday 2:00 AM
2025-07-04 18:33:31,291 - apscheduler.scheduler - INFO - Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-07-04 18:33:31,292 - LotteryScheduler - INFO - 📅 Backup job scheduled: every 7 days
2025-07-04 18:33:31,292 - apscheduler.scheduler - INFO - Added job "LotteryScheduler.scheduled_scraping_job" to job store "default"
2025-07-04 18:33:31,292 - apscheduler.scheduler - INFO - Added job "LotteryScheduler.scheduled_analysis_job" to job store "default"
2025-07-04 18:33:31,293 - apscheduler.scheduler - INFO - Added job "LotteryScheduler.scheduled_cleanup_job" to job store "default"
2025-07-04 18:33:31,293 - apscheduler.scheduler - INFO - Added job "LotteryScheduler.scheduled_backup_job" to job store "default"
2025-07-04 18:33:31,293 - apscheduler.scheduler - INFO - Scheduler started
2025-07-04 18:33:31,294 - LotteryScheduler - INFO - 🚀 Scheduler started successfully
2025-07-04 18:33:31,294 - LotteryScheduler - INFO - 📋 Active jobs:
2025-07-04 18:33:31,294 - LotteryScheduler - INFO -   - scraping_job: 2025-07-04 20:00:00+08:00
2025-07-04 18:33:31,294 - LotteryScheduler - INFO -   - analysis_job: 2025-07-04 23:30:00+08:00
2025-07-04 18:33:31,294 - LotteryScheduler - INFO -   - cleanup_job: 2025-07-07 02:00:00+08:00
2025-07-04 18:33:31,295 - LotteryScheduler - INFO -   - backup_job: 2025-07-11 18:33:31.291585+08:00
2025-07-04 18:33:41,296 - apscheduler.scheduler - INFO - Scheduler has been shut down
2025-07-04 18:33:41,296 - LotteryScheduler - INFO - 🛑 Scheduler stopped
